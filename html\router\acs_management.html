<a href='#' class='help' onclick="getHelp('WirelessSettings')">&nbsp;</a>
 <label id="acstitle" class="title"></label>  

<div id='Acs_Enable_div'>    
	<label id='lAcsEnable'></label>   
	<select id='sAcsEnable'>
		<option id='oAcsDisable' value='0'></option>
		<option id='oAcsEnable' value='1'>Enabled</option>
	</select>
</div>

<div id='Acs_Management_div' style='display: none'>

    <label id='lTr069AcsUrl'></label>
    <input type='text' name='Url'   value='' id='tr069AcsUrl' class='textfield'/>

    <label id='lTr069AcsUsername'></label>
    <input type='text' name='Username'   value='' id='tr069AcsUsername' class='textfield'/>

    <label id='lTr069AcsPassword'></label>
    <input type='password' name='Password'   value='' id='tr069AcsPassword'  onchange='pswChanged()' onkeypress='pswChanged()' class='textfield'/>

    <label id='lReAcsPassword'  style='display: none'>Re-enter Password:</label>
    <input type='password' value='' id='tbacsreenter_password' maxlength="32" style='display: none'  class='textfield'/>
    
        <label class='error' id='lPassErrorMes'  style='display: none'>Password do not match</label>

    <label id='lTr069Inform'></label>
    
    <select id='lTr069InformEnable'  onchange='InformStatusChanged()'>
	    <option id='lInformEnabled' value='1'></option>
	    <option id='lInformDisabled' value='0'></option>
     </select>
    
    <label id='lTr069InformInterval'></label>
    <input type='text' name='Informinterval'   value='' id='tr069InformInterval' class='textfield'/>

    <label id='lTr069ConnName'></label>
    <input type='text' name='Connname'   value='' id='tr069ConnName' class='textfield'/>

    <label id='lTr069ConnPassword'></label>
    <input type='text' name='Connpsw'   value='' id='tr069ConnPassword' class='textfield'/>
    
    <div  style="display: none">
    <input type='text'   value='' id='tr069SecretMode' class='textfield'/>
    <input type='text'   value='' id='acsUsername' class='textfield'/>
    <input type='text'   value='' id='acsPassword' class='textfield'/>
    </div>
</div>

 <div align='right'><span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='setDataReboot()' /></span>
 </div>
    
<div align='center' class="formBox" id="divPrimaryNetworkFormBox">
	<label class='error' id='lPassErrorMesPN'  style='display: none'></label>
</div>

</div>
    <br class="clear" />
</div>

<div class="popUpBox" id="alertMB" style="display: none">

    <h1 id="lAlert"></h1>

    <a href="#" class="close" onclick="hm()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />

 <div align="center">
     <label id="lAlertMessage" class="lable12" style="padding-right: 25px;"></label>
    <br style="clear:both" />
    <div class="buttonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:center">
        <span class="btnWrp"><input id="btnModalOk" type="button"  value="OK" onclick="hm()" /></span>
		</div>
 </div>

</div>

