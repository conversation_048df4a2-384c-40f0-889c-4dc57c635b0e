<?xml version="1.0" encoding="US-ASCII"?>
<RGW>
	<wan>
	       <connect_disconnect>cellular</connect_disconnect>
		<proto>cellular</proto>
		<advanced_setting>0</advanced_setting>
		<cdns_enable>0</cdns_enable>
		<cdns1 />
		<cdns2 />
		<mask />
        <device_uuid />
        <connect_mode>0</connect_mode>
        <Roaming_disable_auto_dial>0</Roaming_disable_auto_dial>
        <Roaming_disable_auto_dial_action>0</Roaming_disable_auto_dial_action>
        <Roaming_disable_dial>0</Roaming_disable_dial>
        <Roaming_disable_dial_action>0</Roaming_disable_dial_action>
        <mtu>1500</mtu>
        <auto_apn>1</auto_apn>
         <additional_APN>0</additional_APN>
        <mtu_action>1</mtu_action>
        <Engineering_mode>0</Engineering_mode>
         <query_time_interval>1</query_time_interval>
		<cellular>
			<disconnectnetwork_action>0</disconnectnetwork_action>
			<disconnectnetwork>1</disconnectnetwork>
			<pin></pin>
			<puk></puk>
			<conn_days_all/>
			<conn_hours_all/>
			<conn_minutes_all/>
			<conn_seconds_all/>
			<sim_status></sim_status>
			<IMEI/>
			<isp_supported_list_meta>ISP%uname%pswd%baud%int1%int2%num%connmode%idl</isp_supported_list_meta>
            <tft_apply_action>0</tft_apply_action>
            <pdp_auto_list_meta>mmc%mnc%operator_name%apn%lte_apn%network_type%authtype2g3g%username2g3g%password2g3g%authtype4g%username4g%password4g%iptype</pdp_auto_list_meta>
            <pdp_supported_list_meta>rulename%connnum%pconnnum%enable%conntype%default%secondary%apn%lte_apn%iptype%qci%authtype2g3%usr2g3%paswd2g3%authtype4g%usr4g%paswd4g%hastft</pdp_supported_list_meta>
            <pdp_supported_list>PDN1%1%1%1%0%1%1%cmnet%cmnet%1%0%NONE%any%any%NONE%any%any%0</pdp_supported_list>
            <pdp_context_list_meta>rulename%connnum%pconnnum%success%default%secondary%ipv4%v4dns1%v4dns2%v4gateway%v4netmask%ipv6%g_ipv6%v6dns1%v6dns2%v6gateway%v6netmask%curconntime%totalconntime</pdp_context_list_meta>
	     <pdp_context_list></pdp_context_list>
	     <profile_list_meta>profile_name%enable%conntype%apn%lte_apn%iptype%qci%authtype2g3g%usr2g3g%paswd2g3g%authtype4g%usr4g%paswd4g</profile_list_meta>
	     <profile_list/>
	     <pdp_enable></pdp_enable>
            <bgscan_time_action>0</bgscan_time_action>
            <bgscan_time>4</bgscan_time>
            <manual_network_start>0</manual_network_start>
            <search_network></search_network>
            <network_param>30</network_param>
            <network_param_action>0</network_param_action>
            <network_select_done/>
            <auto_network>0</auto_network>
            <select_NW_Mode>2</select_NW_Mode>
            <mannual_network_list_meta>name%act%plmm_name</mannual_network_list_meta>
            <mannual_network_list></mannual_network_list>
            <NW_mode>1</NW_mode>
            <prefer_mode>1</prefer_mode>
            <NW_mode_action>1</NW_mode_action>
            <prefer_mode_action>0</prefer_mode_action>
            <prefer_lte_type>0</prefer_lte_type>
            <prefer_lte_type_action>0</prefer_lte_type_action>
		</cellular>
		<wifi>
			<ssid></ssid>
			<enc></enc>
			<cipher></cipher>
			<psk></psk>
		</wifi>
		<username_webportal></username_webportal>
		<password_webportal></password_webportal>
           <LWG_flag>1</LWG_flag>
    <version_flag>0</version_flag>
    <version_flag_action>0</version_flag_action>
    <auto_switch>0</auto_switch>
    <auto_switch_action>0</auto_switch_action>
    <disable_switch>0</disable_switch>
    <rsrp>0</rsrp>
    <rssi>0</rssi>
    <cellid>0</cellid>
    <sinr>0</sinr>   
	</wan>
</RGW>
