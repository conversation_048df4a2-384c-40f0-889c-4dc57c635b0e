
(function ($) {

    $.fn.objConnectedDev = function (InIt) {


        var _xmlname = '';

        var mapData;

        var _xml='';

        var _lastSortValue='';
        var _arrayTableData=new Array(0);
        this.onLoad = function (flag) {
            _arrayTableData=new Array(0);
            var index=0;
            var name;
            var name_type;
            var mac;
            var blocked;
            var conn_type;
            var ip_address;
            var connected;
            if(flag)
                this.loadHTML();
            document.getElementById("title").innerHTML = jQuery.i18n.prop(InIt);
            var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);

            arrayLabels = document.getElementsByTagName("th");
            lableLocaliztion(arrayLabels);

            _xml=callProductXML(_xmlname);

            $(_xml).find("Item").each(function() {
                name = decodeURIComponent($(this).find("name").text());
                name_type = $(this).find("name_type").text();
                mac = $(this).find("mac").text();
                blocked = $(this).find("blocked").text();
                conn_type = $(this).find("conn_type").text();
                ip_address = $(this).find("ip_address").text();
                connected =  $(this).find("connected").text();
                conn_time =  $(this).find("conn_time").text();
                _arrayTableData[index]=new Array(8);

                _arrayTableData[index][0]=index;
                _arrayTableData[index][1]=blocked;
                _arrayTableData[index][2]=name;
                _arrayTableData[index][3]=ip_address;
                _arrayTableData[index][4]=mac;
                _arrayTableData[index][5]=conn_type;
                _arrayTableData[index][6]=name_type;
                _arrayTableData[index][7]=connected;
                _arrayTableData[index][8]=conn_time;
                index++;
            });

            this.loadTableData(_arrayTableData);
        }
        this.loadTableData = function(arrayTableData) {
            // 填充桌面端表格
            this.populateDesktopTable(arrayTableData);
            // 填充移动端卡片
            this.populateMobileCards(arrayTableData);
        }

        this.populateDesktopTable = function(arrayTableData) {
            var tableConnectedDevice=document.getElementById('tableConnectedDevice');
            var tBodytableConnectedDevice = tableConnectedDevice.getElementsByTagName('tbody')[0];
            clearTabaleRows('tableConnectedDevice');
            if(arrayTableData.length==0) {
                var row1 =  tBodytableConnectedDevice.insertRow(0);
                var rowCol1 = row1.insertCell(0);
                rowCol1.colSpan = 7;
                rowCol1.innerHTML = jQuery.i18n.prop("tableNoData");
            } else {
                for(var i=0; i<arrayTableData.length; i++) {
                    var arrayTableDataRow=arrayTableData[i];


                    var row =  tBodytableConnectedDevice.insertRow(i);
                    var indexCol= row.insertCell(0);

                    var nameCol = row.insertCell(1);
                    var sortCol=row.insertCell(2);
                    var ipCol = row.insertCell(3);
                    var macCol = row.insertCell(4);
                    var conn_typeCol = row.insertCell(5);
                    var conntimeCol=row.insertCell(6);

                    // 添加CSS类用于移动端优化
                    nameCol.className = "name-col";
                    sortCol.className = "status-col";
                    ipCol.className = "ip-col";
                    macCol.className = "mac-col";
                    conn_typeCol.className = "connection-col";
                    conntimeCol.className = "time-col";
                    indexCol.style.display='none';
                    indexCol.innerHTML=arrayTableDataRow[0];

                    if(arrayTableDataRow[2] == 'unkown_marvell')
                        arrayTableDataRow[2] = ' ';
                    nameCol.innerHTML=arrayTableDataRow[2];
                    if(arrayTableDataRow[7]=='1')
                        sortCol.innerHTML="<span class='device-status-dot online' title='在线'></span><label value='"+ arrayTableDataRow[7]+"'/> ";
                    else
                        sortCol.innerHTML="<span class='device-status-dot offline' title='离线'></span><label value='"+ arrayTableDataRow[7]+"'/>";
                    if(arrayTableDataRow[3]!='')
                        ipCol.innerHTML=arrayTableDataRow[3];
                    else
                        ipCol.innerHTML='--';
                    macCol.innerHTML=arrayTableDataRow[4];
                    conn_typeCol.innerHTML=arrayTableDataRow[5];
                    var connTime = arrayTableDataRow[8].split(",");
                    var nHour = parseInt(connTime[0]);
                    var nMinute = parseInt(connTime[1]);
                    var nSecond = parseInt(connTime[2]);
                    var connTimeInfo = (nHour > 1) ? (nHour   + " " +  jQuery.i18n.prop("ldHours") + " ") : (nHour  + " " +  jQuery.i18n.prop("ldHour") + " ");
                    connTimeInfo += (nMinute > 1) ? (nMinute  + " " +  jQuery.i18n.prop("ldMinutes") + " ") : (nMinute   + " " +  jQuery.i18n.prop("ldMinute") + " ");
                    connTimeInfo += (nSecond > 1) ? (nSecond  + " " +  jQuery.i18n.prop("ldSeconds")) : (nSecond   + " " +  jQuery.i18n.prop("ldSecond"));
                    conntimeCol.innerHTML = connTimeInfo;

                }

                //alert("reset lastcol: " +tableConnectedDevice.lastCol);
                //Table.sort(tableConnectedDevice,{'desc':_lastSortValue, 'col':3, 'overridesort':true});
                //Table.sort(tableConnectedDevice,{'desc':_lastSortValue,'re_sort':true});
            }
            Table.stripe(tableConnectedDevice,"alternate","table-stripeclass");
            if (_lastSortValue) {
                _lastSortValue = false;
        Table.sort(tableConnectedDevice, {'desc':true,'re_sort':false,'col':3});
            } else {
            Table.sort(tableConnectedDevice, {'re_sort':true,'col':3});
            }
        }

        this.populateMobileCards = function(arrayTableData) {
            var mobileContainer = document.getElementById('mobileDeviceList');
            if (!mobileContainer) return;

            mobileContainer.innerHTML = '';

            if(arrayTableData.length == 0) {
                mobileContainer.innerHTML = '<div class="empty-state">' +
                    '<div class="empty-state-icon">📱</div>' +
                    '<div class="empty-state-text">' + jQuery.i18n.prop("tableNoData") + '</div>' +
                    '</div>';
                return;
            }

            for(var i = 0; i < arrayTableData.length; i++) {
                var device = arrayTableData[i];
                var deviceName = device[2] == 'unkown_marvell' ? '未知设备' : device[2];
                var isConnected = device[7] == '1';
                var ipAddress = device[3] || '--';
                var macAddress = device[4];
                var connectionType = device[5];

                // 处理连接时间
                var connTime = device[8].split(",");
                var nHour = parseInt(connTime[0]);
                var nMinute = parseInt(connTime[1]);
                var nSecond = parseInt(connTime[2]);
                var timeStr = '';
                if(nHour > 0) timeStr += nHour + 'h ';
                if(nMinute > 0) timeStr += nMinute + 'm ';
                timeStr += nSecond + 's';

                var statusIcon = isConnected ?
                    '<span class="device-status-dot online" title="在线"></span>' :
                    '<span class="device-status-dot offline" title="离线"></span>';

                // 获取国际化标签
                var ipLabel = jQuery.i18n.prop("ltIpAddress") || "IP地址";
                var macLabel = jQuery.i18n.prop("ltMac") || "MAC地址";
                var connectionLabel = jQuery.i18n.prop("ltConnection") || "连接方式";
                var timeLabel = jQuery.i18n.prop("ltTime") || "连接时长";
                var renameLabel = jQuery.i18n.prop("btnModalOk") || "重命名";
                var actionLabel = isConnected ? "断开" : "连接";

                var cardHtml = '<div class="device-card" data-index="' + i + '">' +
                    '<div class="device-card-header">' +
                        '<div class="device-name">' + deviceName + '</div>' +
                        '<div class="device-status">' + statusIcon + '</div>' +
                    '</div>' +
                    '<div class="device-info-grid">' +
                        '<div class="device-info-item">' +
                            '<div class="device-info-label">' + ipLabel + '</div>' +
                            '<div class="device-info-value">' + ipAddress + '</div>' +
                        '</div>' +
                        '<div class="device-info-item">' +
                            '<div class="device-info-label">' + macLabel + '</div>' +
                            '<div class="device-info-value">' + macAddress + '</div>' +
                        '</div>' +
                        '<div class="device-info-item">' +
                            '<div class="device-info-label">' + connectionLabel + '</div>' +
                            '<div class="device-info-value">' + connectionType + '</div>' +
                        '</div>' +
                        '<div class="device-info-item">' +
                            '<div class="device-info-label">' + timeLabel + '</div>' +
                            '<div class="device-info-value">' + timeStr + '</div>' +
                        '</div>' +
                    '</div>' +
                    '<div class="device-actions" style="display: none;">' +
                        '<button class="device-action-btn primary" onclick="showDlg(' + i + ')" ontouchstart="">' + renameLabel + '</button>' +
                        '<button class="device-action-btn" onclick="blockUnblock(' + i + ', ' + (isConnected ? 'true' : 'false') + ')" ontouchstart="">' +
                            actionLabel +
                        '</button>' +
                    '</div>' +
                '</div>';

                mobileContainer.innerHTML += cardHtml;
            }
        }
        this.loadHTML = function() {
            document.getElementById('Content').innerHTML ="";
            document.getElementById('Content').innerHTML = callProductHTML("html/home_network/connected_device.html");
            Table.auto();
            _lastSortValue = true;

            // 初始化响应式视图
            this.initResponsiveView();
        }

        this.initResponsiveView = function() {
            // 检查屏幕尺寸并切换视图
            var checkViewport = function() {
                var isMobile = window.innerWidth <= 768;
                var desktopView = document.querySelector('.desktop-view');
                var mobileView = document.querySelector('.mobile-view');

                if (desktopView && mobileView) {
                    if (isMobile) {
                        desktopView.style.display = 'none';
                        mobileView.style.display = 'block';
                    } else {
                        desktopView.style.display = 'block';
                        mobileView.style.display = 'none';
                    }
                }
            };

            // 初始检查
            checkViewport();

            // 监听窗口大小变化
            window.addEventListener('resize', checkViewport);
        }
        this.setXMLName = function (xmlname) {
            _xmlname = xmlname;
        }
        this.postItem = function(index,isBlocked,renameFlag,name) {
            var itemIndex=0;
            mapData=null;
            mapData = new Array();

            this.putMapElement("RGW/device_management/known_devices_list/Item#index",_arrayTableData[index][0],itemIndex++);
            this.putMapElement("RGW/device_management/known_devices_list/Item/blocked",isBlocked,itemIndex++);
            this.putMapElement("RGW/device_management/known_devices_list/Item/mac",_arrayTableData[index][4],itemIndex++);
            if(renameFlag) {

                this.putMapElement("RGW/device_management/known_devices_list/Item/name",name,itemIndex++);
                this.putMapElement("RGW/device_management/known_devices_list/Item/name_type",2,itemIndex++);
            } else {
                this.putMapElement("RGW/device_management/known_devices_list/Item/name",_arrayTableData[index][2]);
                this.putMapElement("RGW/device_management/known_devices_list/Item/name_type",_arrayTableData[index][6],itemIndex++);
            }
            var connType=_arrayTableData[index][5];
            if(connType!='')
                this.putMapElement("RGW/device_management/known_devices_list/Item/con_type",connType,itemIndex++);

            if(mapData.length>0) {
                postXML(_xmlname, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
                //this.onLoad();
            }
        }
        this.onPostSuccess = function () {
            this.onLoad(true);
        }
        this.putMapElement = function(xpath,value,index) {
            mapData[index]=new Array(2);
            mapData[index][0]=xpath;
            mapData[index][1]=value;
        }
        this.getTableData = function() {
            return _arrayTableData;
        }
        this.getName = function(index) {
            return _arrayTableData[index][2];
        }
        this.getBlocked = function(index) {
            return _arrayTableData[index][1];
        }
        this.postItemRemoveDeviceEntry = function(index) {
            mapData=null;
            mapData = new Array();
            this.putMapElement("RGW/device_management/known_devices_list/Item/mac#delete",_arrayTableData[index][4],index);

            if(mapData.length>0) {
                postXML(_xmlname, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
                // this.onLoad();
            }

        }
        return this.each(function () {
            _connectedDeviceIntervalID = setInterval( "g_objContent.onLoad(false)", _connectedDeviceInterval);
        });
    }
})(jQuery);
var connetedDeviceSelectedIndex=0;
function showDlg(index) {
    clearInterval(_connectedDeviceIntervalID);
    connetedDeviceSelectedIndex=index;

    // 添加show类以应用移动端样式
    var modal = document.getElementById('box');
    if (modal) {
        modal.classList.add('show');
    }

    sm('box',350,200);

    getID("tbModal").value = g_objContent.getName(index);
    getID("tbModal").focus();
    getID("tbModal").focus();
    document.getElementById("btnModalOk").innerHTML=jQuery.i18n.prop("btnModalOk");
    document.getElementById("btnModalReset").innerHTML=jQuery.i18n.prop("btnModalReset");
    document.getElementById("lModalHeader").innerHTML=jQuery.i18n.prop("lModalHeader");
    document.getElementById("h1DeviceHeader").innerHTML=jQuery.i18n.prop("h1DeviceHeader");

}
function btnOkSelected() {
    _connectedDeviceIntervalID = setInterval( "g_objContent.onLoad()", _connectedDeviceInterval);

    var  index=connetedDeviceSelectedIndex;
    var strName=getID("tbModal").value;
    strName = encodeURIComponent(strName);
    if(strName!='') {
        if(deviceNameValidation(strName)) {
            g_objContent.postItem(index,g_objContent.getBlocked(index),true,strName);
            // hm('box');
        } else {
            getID("ErrInvalidName").style.display = "block";
            getID("ErrInvalidName").innerHTML = jQuery.i18n.prop("ErrInvalidName");
        }
    }
}
function btnCancelClicked() {
    // 移除show类
    var modal = document.getElementById('box');
    if (modal) {
        modal.classList.remove('show');
    }
    _connectedDeviceIntervalID = setInterval( "g_objContent.onLoad()", _connectedDeviceInterval);
}

function btnRemoveSelected() {
    _connectedDeviceIntervalID = setInterval( "g_objContent.onLoad(false)", _connectedDeviceInterval);

    var  index=connetedDeviceSelectedIndex;
    //var strName=getID("tbModal").value;
    g_objContent.postItemRemoveDeviceEntry(index);
}

function blockUnblock(index,value) {
    g_objContent.postItem(index,value,false,null);
}

