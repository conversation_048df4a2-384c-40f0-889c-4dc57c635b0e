<a href='#' class='help' onclick="getHelp('TimeZone')">&nbsp;</a>

      <label id="title" class="title"></label>
    <div >
    <label id='ldeviceTimeZone'></label>
    <div id='ldeviceTimeZoneValue' class="boldTxt subttl"></div>
    
    </div>
    <br />

    <input type="radio" name="TimeZone" value="0" id="getConnDevTimeZone" onclick="rbTimeZoneCicked()" /><span class="boldTxt">Get Connected Client's Time Zone</span> &nbsp;&nbsp; <span id="lConnectedDeviceTimeZone" ></span>
    <br /><br /><input type="radio" name="TimeZone" value="1" id="setConnDevTimeZone" checked onclick="rbTimeZoneCicked()" /><span class="boldTxt">Select Time Zone:</span>
    <br /> <select id='drpdwnTimeZone' class="combox2">
 
    </select>
    <div  class="formBox" ></div>
 <div align='right'><span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='setData()' /></span></div>

