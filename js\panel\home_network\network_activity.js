
(function($) {

    $.fn.objAccess_Logs = function(InIt) {

	 var LOG_NUM_PER_PAGE = 10;
	 var MAX_LOG_NUM = 300;
	 var currentActiveDialPageIdx = 1;
	 var currentActiveClientPageIdx = 1;
	 var xmlName = "";
	 var pageInitFlag = true;
	 var refreshLogType = ""; 

	 $("#Content").html(callProductHTML("html/home_network/network_activity.html"));

	 function SetLocale() {
	 	 var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);
            lableLocaliztion(document.getElementsByTagName("label"));
            lableLocaliztion(document.getElementsByTagName("th"));
            $("#ltDialLogDeleteBtn").val(jQuery.i18n.prop("ltDialLogDeleteBtn"));
	     $("#ltClientLogDeleteBtn").val(jQuery.i18n.prop("ltClientLogDeleteBtn"));
	    
        }

	 function RefreshDialLogDeleteBtn(bDisabledBtn) {
            if(bDisabledBtn) {
                $("#ltDialLogDeleteBtn").attr("disabled", true);
                $("#ltDialLogDeleteBtn").parent(".btnWrp:first").addClass("disabledBtn");

            } else {
                $("#ltDialLogDeleteBtn").attr("disabled", false);
                $("#ltDialLogDeleteBtn").parent(".btnWrp:first").removeClass("disabledBtn");

            }
        }

	  function RefreshClientLogDeleteBtn(bDisabledBtn) {
            if(bDisabledBtn) {
                $("#ltClientLogDeleteBtn").attr("disabled", true);
                $("#ltClientLogDeleteBtn").parent(".btnWrp:first").addClass("disabledBtn");

            } else {
                $("#ltClientLogDeleteBtn").attr("disabled", false);
                $("#ltClientLogDeleteBtn").parent(".btnWrp:first").removeClass("disabledBtn");

            }
        }
	 
	  function InitDialLogPageNum(totalPageNum) {
            var htmlTxt = "";
            for (var idx = 1; idx <= totalPageNum; ++idx) {
                var html = "<a style=\"color: red; font-weight: 700; text-decoration: underline;margin-left:3px;cursor:pointer;\" href=\"##\">" + idx + "</a>";
                //$("#divPhoneBookPageNum").append(htmlTxt);
                htmlTxt += html;

            }
            document.getElementById("divDialLogPageNum").innerHTML = htmlTxt;
		
            $("#divDialLogPageNum").click(function(event) {
                if ($(event.target).is("a")) {
                    $(event.target).css("color", "blue");
                    $(event.target).addClass("pageSelIdx");
                    $(event.target).siblings().css("color", "red");
                    $(event.target).siblings().removeClass("pageSelIdx");
                    var pageIdx = $(event.target).text();
                    currentActiveDialPageIdx = pageIdx;
                    $("#deleteAllDialLog").attr("checked",false);
                    RefreshDialLogDeleteBtn(true);
                    UpdateLogList(pageIdx, "1", false);
                }
            });
        }

	   function InitClientLogPageNum(totalPageNum) {
            var htmlTxt = "";
            for (var idx = 1; idx <= totalPageNum; ++idx) {
                var html = "<a style=\"color: red; font-weight: 700; text-decoration: underline;margin-left:3px;cursor:pointer;\" href=\"##\">" + idx + "</a>";
                //$("#divPhoneBookPageNum").append(htmlTxt);
                htmlTxt += html;

            }
            document.getElementById("divClientLogPageNum").innerHTML = htmlTxt;

            $("#divClientLogPageNum").click(function(event) {
                if ($(event.target).is("a")) {
                    $(event.target).css("color", "blue");
                    $(event.target).addClass("pageSelIdx");
                    $(event.target).siblings().css("color", "red");
                    $(event.target).siblings().removeClass("pageSelIdx");
                    var pageIdx = $(event.target).text();
                    currentActiveClientPageIdx = pageIdx;
                    $("#deleteAllClientLog").attr("checked",false);
                    RefreshClientLogDeleteBtn(true);
                    UpdateLogList(pageIdx, "0", false);
                }
            });
        }

	function AddDialLogToList(id, cid, startTime, endTime, ipType, ipAddr, ipv6Addr, pdpName) {
		var logInfo = id + "$" + cid + "$" + startTime + "$" + endTime
		              + "$" + ipType + "$" + ipAddr + "$" + ipv6Addr + "$" + pdpName + "$";

		var startTimeHtml, endTimeHtml, ipTypeHtml;
		var ipTypeHtml,ipAddrHtml, ipv6AddrHtml;
		
		if ("0" != startTime) {
		    var arr = startTime.split(" ");
		    startTimeHtml = arr[1] + "/"+ arr[2] + "/" + arr[0] + " " + arr[3];
		}
		if ("0" != endTime) {
		    var arr = endTime.split(" ");
		    endTimeHtml = arr[1] + "/"+ arr[2] + "/" + arr[0] + " " + arr[3];
		}

		ipTypeHtml = "Unkown";
		ipAddrHtml = ipAddr;
		ipv6AddrHtml = ipv6Addr;

		if (0 == ipType) {
		    ipTypeHtml = "IPv4v6";
		}
		else if (1 == ipType) {
		    ipTypeHtml = "IPv4";
		    ipv6AddrHtml = "N/A";
		}
		else if (2 == ipType) {
		    ipTypeHtml = "IPv6";
		    ipAddrHtml = "N/A";
		}

		var htmlTxtNode = "<tr name=\"" + logInfo + "\"><td class=\"pointer\" style=\"text-align:center;\"><span>" + pdpName + "</span></td>"
		                  + "<td style=\"text-align:center\">" + cid + " </td>"
		                  + "<td style=\"text-align:center; padding: 5px 10px\">" + startTimeHtml + " </td>"
		                  + "<td style=\"text-align:center\">" + endTimeHtml + " </td>"
		                  + "<td style=\"text-align:center\">" + ipTypeHtml + " </td>"
		                  + "<td style=\"text-align:center\">" + ipAddrHtml + " </td>"
		                  + "<td style=\"text-align:center\">" + ipv6AddrHtml + " </td>"
		                  + " <td style=\"text-align:center;\"><input class=\"delCheckBox\" type=\"checkbox\" id=\"" + id + "\"></td></tr>";

		$("#DialLogList").append(htmlTxtNode);

		$(".delCheckBox:last").click(function() {
		    if ($(".delCheckBox:checked").length == $(".delCheckBox").length) {
		        $("#deleteAllDialLog").attr("checked", true);
		    } else {
		        $("#deleteAllDialLog").attr("checked", false);
		    }
		    if ($(".delCheckBox:checked").length >= 1) {
		        RefreshDialLogDeleteBtn(false);
		    } else {
		        RefreshDialLogDeleteBtn(true);
		    }
		});

		$("#deleteAllDialLog").click(function() {
		    if ($("#deleteAllDialLog").attr("checked")) {
		        $(".delCheckBox").each(function() {
		            $(this).attr("checked", true);
		        });
		        RefreshDialLogDeleteBtn(false);
		    } else {
		        $(".delCheckBox").each(function() {
		            $(this).attr("checked", false);
		        });
		        RefreshDialLogDeleteBtn(true);
		    }
		})
	  }

	function AddClientLogToList(id, mac, conTime, disconTime) {
		var logInfo = id + "$" + mac + "$" + conTime + "$" + disconTime + "$";
		var conTimeHtml, disconTimeHtml;

		if ("0" != conTime) {
		    var arr = conTime.split(" ");
		    conTimeHtml = arr[1] + "/"+ arr[2] + "/" + arr[0] + " " + arr[3];
		}
		if ("0" != disconTime) {
		    var arr = disconTime.split(" ");
		    disconTimeHtml = arr[1] + "/"+ arr[2] + "/" + arr[0] + " " + arr[3];
		}

		var htmlTxtNode = "<tr name=\"" + logInfo + "\"><td class=\"pointer\" style=\"text-align:center;\"><span>" + mac + "</span></td>"
		                  + "<td style=\"text-align:center; padding: 5px 10px\">" + conTimeHtml + " </td>"
		                  + "<td style=\"text-align:center\">" + disconTimeHtml + " </td>"
		                  + " <td style=\"text-align:center;\"><input class=\"delCheckBox1\" type=\"checkbox\" id=\"" + id + "\"></td></tr>";

		$("#ClientLogList").append(htmlTxtNode);

		$(".delCheckBox1:last").click(function() {
		    if ($(".delCheckBox1:checked").length == $(".delCheckBox1").length) {
		        $("#deleteAllClientLog").attr("checked", true);
		    } else {
		        $("#deleteAllClientLog").attr("checked", false);
		    }
		    if ($(".delCheckBox1:checked").length >= 1) {
		        RefreshClientLogDeleteBtn(false);
		    } else {
		        RefreshClientLogDeleteBtn(true);
		    }
		});

		$("#deleteAllClientLog").click(function() {
		    if ($("#deleteAllClientLog").attr("checked")) {
		        $(".delCheckBox1").each(function() {
		            $(this).attr("checked", true);
		        });
		        RefreshClientLogDeleteBtn(false);
		    } else {
		        $(".delCheckBox1").each(function() {
		            $(this).attr("checked", false);
		        });
		        RefreshClientLogDeleteBtn(true);
		    }
		})
	}
	
	function LogListResult(xmldata) {
				  
              if (pageInitFlag) {
                var dialLogCount = parseInt($(xmldata).find("dial_log_num").text());
		  var clientLogCount = parseInt($(xmldata).find("client_log_num").text());
		  var login_time = $(xmldata).find("login_time").text();
			
		 if ( "" != login_time) {
		    var arrLoginTime = login_time.split(" ");
		    login_time = arrLoginTime[1] + "/" + arrLoginTime[2] + "/" + arrLoginTime[0] + " " + arrLoginTime[3];
		 }
		 document.getElementById("lDateValue").innerHTML = jQuery.i18n.prop('lLogintime') + ' ' + login_time;

		 $("#DialLogList").empty();
		 $("#deleteAllDialLog").attr("checked", false);
		 RefreshDialLogDeleteBtn(true);

		 $("#ClientLogList").empty();
		 $("#deleteAllClientLog").attr("checked", false);
		 RefreshClientLogDeleteBtn(true);
			
                var dialLogPageNum = Math.floor(dialLogCount / LOG_NUM_PER_PAGE);
                if (dialLogCount % LOG_NUM_PER_PAGE) {
                    dialLogPageNum = dialLogPageNum + 1;
                }
		  InitDialLogPageNum(dialLogPageNum);

		  
		  var clientLogPageNum = Math.floor(clientLogCount / LOG_NUM_PER_PAGE);
                if (clientLogCount % LOG_NUM_PER_PAGE) {
                    clientLogPageNum = clientLogPageNum + 1;
                }
                InitClientLogPageNum(clientLogPageNum);
				
                var SelPage = currentActiveDialPageIdx -1;
                var $Selector = "#divDialLogPageNum a:eq(" + SelPage + ")";
                $($Selector).css("color", "blue");
                $($Selector).siblings().css("color", "red");
                $($Selector).addClass("pageSelIdx");
                $($Selector).siblings().removeClass("pageSelIdx");

		  var SelClientPage = currentActiveClientPageIdx -1;
		  $Selector = "#divClientLogPageNum a:eq(" + SelClientPage + ")";
		  $($Selector).css("color", "blue");
                $($Selector).siblings().css("color", "red");
                $($Selector).addClass("pageSelIdx");
                $($Selector).siblings().removeClass("pageSelIdx");
            } else {
            	  if (refreshLogType == "1") {
		  	$("#DialLogList").empty();
		  	$("#deleteAllDialLog").attr("checked", false);
		  	RefreshDialLogDeleteBtn(true);
            	  } else {
			$("#ClientLogList").empty();
		 	$("#deleteAllClientLog").attr("checked", false);
		 	RefreshClientLogDeleteBtn(true);
		  }	 
	     }

            $(xmldata).find("dial_log_entries").each(function() {
                $(this).find("Item").each(function() {
                    var dialLogID = $(this).find("id").text();
                    var dialLogStartTime = $(this).find("start_time").text();
                    var dialLogEndTime = $(this).find("end_time").text();
                    var dialLogCid = $(this).find("cid").text();
                    var dialLogIpType = $(this).find("ip_type").text();
                    var dialLogPdpName = $(this).find("pdp_name").text();
                    var dialLogIpAddr = $(this).find("ip_addr").text();
                    var dialLogIpv6Addr = $(this).find("ipv6_addr").text(); 

                    AddDialLogToList(dialLogID, dialLogCid, dialLogStartTime, dialLogEndTime, dialLogIpType, dialLogIpAddr, dialLogIpv6Addr, dialLogPdpName);
                });
            });

	     $(xmldata).find("client_log_entries").each(function() {
                $(this).find("Item").each(function() {
                    var clientLogID = $(this).find("id").text();
                    var clientLogMac = $(this).find("wifimac").text();
                    var clientLogConTime = $(this).find("con_time").text();
                    var clientLogDisconTime = $(this).find("discon_time").text();

                    AddClientLogToList(clientLogID, clientLogMac, clientLogConTime, clientLogDisconTime);
                });
            });
         }
			 
	  function UpdateLogList(pageNumber, logType, bInitFlag) {

            var mapData = new Array();
            var itemIndex = 0;

	     if (bInitFlag)
	     {
            	  putMapElement(mapData, "RGW/detailed_log/dial_log_page_num", pageNumber, itemIndex++);
                putMapElement(mapData, "RGW/detailed_log/client_log_page_num", pageNumber, itemIndex++);
	     }
	     else if (logType == "1")
	     	  putMapElement(mapData, "RGW/detailed_log/dial_log_page_num", pageNumber, itemIndex++);
            else
		  putMapElement(mapData, "RGW/detailed_log/client_log_page_num", pageNumber, itemIndex++);	


	   pageInitFlag = bInitFlag;
	   refreshLogType = logType;

           PostXMLWithResponse(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)), LogListResult);
        }

	  $("#ltDialLogDeleteBtn").click(function() {
            var delId = "";
            $(".delCheckBox:checked").each(function() {
                delId = delId + $(this).attr("id") + ",";
            });
     
            var itemIndex = 0;
            var mapData = new Array();
            putMapElement(mapData, "RGW/detailed_log/delete_dial_log_index", delId, itemIndex++);

            function QueryDialLogDeleteResult(nullArg)
            {
                 var xml = GetSyncXML(xmlName);

                 if (0 == $(xml).find("delete_dial_log_result").text()) {
                    if (currentActiveDialPageIdx != 1 && $("#deleteAllDialLog").attr("checked")) {
                        currentActiveDialPageIdx--;
                    }
					
                    $("#deleteAllDialLog").attr("checked", false);
	                    UpdateLogList(currentActiveDialPageIdx, "1", true);
                    } else {
		             alert("delete log failed.");
                }
            }

            PostXMLWithResponse(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)),QueryDialLogDeleteResult);
           
        });

	    $("#ltClientLogDeleteBtn").click(function() {
              var delId = "";

		$(".delCheckBox1:checked").each(function() {
	    		delId = delId + $(this).attr("id") + ",";
		});
		
            var itemIndex = 0;
            var mapData = new Array();
            putMapElement(mapData, "RGW/detailed_log/delete_client_log_index", delId, itemIndex++);

            function QueryClientLogDeleteResult(nullArg)
            {
                 var xml = GetSyncXML(xmlName);

                 if (0 == $(xml).find("delete_client_log_result").text()) {
			if (currentActiveClientPageIdx != 1 && $("#deleteAllClientLog").attr("checked")) {
				currentActiveClientPageIdx--;
			}

			$("#deleteAllClientLog").attr("checked", false);
			UpdateLogList(currentActiveClientPageIdx, "0", true);
                  } else {
		       alert("delete log failed.");
                  }
            }

            PostXMLWithResponse(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)),QueryClientLogDeleteResult);
           
        });
		

	 this.onLoad = function() {
            menuId = $("#submenu").children(".on:first").attr("id");
            SetLocale();
            UpdateLogList("1", "1", true);
        }


        this.onPost = function() {
        }

        this.onPostSuccess = function() {
        }


        this.setXMLName = function(_xmlname) {
            xmlName = _xmlname;
        }

         return this;
		
        //return this.each(function() {
           // _networkActivityIntervalID = setInterval("g_objContent.onLoad(false)", _networkActivityInterval);
        //});
    }
})(jQuery);

function btnCancelClickedAccessLogs() {
    hm();
    _networkActivityIntervalID = setInterval("g_objContent.onLoad(false)", _networkActivityInterval);
}

function txtLogRuleNameClicked() {
    document.getElementById("lErrorLogs").style.display = "none";
}

function btnOKClickedAppLogs() {
    clearInterval(_networkActivityIntervalID);
    var ruleName = document.getElementById("txtLogRulename").value;
    var deviceName = document.getElementById("drpdwnDevcieNames").value;
    var mac = getMacAddress();
    var days = getDays();
    var startTime = getStartTime();
    var endTime = getEndTime();
    var errorString = validate_rule(ruleName, deviceName, mac, startTime, endTime);
    if (errorString == "OK") {
        hm();
        g_objContent.postItem(ruleName, deviceName, mac, startTime, endTime, days);
    }
    else {
        document.getElementById("lErrorLogs").style.display = "block";
        document.getElementById("lErrorLogs").innerHTML = jQuery.i18n.prop(errorString);
    }

}
function btnCancelClickedLogs() {
    hm();
    _networkActivityIntervalID = setInterval("g_objContent.onLoad(false)", _networkActivityInterval);
}
function btnAlertOkClickedLogs() {
    hm();
    _networkActivityIntervalID = setInterval("g_objContent.onLoad(false)", _networkActivityInterval);
}
function addMacAddress(address) {
    //    alert(address);
    if (address == null)
        address = ":::::";
    var arraySplit = address.split(":");
    for (var i = 1; i <= 6; i++)
        document.getElementById("txtMac" + i.toString()).value = arraySplit[i - 1];
}
function getMacAddress() {
    var address = "";
    for (var i = 1; i <= 5; i++)
        address += document.getElementById("txtMac" + i.toString()).value + ":";
    address += document.getElementById("txtMac" + i.toString()).value;
    return address;
}
function drpdwnDevcieNamesChanged() {
    var linkObj = document.getElementById("drpdwnDevcieNames");
    var value = linkObj.options[linkObj.selectedIndex].value;
    if (value != "Custom") {
        addMacAddress(g_objContent.getMacAddress(value));
        disabledMac();
    }
    else {
        addMacAddress(null);
        enableMac()
    }
}
function localizeMBAccessLogRule() {
    var arrayLabels = document.getElementsByTagName("label");
    lableLocaliztionMB(arrayLabels);
}
function lableLocaliztionMB(labelArray) {
    for (var i = 0; i < labelArray.length; i++) {
        if (jQuery.i18n.prop(labelArray[i].id) != null)
            getID(labelArray[i].id).innerHTML = jQuery.i18n.prop(labelArray[i].id);
        //   $("#"+labelArray[i].id).text(jQuery.i18n.prop(labelArray[i].id));

    }
}
function addDays(days) {
    if (days == null)
        days = "1,2,3,4,5,6";
    var arraySplit = days.split(",");
    for (var i = 0; i <= 6; i++)
        document.getElementById("tableMonth1" + i.toString()).checked = false;

    for (i = 0; i < arraySplit.length; i++)
        document.getElementById("tableMonth1" + arraySplit[i].toString()).checked = true;
}
function disabledMac() {
    for (var i = 1; i <= 6; i++)
        document.getElementById("txtMac" + i.toString()).disabled = true;
}
function enableMac() {
    for (var i = 1; i <= 6; i++)
        document.getElementById("txtMac" + i.toString()).disabled = false;
}
function getDays() {
    var days = "";

    for (var i = 0; i <= 6; i++) {
        if (document.getElementById("tableMonth1" + i.toString()).checked)
            days += i.toString() + ",";
    }
    days = days.slice(0, days.length - 1)
    return days;
}
function setStartTime(time) {
    var arraySplit = time.split(":");

    document.getElementById("txtLogStartTime1").value = arraySplit[0];
    document.getElementById("txtLogStartTime2").value = arraySplit[1];
    document.getElementById("txtLogStartTime3").value = "00";

}
function getStartTime() {
    var value;
    value = document.getElementById("txtLogStartTime1").value;
    value = value + ":" + document.getElementById("txtLogStartTime2").value;
    // value = value + ":" + document.getElementById("txtLogStartTime3").value;
    return value;
}
function setEndTime(time) {
    var arraySplit = time.split(":");

    document.getElementById("txtLogEndTime1").value = arraySplit[0];
    document.getElementById("txtLogEndTime2").value = arraySplit[1];
    document.getElementById("txtLogEndTime3").value = "00";
}
function getEndTime() {
    var value;
    value = document.getElementById("txtLogEndTime1").value;
    value = value + ":" + document.getElementById("txtLogEndTime2").value;
    //  value = value + ":" + document.getElementById("txtLogEndTime3").value;
    return value;
}
function validate_rule(ruleName, deviceName, mac, startTime, endTime) {
    var regex = /^([0-9a-f]{2}([:-]|$)){6}$|([0-9a-f]{4}([.]|$)){3}$/i;
    if (ruleName == "")
        return "EMPTY_RULE_NAME";
    if (!deviceNameValidation(ruleName))
        return "SPECIAL_CHARS_ARE_NOT_ALLOWED";
    if (!deviceNameValidation(deviceName))
        return "SPECIAL_CHARS_ARE_NOT_ALLOWED";
    if (!regex.test(mac))
        return "MAC_IS_NOT_VALID";
    if (!startTime.match(/^[0-2][0-9]\:[0-6][0-9]$/))
        return "INVALID_START_TIME";
    if (!endTime.match(/^[0-2][0-9]\:[0-6][0-9]$/))
        return "INVALID_END_TIME";
    if (!compareTimes(startTime, endTime))
        return "START_TIME_LESS_ERROR"
    return "OK";
}
function compareTimes(time1, time2) {
    var splitArray1 = time1.split(":");
    var splitArray2 = time2.split(":");
    for (var i = 0; i < 3; i++) {
        if (splitArray1[i] < splitArray2[i])
            return true;
    }
    return false;


}
function setFocus(controlID) {
    var str = document.getElementById(controlID).value;
    if (str.length == 2) {
        var c = controlID.toString().charAt(controlID.length - 1);
        c++;
        controlID = controlID.substring(0, controlID.length - 1);
        controlID = controlID + c;
        document.getElementById(controlID.toString()).focus();
    }
}

