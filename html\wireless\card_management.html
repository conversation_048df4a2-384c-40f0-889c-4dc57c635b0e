<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title id="cardManagementTitle">卡片管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .card-management-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            /* border: 1px solid #ddd; */
            border-radius: 5px;
            /* background-color: #f9f9f9; */
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 25px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .card-select {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
            display: none;
        }
        .status-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
        }
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #ffe0e0;
            border-left: 6px solid #ff5252;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .debug-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        .debug-button {
            padding: 10px 24px;
            background: linear-gradient(90deg, #2196F3 0%, #21CBF3 100%);
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 4px 14px 0 rgba(33, 203, 243, 0.15), 0 1.5px 4px 0 rgba(33, 150, 243, 0.10);
            transition: background 0.3s, box-shadow 0.2s, transform 0.1s;
            outline: none;
        }
        .debug-button:hover {
            background: linear-gradient(90deg, #21CBF3 0%, #2196F3 100%);
            box-shadow: 0 6px 20px 0 rgba(33, 203, 243, 0.25), 0 2px 8px 0 rgba(33, 150, 243, 0.15);
            transform: translateY(-2px) scale(1.04);
        }
        .debug-button:active {
            background: linear-gradient(90deg, #1976D2 0%, #21CBF3 100%);
            box-shadow: 0 2px 6px 0 rgba(33, 203, 243, 0.10);
            transform: scale(0.98);
        }
        .debug-button:focus {
            outline: 2px solid #21CBF3;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
  <div class="content">
    <div class="form-section">
      <div class="form-group">
        <div id="mCardManagement" class="formArea">
          <div class="card-management-container">
            <!-- 卡片管理标题 -->
            <h1 id="title"></h1>
            <!-- 自动切卡设置 -->
            <div class="form-group">
              <label id="autoSelectCardLabel" for="autoSelectCard"></label>
              <label class="toggle-switch">
                <input type="checkbox" id="autoSelectCard" checked>
                <span class="slider"></span>
              </label>
              <div id="autoSelectStatus" class="status-text"></div>
            </div>
            <!-- 卡片选择 -->
            <div class="form-group">
              <label id="selectCardLabel" for="cardSelect"></label>
              <select id="cardSelect" class="card-select" style="display: block;">
                <option id="ctccCardOption" value="0"></option>
                <option id="cuccCardOption" value="1"></option>
                <!-- <!-- <option id="cmccCardOption" value="2"></option> --> -->
              </select>
            </div>
            <!-- 当前卡片显示 -->
            <div class="form-group" style="display:none;">
              <label id="currentCardLabel"></label>
              <div id="currentCard"></div>
            </div>
            <!-- 状态提示区域 -->
            <div id="statusEnabledText" style="display:none;"></div>
            <div id="statusDisabledText" style="display:none;"></div>
            <div id="currentStatusText" style="display:none;"></div>
            <div id="autoSelectModeText" style="display:none;"></div>
            <div id="cardNumberText" style="display:none;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
