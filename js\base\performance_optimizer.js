/**
 * 性能优化器 - 防止频繁切换界面导致设备卡死
 */
(function() {
    'use strict';
    
    // 防抖和节流控制
    var switchDebounceTimer = null;
    var lastSwitchTime = 0;
    var MIN_SWITCH_INTERVAL = 500; // 最小切换间隔500ms
    
    // 请求队列管理
    var activeRequests = new Map();
    var requestCounter = 0;
    
    // 定时器管理
    var activeTimers = new Set();
    
    /**
     * 防抖函数包装器
     */
    function debounce(func, wait) {
        var timeout;
        return function executedFunction() {
            var context = this;
            var args = arguments;
            var later = function() {
                timeout = null;
                func.apply(context, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 节流函数包装器
     */
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() { inThrottle = false; }, limit);
            }
        };
    }
    
    /**
     * 优化的界面切换函数
     */
    function optimizedCreateMenu(originalCreateMenu) {
        return function(index) {
            var now = Date.now();
            
            // 防止过于频繁的切换
            if (now - lastSwitchTime < MIN_SWITCH_INTERVAL) {
                return;
            }
            
            // 清除之前的防抖定时器
            if (switchDebounceTimer) {
                clearTimeout(switchDebounceTimer);
            }
            
            // 使用防抖延迟执行
            switchDebounceTimer = setTimeout(function() {
                try {
                    // 清理旧的定时器
                    clearAllActiveTimers();
                    
                    // 取消未完成的请求
                    cancelPendingRequests();
                    
                    // 执行原始函数
                    originalCreateMenu.call(this, index);
                    
                    lastSwitchTime = Date.now();
                } catch (error) {
                    console.error('界面切换出错:', error);
                }
            }, 100);
        };
    }
    
    /**
     * 优化的表单显示函数
     */
    function optimizedDisplayForm(originalDisplayForm) {
        return function(clickedItem) {
            var now = Date.now();
            
            // 防止过于频繁的切换
            if (now - lastSwitchTime < MIN_SWITCH_INTERVAL) {
                return;
            }
            
            try {
                // 清理旧的定时器
                clearAllActiveTimers();
                
                // 取消未完成的请求
                cancelPendingRequests();
                
                // 执行原始函数
                originalDisplayForm.call(this, clickedItem);
                
                lastSwitchTime = Date.now();
            } catch (error) {
                console.error('表单显示出错:', error);
            }
        };
    }
    
    /**
     * 清理所有活动定时器
     */
    function clearAllActiveTimers() {
        // 清理全局定时器变量
        if (typeof _dashboardIntervalID !== 'undefined') clearInterval(_dashboardIntervalID);
        if (typeof _connectedDeviceIntervalID !== 'undefined') clearInterval(_connectedDeviceIntervalID);
        if (typeof _trafficstatisticsIntervalID !== 'undefined') clearInterval(_trafficstatisticsIntervalID);
        if (typeof _networkActivityIntervalID !== 'undefined') clearInterval(_networkActivityIntervalID);
        if (typeof _storageSettingsIntervalID !== 'undefined') clearInterval(_storageSettingsIntervalID);
        if (typeof _WiFiIntervalID !== 'undefined') clearInterval(_WiFiIntervalID);
        
        // 清理记录的定时器
        activeTimers.forEach(function(timerId) {
            clearInterval(timerId);
            clearTimeout(timerId);
        });
        activeTimers.clear();
    }
    
    /**
     * 取消待处理的请求
     */
    function cancelPendingRequests() {
        activeRequests.forEach(function(xhr, requestId) {
            if (xhr && xhr.readyState !== 4) {
                xhr.abort();
            }
        });
        activeRequests.clear();
    }
    
    /**
     * 优化的Ajax请求函数
     */
    function optimizedAjaxRequest(originalAjax) {
        return function(options) {
            var requestId = ++requestCounter;
            
            // 包装原始的beforeSend
            var originalBeforeSend = options.beforeSend;
            options.beforeSend = function(xhr) {
                activeRequests.set(requestId, xhr);
                if (originalBeforeSend) {
                    originalBeforeSend.call(this, xhr);
                }
            };
            
            // 包装原始的complete
            var originalComplete = options.complete;
            options.complete = function(xhr, status) {
                activeRequests.delete(requestId);
                if (originalComplete) {
                    originalComplete.call(this, xhr, status);
                }
            };
            
            // 包装原始的error
            var originalError = options.error;
            options.error = function(xhr, status, error) {
                activeRequests.delete(requestId);
                if (originalError && status !== 'abort') {
                    originalError.call(this, xhr, status, error);
                }
            };
            
            return originalAjax.call(this, options);
        };
    }
    
    /**
     * 优化的定时器创建函数
     */
    function optimizedSetInterval(originalSetInterval) {
        return function(callback, delay) {
            var timerId = originalSetInterval.call(this, callback, delay);
            activeTimers.add(timerId);
            return timerId;
        };
    }
    
    /**
     * 优化的定时器清理函数
     */
    function optimizedClearInterval(originalClearInterval) {
        return function(timerId) {
            activeTimers.delete(timerId);
            return originalClearInterval.call(this, timerId);
        };
    }
    
    /**
     * 内存清理函数
     */
    function cleanupMemory() {
        // 清理缓存的XML数据
        if (typeof cachedXmlData !== 'undefined') {
            cachedXmlData = null;
        }
        
        // 强制垃圾回收（如果支持）
        if (window.gc) {
            window.gc();
        }
    }
    
    /**
     * 初始化性能优化
     */
    function initPerformanceOptimizer() {
        // 优化界面切换函数
        if (window.createMenu) {
            window.originalCreateMenu = window.createMenu;
            window.createMenu = optimizedCreateMenu(window.originalCreateMenu);
        }
        
        if (window.displayForm) {
            window.originalDisplayForm = window.displayForm;
            window.displayForm = optimizedDisplayForm(window.originalDisplayForm);
        }
        
        // 优化jQuery Ajax
        if (window.$ && $.ajax) {
            window.originalAjax = $.ajax;
            $.ajax = optimizedAjaxRequest(window.originalAjax);
        }
        
        // 优化定时器函数
        if (window.setInterval) {
            window.originalSetInterval = window.setInterval;
            window.setInterval = optimizedSetInterval(window.originalSetInterval);
        }
        
        if (window.clearInterval) {
            window.originalClearInterval = window.clearInterval;
            window.clearInterval = optimizedClearInterval(window.originalClearInterval);
        }
        
        // 定期清理内存
        setInterval(cleanupMemory, 30000); // 每30秒清理一次
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            clearAllActiveTimers();
            cancelPendingRequests();
            cleanupMemory();
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时暂停定时器
                clearAllActiveTimers();
            }
        });
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initPerformanceOptimizer);
    } else {
        initPerformanceOptimizer();
    }
    
    // 暴露清理函数到全局
    window.performanceOptimizer = {
        clearAllTimers: clearAllActiveTimers,
        cancelRequests: cancelPendingRequests,
        cleanupMemory: cleanupMemory
    };
    
})();