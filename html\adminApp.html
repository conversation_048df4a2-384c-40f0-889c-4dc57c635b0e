<!-- 蓝白配色CSS -->
<link href="css/blue-white-theme.css" rel="stylesheet" type="text/css" />
<!-- 添加FontAwesome图标库 -->

<script type="text/javascript" src="js/theme-switcher.js" language="javascript"></script>
<!-- 添加jQuery和i18n相关脚本 -->
<script src="js/jquery/jquery.min.js"></script>
<script src="js/jquery/jquery.i18n.properties.js"></script>
<script>
    // 检查jQuery是否正确加载
    window.onload = function() {
        if (typeof jQuery === 'undefined') {
            return;
        }
        
        if (typeof jQuery.i18n === 'undefined') {
            return;
        }
        
        // 初始化i18n配置
        initI18n();
    };

    // i18n初始化函数
    function initI18n() {
        try {
            jQuery.i18n.properties({
                name: 'Messages',
                path: '../properties/',
                mode: 'map',
                language: 'cn',
                encoding: 'UTF-8',
                cache: false,
                async: false,
                callback: function() {
                    try {
                        var testTranslation = jQuery.i18n.prop('battery_level');
                        
                        if (typeof updateStaticTextI18n === 'function') {
                            updateStaticTextI18n();
                        } else {
                        }
                        
                        // 更新电池信息
                        if (typeof updateBatteryInfo === 'function') {
                            updateBatteryInfo();
                        }
                    } catch (e) {
                    }
                }
            });
        } catch (e) {
        }
    }

    // 更新所有静态文本的翻译
    function updateStaticTextI18n() {
        try {
            // 获取并打印所有需要更新的元素
            var elements = {
                batteryLevelLabel: document.getElementById("batteryLevelLabel"),
                chargingStatusLabel: document.getElementById("chargingStatusLabel"),
                imeiLabel: document.getElementById("imeiLabel"),
                snLabel: document.getElementById("snLabel"),
                imsiLabel: document.getElementById("imsiLabel"),
                iccidLabel: document.getElementById("iccidLabel"),
                macLabel: document.getElementById("macLabel"),
                ipLabel: document.getElementById("ipLabel")
            };

            // 检查并更新每个元素
            for (var id in elements) {
                if (elements[id]) {
                    var translation = jQuery.i18n.prop(id.replace('Label', ''));
                    elements[id].textContent = translation;
                } else {

                }
            }
            
        } catch (error) {
        }
    }


</script>
<style>
    /* 本地图标样式 - 替代Font Awesome */
    .fa {
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        text-rendering: auto;
        line-height: 1;
    }

    /* 各种图标的样式 */
    .fa-user {
        display: inline-block;
        width: 1em;
        height: 1em;
        position: relative;
        vertical-align: middle;
    }

    .fa-user::before {
        content: "";
        position: absolute;
        top: 0.1em;
        left: 50%;
        transform: translateX(-50%);
        width: 0.35em;
        height: 0.35em;
        background: currentColor;
        border-radius: 50%;
    }

    .fa-user::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0.8em;
        height: 0.5em;
        background: currentColor;
        border-radius: 0.4em 0.4em 0 0;
    }
    .fa-spinner::before { content: "⟳"; font-family: Arial, sans-serif; }
    .fa-stethoscope::before { content: "🩺"; font-family: Arial, sans-serif; }
    .fa-cog::before { content: "⚙️"; font-family: Arial, sans-serif; }
    .fa-question-circle::before { content: "❓"; font-family: Arial, sans-serif; }
    .fa-sign-out::before { content: "🚪"; font-family: Arial, sans-serif; }
    .fa-exclamation-triangle::before { content: "⚠️"; font-family: Arial, sans-serif; }
    .fa-exclamation-circle::before { content: "❗"; font-family: Arial, sans-serif; }
    .fa-caret-down::before { content: "▼"; font-family: Arial, sans-serif; font-size: 0.8em; }

    .fa-spin {
        animation: fa-spin 1s infinite linear;
    }
    @keyframes fa-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 添加设备信息区域的样式 */
    .info-row {
        width: 100%;
        box-sizing: border-box;
        padding: 15px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 12px;
        background-color: #f5f7fa;
        border-radius: 8px;
    }
    
    /* 添加物理小区信息行的样式 */
    .phy-cell-info-row {
        width: 100%;
        box-sizing: border-box;
        padding: 15px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 12px;
        background-color: #f5f7fa;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    
    /* 添加Ping测试结果行的样式 */
    .ping-results-row {
        width: 100% !important;
        box-sizing: border-box !important;
        padding: 15px !important;
        background-color: #f5f7fa !important;
        border-radius: 8px !important;
        margin-bottom: 15px !important;
    }
    
    /* Ping结果容器样式 */
    #pingResultsContainer {
        display: flex !important;
        flex-direction: row !important;  /* 电脑模式下水平排列 */
        flex-wrap: wrap !important;
        gap: 8px !important;
    }
    
    /* Ping结果项的样式 */
    .ping-result-item {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 10px !important;
        background-color: #fff !important;
        border-radius: 6px !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
        border: 1px solid #e8e8e8 !important;
        flex: 1 1 45% !important; /* 修改为45%，一行放置两个 */
        min-width: 300px !important;
        max-width: calc(50% - 8px) !important;  /* 修改为50%，电脑模式下一行最多显示2个 */
        margin-bottom: 0 !important; /* 覆盖可能的下边距 */
        cursor: pointer !important; /* 添加指针样式 */
        transition: box-shadow 0.2s ease !important;
    }
    
    .ping-result-item:hover {
        box-shadow: 0 3px 6px rgba(0,0,0,0.1) !important;
    }
    
    .ping-result-url {
        font-size: 14px !important;
        color: #333 !important;
        flex: 1 !important;
        margin-right: 8px !important;
    }
    
    .ping-result-status {
        font-weight: bold !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 13px !important;
        text-align: center !important;
        min-width: 60px !important;
        white-space: nowrap !important;
    }
    
    .ping-success {
        background-color: #e6f7e9 !important;
        color: #28a745 !important;
    }
    
    .ping-fail {
        background-color: #fce8ea !important;
        color: #dc3545 !important;
    }
    
    /* 无数据占位符样式 */
    .ping-result-placeholder {
        text-align: center !important;
        color: #999 !important;
        padding: 10px !important;
        width: 100% !important;
    }
    

    
    .info-item {
        box-sizing: border-box;
        overflow: visible;
        word-break: break-all;
        padding: 12px;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        background-color: #ffffff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .info-item.clean {
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        background-color: #ffffff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: box-shadow 0.2s ease;
    }
    
    .info-item.clean:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .info-item.clean strong {
        font-size: 12px;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 6px;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-item.clean label {
        font-size: 16px;
        font-weight: 500;
        color: #212529;
        margin: 0;
        word-break: break-all;
        line-height: 1.4;
    }
    
    .info-section {
        margin-bottom: 30px;
    }
    
    .info-section:last-child {
        margin-bottom: 0;
    }
    
    .info-item strong {
        display: block;
        margin-bottom: 8px;
        color: #666;
        font-weight: 500;
        font-size: 14px;
    }
    
    .info-item label {
        display: block;
        word-break: break-all;
        overflow-wrap: break-word;
        text-align: right;
        color: #0066cc;
        font-weight: 500;
        font-size: 15px;
    }
    
    /* 添加系统信息区域的样式 */
    .system-info-toggle {
        display: inline-flex;
        align-items: center;
        margin: 10px 0;
        padding: 8px 15px;
        background-color: #f0f4f8;
        border: 1px solid #d0d7de;
        border-radius: 6px;
        color: #0066cc;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .system-info-toggle:hover {
        background-color: #e1e7ef;
    }
    
    .system-info-toggle i {
        margin-left: 8px;
        transition: transform 0.3s ease;
    }
    
    .system-info-toggle.open i {
        transform: rotate(180deg);
    }
    
    #systemInfoContent {
        transition: max-height 0.3s ease, opacity 0.3s ease;
        max-height: 0;
        overflow: hidden;
        opacity: 0;
    }
    
    #systemInfoContent.show {
        max-height: 2000px;
        opacity: 1;
    }
    
    /* 响应式布局调整 */
    @media (max-width: 1200px) {
        .info-row, .phy-cell-info-row {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        }
    }
    
    @media (max-width: 992px) {
        .info-row, .phy-cell-info-row {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
    }
    
    @media (max-width: 768px) {
        .info-row, .phy-cell-info-row, .ping-results-row {
            display: block !important; /* 强制显示 */
            padding: 0 15px;
            background-color: transparent;
            border-radius: 0;
        }
        
        .device-info-header {
            flex-direction: column !important;
            gap: 8px !important;
            text-align: center !important;
        }
        
        #sysInfoBtn {
            width: 100% !important;
            max-width: 200px !important;
        }
        
        .ping-results-row {
            margin-top: 10px;
        }
        
        .ping-result-item {
            margin-bottom: 8px;
        }
        
    .info-item {
        box-sizing: border-box;
        overflow: visible;
        word-break: break-all;
        padding: 12px;
        display: flex;
        flex-direction: row; /* 手机端改为水平排列 */
        align-items: center;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        background-color: #ffffff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        min-width: 0;
    }
        
        .info-item strong {
            margin-bottom: 0;
            margin-right: 8px;
            min-width: 80px;
            font-size: inherit;
            flex-shrink: 0; /* 防止标签被压缩 */
        }
        
        .info-item label {
            flex: 1;
            font-size: inherit;
            text-align: right; /* 值右对齐 */
        }
        
        /* Ping结果在移动设备上的特殊样式 */
        #pingResultsContainer {
            flex-direction: column !important;  /* 手机模式下垂直排列 */
        }
        
        .ping-result-item {
            max-width: 100% !important;  /* 手机模式下占满整行 */
            margin-bottom: 8px !important;
            flex: 1 1 100% !important;
        }
    }
</style>
<div class="header" id="header">
    <!-- <div class="logo"><img src="images/ZYlogo.png" alt="ZY" title="ZY" /></div> -->
    <div class="loginArea"style="display: none"><label id="lableWelcome">Welcome</label> <a href="#"></a> <br />
        <span id="quickSetupSpan" style="display: none;"><a href="#." onclick="quickSetup()" id="quickSetup" >Quick Setup</a>  |  <a href="#." id="MainHelp" onclick="getMainHelp()">Help</a>  |  <a href="#." id="MainLogOut" onclick="logOut()">Log Out</a></span>
    </div>
    
    <!-- 快速链接移到右上角 -->
    <div class="top-quick-links">
        <span id="quickSetupSpan2"><a href="#." onclick="triggerSysInfo()" id="sysInfoBtn"><i class="fa fa-stethoscope"></i> 系统诊断</a> | <a href="#." id="MainHelp2" onclick="getMainHelp()">Help</a> | <a href="#." id="MainLogOut2" onclick="logOut()">Log Out</a></span>
    </div>
    
    <!-- 电池信息显示区域，水平居中 -->
    <div class="header-battery-info">
        <div class="header-info-item battery-info">
            <strong id="batteryLevelLabel"></strong>
            <span class="info-value" id="batteryLevel">0%</span>
            <div class="battery-icon">
                <div class="battery-level" id="batteryLevelVisual"></div>
            </div>
        </div>
        <div class="header-info-item charging-info">
            <strong id="chargingStatusLabel"></strong>
            <span class="info-value" id="chargingStatus"></span>
        </div>
    </div>
    <!-- <br class="clear" /> -->
</div>
<div class="main-layout">
    <div class="ant-layout-content">
        <span class="mobileMenu" onclick="openNav()" id="mobileMenubtn">&#9776;</span>
        <div class="ant-layout-sider">
            <div class="ant-menu-wrapper">
                <div class="logo-container">
                    <img src="images/ZYlogo.png" alt="ZY" class="menu-logo" />
                </div>
                
                <!-- 添加侧边栏快速链接按钮 -->
                <!-- <div class="sidebar-quick-buttons">
                    <button onclick="quickSetup()" class="sidebar-btn" title="Quick Setup"><i class="fa fa-cog"></i></button>
                    <button onclick="getMainHelp()" class="sidebar-btn" title="Help"><i class="fa fa-question-circle"></i></button>
                    <button onclick="logOut()" class="sidebar-btn" title="Log Out"><i class="fa fa-sign-out"></i></button>
                </div> -->
                
                <ul id="main-menu" class="ant-menu"></ul>
            </div>
        </div>
        <div class="homepic" id="homepic">
            <!-- 设备信息标题 -->
            <div class="device-info-header" style="margin: 10px 0; padding: 8px 15px; background-color: #bbdefb; border-radius: 6px;">
                <div class="device-info-title" id="deviceInfoTitle" style="font-size: 16px; font-weight: bold; color: #333;">
                    设备信息
                </div>
            </div>
            

            
            <div class="info-row" style="display: grid; gap: 20px; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); padding: 25px; background: #f8f9fa; border-radius: 12px; border: 1px solid #e9ecef;">
                
                <!-- 设备标识分类 -->
                <div class="info-section" style="grid-column: 1 / -1;">
                    <h3 style="color: #495057; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding-bottom: 10px; border-bottom: 2px solid #007bff;">📱 设备标识信息</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px;">
                        <div class="info-item clean">
                            <strong id="imeiLabel">IMEI</strong>
                            <label id="IMEIdiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="snLabel">SN</strong>
                            <label id="SNdiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="imsiLabel">IMSI</strong>
                            <label id="IMSIdiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="iccidLabel">ICCID</strong>
                            <label id="ICCIDdiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="macLabel">MAC</strong>
                            <label id="MACdiv">--</label>
                        </div>
                    </div>
                </div>
                
                <!-- 网络状态分类 -->
                <div class="info-section" style="grid-column: 1 / -1;">
                    <h3 style="color: #495057; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding-bottom: 10px; border-bottom: 2px solid #28a745;">🌐 网络连接状态</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px;">
                        <div class="info-item clean">
                            <strong id="networkTypeLabel">网络类型</strong>
                            <label id="NetworkType">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong>IPv4地址</strong>
                            <label id="IPv4div">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="operatorLabel">运营商</strong>
                            <label id="Operator">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="signalStrengthLabel">信号强度</strong>
                            <label id="SignalStrength">--</label>
                        </div>
                    </div>
                </div>
                
                <!-- 频段信息分类 -->
                <div class="info-section" style="grid-column: 1 / -1;">
                    <h3 style="color: #495057; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding-bottom: 10px; border-bottom: 2px solid #ffc107;">📡 频段与小区信息</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px;">
                        <div class="info-item clean">
                            <strong id="bandLabel">频段</strong>
                            <label id="BandDiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="phyCellIdLabel">物理小区ID</strong>
                            <label id="PhyCellIdDiv">--</label>
                        </div>
                    </div>
                </div>
                
                <!-- 信号质量分类 -->
                <div class="info-section" style="grid-column: 1 / -1;">
                    <h3 style="color: #495057; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding-bottom: 10px; border-bottom: 2px solid #dc3545;">📊 信号质量参数</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div class="info-item clean">
                            <strong>RSRQ</strong>
                            <label id="RsrqDiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong>RSRP</strong>
                            <label id="RsrpDiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong>SINR</strong>
                            <label id="SinrDiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong>RXLEV</strong>
                            <label id="RxlevDiv">--</label>
                        </div>
                        <!-- <div class="info-item clean">
                            <strong>BER</strong>
                            <label id="BerDiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong>RSCP</strong>
                            <label id="RscpDiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong>ECNO</strong>
                            <label id="EcnoDiv">--</label>
                        </div> -->
                    </div>
                </div>
                
                <!-- 系统信息分类 -->
                <div class="info-section" style="grid-column: 1 / -1;">
                    <h3 style="color: #495057; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding-bottom: 10px; border-bottom: 2px solid #6f42c1;">⚙️ 系统与连接信息</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px;">
                        <div class="info-item clean">
                            <strong id="ipLabel">本地IP</strong>
                            <label id="IPdiv">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="onlineTimeLabel">在线时长</strong>
                            <label id="OnlineTime">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="softwareVersionLabel">软件版本</strong>
                            <label id="SoftwareVersion">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="h3Wireless">WiFi状态</strong>
                            <label id="lWLANStatus">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="pWirelessNw">WiFi名称</strong>
                            <label id="lWirelessNwValue">--</label>
                        </div>
                        <div class="info-item clean">
                            <strong id="h3ConnDevice">连接设备</strong>
                            <label id="lConnDeviceValue">--</label>
                        </div>
                    </div>
                </div>

            <!-- 添加Ping测试结果显示区域 -->
            <div class="ping-results-row">
                <div id="pingResultsTitle" style="margin-bottom: 10px; font-weight: bold; color: #333; font-size: 14px;"></div>
                <div id="pingResultsContainer">
                    <!-- Ping结果将由JavaScript动态填充 -->
                    <div class="ping-result-placeholder" style="text-align: center; color: #999; padding: 10px; width: 100%;">
                        <span id="pingWaitingDataLabel"></span>
                    </div>
                </div>
            </div>
            

            </div>
            
            <img id="picdiv" src=""/>
        </div>
        <div class="mainColumn" id="mainColumn" >
            <div class="leftBar">
                <ul class="leftMenu" id="submenu"> </ul>
            </div>
            <div id="Content" class="content">
            </div>
            <br class="clear" /><br class="clear" />
        </div>
    </div>
</div>
<div id="mySidenav" class="sidenav">
  <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
  <div class="side-header">
    <ul id="side-menu" class="side-main-menu"></ul>
  </div>
  <div class="side-submenu-container">
    <ul id="phoneMenu"></ul>
  </div>
  <div class="side-footer">
    <span id="quickSetupSpan"></span>
  </div>
</div>
<div id="alertMB" style="display: none">
    <h1 id="lAlert"></h1>
    <a href="#" class="close" onclick="hm()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />
    <div align="center">
        <label id="lAlertMessage" class="lable12"></label>
        <br style="clear:both" />
        <div class="buttonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:center">
            <span class="btnWrp"><input id="btnModalOk" type="button"  value="OK" onclick="hm()" /></span>
        </div>
    </div>
</div>
<div id="mbox" style="display: none">
    <div class="" id="PleaseWait" >
        <div align="center">
            <br />
            <img src="images/PleaseWait.png" alt="..." class="loading" id="WaitPic" />
            <br />
            <label id="lPleaseWait"></label>
        </div>
    </div>
</div>
<script type="text/javascript">
    function displayControls() {
        document.getElementById("laUsername").innerHTML=jQuery.i18n.prop("laUsername");
        document.getElementById("laPassword").innerHTML=jQuery.i18n.prop("laPassword");
        document.getElementById("btnSignIn").value = jQuery.i18n.prop("btnSignIn");
        document.getElementById("lSignIn").innerHTML = jQuery.i18n.prop("lTitle");
    }
    
    // 更新电池信息显示
    function updateBatteryInfo() {
        try {
            // 从dashboard.js中获取电池信息，检查各种可能的ID
            // 电池电量信息
            var batteryInfo = "";
            var batteryPercentage = "";
            
            // 尝试从可能的ID获取电池信息
            if (document.getElementById("pDashBetteryInfo")) {
                batteryInfo = document.getElementById("pDashBetteryInfo").textContent;
            } else if (document.getElementById("设备的电池电量")) {
                batteryInfo = document.getElementById("设备的电池电量").textContent;
            }
            
            // 电池电压信息
            var batteryVoltage = "";
            if (document.getElementById("pDashBetteryVol")) {
                batteryVoltage = document.getElementById("pDashBetteryVol").textContent;
            }
            
            // 根据图片显示的信息，我们也查找这些可能的元素
            if (!batteryInfo && document.querySelector(".设备的电池电量")) {
                batteryInfo = document.querySelector(".设备的电池电量").textContent;
            }
            
            // 如果没有找到电池信息，尝试从"路由器"部分获取
            if (!batteryInfo) {
                var routerElements = document.querySelectorAll("div");
                for (var i = 0; i < routerElements.length; i++) {
                    if (routerElements[i].textContent.indexOf("设备的电池电量") > -1) {
                        var text = routerElements[i].textContent;
                        batteryInfo = text.substring(text.indexOf("设备的电池电量") + 7).trim();
                        break;
                    }
                }
            }
            
            // 解析电池电量
            var batteryLevel = "";
            var batteryLevelValue = 0;
            var isCharging = false;
            
            if (batteryInfo) {
                // 尝试提取百分比数字
                var regex = /(\d+)%/;
                var match = batteryInfo.match(regex);
                
                if (match && match[1]) {
                    batteryLevel = match[1] + "%";
                    batteryLevelValue = parseInt(match[1]);
                } else {
                    // 尝试直接获取数字
                    var numericMatch = batteryInfo.match(/(\d+)/);
                    if (numericMatch && numericMatch[1]) {
                        batteryLevel = numericMatch[1] + "%";
                        batteryLevelValue = parseInt(numericMatch[1]);
                    }
                }
                
                // 检查是否在充电
                isCharging = batteryInfo.indexOf("充电") > -1;
                
                // 更新电池电量显示
                document.getElementById("batteryLevel").textContent = batteryLevel || "未知";
                
                // 更新电池图标
                if (batteryLevel) {
                    var batteryLevelVisual = document.getElementById("batteryLevelVisual");
                    batteryLevelVisual.style.width = batteryLevel;
                    
                    // 根据电量设置不同颜色
                    batteryLevelVisual.className = "battery-level";
                    if (isCharging) {
                        batteryLevelVisual.classList.add("charging");
                    } else if (batteryLevelValue >= 60) {
                        batteryLevelVisual.classList.add("high");
                    } else if (batteryLevelValue >= 30) {
                        batteryLevelVisual.classList.add("medium");
                    } else if (batteryLevelValue > 0) {
                        batteryLevelVisual.classList.add("low");
                    }
                }
            } else {
                document.getElementById("batteryLevel").textContent = "unknown";
            }
            
            // 更新充电状态
            document.getElementById("chargingStatus").textContent = isCharging ? "充电中" : "未充电";
            

            
        } catch (error) {
        }
    }

    function initBatteryMonitor() {
        updateBatteryInfo();
        
        setInterval(updateBatteryInfo, 5000); // 5秒更新一次，确保能捕获到dashboard.js加载的数据
        
        if (document.getElementById("batteryLevel") && 
            document.getElementById("batteryLevel").textContent === "" || 
            document.getElementById("batteryLevel").textContent === "0%") {
            window.setDashboardBatteryInfo(0, false, '未知');
        }
    }
    
    document.addEventListener("DOMContentLoaded", function() {
        // 初始化i18n
        if (typeof jQuery !== 'undefined' && typeof jQuery.i18n !== 'undefined') {
            jQuery.i18n.properties({
                name: 'Messages',
                path: '../properties/',
                mode: 'map',
                language: 'cn', // 设置默认语言为中文
                callback: function() {
                    // 更新所有需要翻译的文本
                    updateStaticTextI18n();
                    // 更新电池信息
                    updateBatteryInfo();
                }
            });
        }
        
        window.setDashboardBatteryInfo(0, false, '未知');
        
        setTimeout(initBatteryMonitor, 500);
        
        if (typeof setupMobileInfoDropdown === 'function') {
            setupMobileInfoDropdown();
        }
    });
    
    window.addEventListener("load", function() {
        setTimeout(initBatteryMonitor, 2000);
        
        if (typeof setupMobileInfoDropdown === 'function') {
            setupMobileInfoDropdown();
        }
        
        checkMobileDevice();
    });
    
    function checkMobileDevice() {
        var isMobile = window.innerWidth <= 768;
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        var infoRow = document.querySelector('.info-row');
        
        if (isMobile && infoToggleBtn && infoRow) {
            infoToggleBtn.style.display = 'flex';
            infoRow.style.display = 'none';
        }
    }
    
    function setDashboardBatteryInfo(percentage, isCharging, voltage) {
        var batteryLevel = document.getElementById("batteryLevel");
        var chargingStatus = document.getElementById("chargingStatus");
        var batteryVoltage = document.getElementById("batteryVoltage");
        var batteryLevelVisual = document.getElementById("batteryLevelVisual");
        
        if (batteryLevel) {
            batteryLevel.textContent = percentage + "%";
        }
        
        if (chargingStatus) {
            chargingStatus.textContent = isCharging ? "充电中" : "未充电";
        }
        
        if (batteryVoltage) {
            batteryVoltage.textContent = voltage || "未知";
        }
        
        if (batteryLevelVisual) {
            batteryLevelVisual.style.width = percentage + "%";
            batteryLevelVisual.className = "battery-level";
            
            if (isCharging) {
                batteryLevelVisual.classList.add("charging");
            } else if (percentage >= 60) {
                batteryLevelVisual.classList.add("high");
            } else if (percentage >= 30) {
                batteryLevelVisual.classList.add("medium");
            } else {
                batteryLevelVisual.classList.add("low");
            }
        }
    }
</script>
<div id="mbox1" style="display: none">
    <div class="popUpBox popUpBox3" id="MBQuickSetup">
        <div class="pBoxCont" style="padding:10px 55px 10px 20px;">
            <h2 class="subtlt" id="QsText"></h2>
            <p id="QsText1"></p>
            <p id="QsText2"></p>
            <br />
            <br />
            <div align='center'>
                <span class="btnWrp">
                    <input type="button" id="btnQuickSetup" value="Quick Setup" onclick="hm(); quickSetup()" style="padding:3px 3px;"/>
                </span>
            </div>
            <br />
            <br />
            <br />
            <input id="chkSkip" type="checkbox" value="" class="chk" /><label id="QsText3" style="width:230px; color:#333; padding:2px 0 0 0;">Don't show Quick Setup in future.</label>
            <span class="btnWrp skip" style="margin-left: auto;margin-right: auto;"><input id="btnSkip" type="button" value="Skip" onclick="btnSkipQSClicked()" /></span>
            <br style="clear:both" />
            <label class="error" style="width:500px; margin-left:22px" id="QsText4"></label>
            <br style="clear:both" />
        </div>
    </div>
</div>
<div class="footer" style="display: none;">
    <div class="footerLft"></div>
    <div class="footerMast">
        <span class="logoTxt">ZYIoT</span>
        <span class="footerTxt"><label id="lVesrion"> </label></span>
    </div>
    <div class="footerRgt"></div>
</div>
<script>
    window.setDashboardBatteryInfo = function(percentage, isCharging, voltage) {
        try {
            percentage = percentage || 0;
            isCharging = !!isCharging; 
            voltage = voltage || '未知';
            
            if (typeof percentage === 'string') {
                percentage = parseInt(percentage) || 0;
            }
            
            if (typeof window.GlobalBatteryMonitor !== 'undefined' && window.GlobalBatteryMonitor.updateBatteryDisplay) {
                window.GlobalBatteryMonitor.updateBatteryDisplay(percentage, isCharging, voltage);
            }
            
            if (typeof updateHeaderBatteryInfo === 'function') {
                updateHeaderBatteryInfo(percentage, isCharging, voltage);
                return; 

            var batteryLevelDisplay = document.querySelector('.battery-info .info-value');
            if (batteryLevelDisplay) {
                batteryLevelDisplay.textContent = percentage + '%';
            }
            

            var batteryVoltageDisplay = document.querySelector('.voltage-info .info-value');
            if (batteryVoltageDisplay) {
                batteryVoltageDisplay.textContent = voltage;
            }
            
  
            var chargingStatusDisplay = document.querySelector('.charging-info .info-value');
            if (chargingStatusDisplay) {
                var chargingText = isCharging ? '充电中' : '未充电';

                if (typeof jQuery !== 'undefined' && jQuery.i18n && jQuery.i18n.prop) {
                    chargingText = isCharging ? jQuery.i18n.prop('lCharging') || '充电中' : jQuery.i18n.prop('lUncharged') || '未充电';
                }
                chargingStatusDisplay.textContent = chargingText;
            }
            

            var batteryLevelElement = document.querySelector('.battery-level');
            if (batteryLevelElement) {
                batteryLevelElement.style.width = percentage + '%';
                

                batteryLevelElement.className = 'battery-level';
                if (isCharging) {
                    batteryLevelElement.classList.add('charging');
                } else if (percentage > 60) {
                    batteryLevelElement.classList.add('high');
                } else if (percentage > 20) {
                    batteryLevelElement.classList.add('medium');
                } else {
                    batteryLevelElement.classList.add('low');
                }
            }
            
        } 
    }catch{}
    
    (function initBatteryDisplay() {
        setTimeout(function() {
            if (!document.querySelector('.battery-info .info-value') || 
                document.querySelector('.battery-info .info-value').textContent === '' ||
                document.querySelector('.battery-info .info-value').textContent === '0%') {
                window.setDashboardBatteryInfo(0, false, '未知');
            }
            
            if (typeof window.GlobalBatteryMonitor !== 'undefined' && !window.GlobalBatteryMonitor.initialized) {
                window.GlobalBatteryMonitor.init();
            }
        }, 500);
    })();
</script>
<!-- 引入性能优化器 -->
<script src="../js/base/performance_optimizer.js"></script>
<script src="../js/panel/dashboard.js"></script>
<script src="../js/battery-adapter.js"></script>
<script src="../js/global-battery-monitor.js"></script>

<!-- 立即执行的移动端检测初始化 -->
<script type="text/javascript">
    // 切换信息显示的函数
    function toggleInfoDisplay() {
        var infoRow = document.querySelector('.info-row');
        var phyCellInfoRow = document.querySelector('.phy-cell-info-row');
        var pingResultsRow = document.querySelector('.ping-results-row');
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        var systemInfoContent = document.getElementById('systemInfoContent');
        
        if (!infoRow || !infoToggleBtn) return;
        
        if (infoRow.classList.contains('open')) {
            infoRow.classList.remove('open');
            infoRow.style.display = 'none';   
            if (phyCellInfoRow) phyCellInfoRow.style.display = 'none'; 
            if (pingResultsRow) pingResultsRow.style.display = 'none';
            infoToggleBtn.classList.remove('open');
        } else {
            infoRow.classList.add('open');
            var isMobile = window.innerWidth <= 768;
            infoRow.style.display = isMobile ? 'block' : 'grid';
            if (phyCellInfoRow) phyCellInfoRow.style.display = isMobile ? 'block' : 'grid';
            if (pingResultsRow) pingResultsRow.style.display = 'block';
            infoToggleBtn.classList.add('open');
            
        }
    }
    
    // 初始化移动端设备信息显示 (确保事件绑定)
    function initMobileInfoToggle() {
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        if (infoToggleBtn) {
            infoToggleBtn.onclick = function() {
                toggleInfoDisplay();
            };
        }
    }
    
    (function() {
        var isMobile = window.innerWidth <= 768;
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        var infoRow = document.querySelector('.info-row'); 
        var phyCellInfoRow = document.querySelector('.phy-cell-info-row');
        var pingResultsRow = document.querySelector('.ping-results-row');
        
        if (isMobile && infoToggleBtn && infoRow) {
            infoToggleBtn.style.display = 'none'; 
            infoRow.style.display = 'block'; 
            if (phyCellInfoRow) phyCellInfoRow.style.display = 'block'; 
            if (pingResultsRow) pingResultsRow.style.display = 'block'; 
        } else if (!isMobile && infoToggleBtn && infoRow) {
            infoToggleBtn.style.display = 'none';
            infoRow.style.display = 'grid'; 
            if (phyCellInfoRow) phyCellInfoRow.style.display = 'grid'; 
            if (pingResultsRow) pingResultsRow.style.display = 'block'; 
        }
        
        initMobileInfoToggle(); 
        
        window.addEventListener('resize', function() {
            var isMobile = window.innerWidth <= 768;
            var isManuallyOpened = infoRow && infoRow.classList.contains('open');
            
            if (isMobile && infoToggleBtn && infoRow) {
                infoToggleBtn.style.display = 'none'; 
                
                infoRow.style.display = 'block';
                if (phyCellInfoRow) phyCellInfoRow.style.display = 'block';
                if (pingResultsRow) pingResultsRow.style.display = 'block';
            } else if (!isMobile && infoToggleBtn && infoRow) {
                infoToggleBtn.style.display = 'none';
                
                if (isManuallyOpened || !isManuallyOpened) { 
                    infoRow.style.display = 'grid';
                    if (phyCellInfoRow) phyCellInfoRow.style.display = 'grid';
                    if (pingResultsRow) pingResultsRow.style.display = 'block';
                }
            }
        });
    })();
</script>
<script>
    (function() {
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        var infoRow = document.querySelector('.info-row');
        
        if (infoToggleBtn) {
            infoToggleBtn.onclick = function() {
                if (typeof toggleInfoDisplay === 'function') {
                    toggleInfoDisplay();
                } else {
                    if (infoRow) {
                        if (infoRow.classList.contains('open')) {
                            infoRow.classList.remove('open');
                            infoRow.style.display = 'none';
                            infoToggleBtn.classList.remove('open');
                        } else {
                            infoRow.classList.add('open');
                            var isMobile = window.innerWidth <= 768;
                            infoRow.style.display = isMobile ? 'block' : 'grid';
                            infoToggleBtn.classList.add('open');
                        }
                    }
                }
            };
            
            infoToggleBtn.style.zIndex = "1000";
        }
        
        var isMobile = window.innerWidth <= 768;
        if (isMobile && infoToggleBtn && infoRow) {
            infoToggleBtn.style.display = 'none'; 
            infoRow.style.display = 'block'; 
        } else if (!isMobile && infoToggleBtn && infoRow) {
            infoToggleBtn.style.display = 'none';
            infoRow.style.display = 'grid';
        }
    })();
</script>

<!-- 添加设备信息区域显示控制脚本 -->
<script>
    // 确保在页面完全加载后初始化设备信息区域的显示状态
    (function() {
        window.addEventListener("load", function() {
            if (typeof initInfoRowVisibility === 'function') {
                
                var infoRow = document.querySelector('.info-row');
                var wasManuallyOpened = infoRow && infoRow.classList.contains('open');
                
                initInfoRowVisibility();
                
                if (wasManuallyOpened && infoRow) {
                    if (!infoRow.classList.contains('open')) {
                        infoRow.classList.add('open');
                    }
                    
                    var isMobile = window.innerWidth <= 768;
                    infoRow.style.display = isMobile ? 'block' : 'grid';
                    
                    var infoToggleBtn = document.getElementById('infoToggleBtn');
                    if (infoToggleBtn && !infoToggleBtn.classList.contains('open')) {
                        infoToggleBtn.classList.add('open');
                    }
                }
                setTimeout(function() {
                    var stillManuallyOpened = infoRow && infoRow.classList.contains('open');
                    
                    initInfoRowVisibility();
                    
                    if (stillManuallyOpened && infoRow) {
                        if (!infoRow.classList.contains('open')) {
                            infoRow.classList.add('open');
                        }
                        
                        var isMobile = window.innerWidth <= 768;
                        infoRow.style.display = isMobile ? 'block' : 'grid';
                        
                        var infoToggleBtn = document.getElementById('infoToggleBtn');
                        if (infoToggleBtn && !infoToggleBtn.classList.contains('open')) {
                            infoToggleBtn.classList.add('open');
                        }
                    }
                }, 1000);
            } else {
                
                var isDashboard = true; 
                
                var contentElement = document.getElementById('Content');
                if (contentElement && contentElement.children.length > 0 && 
                    contentElement.querySelector('.dashboard-container') === null) {
                    isDashboard = false;
                }
                
                var homepic = document.getElementById('homepic');
                var infoRow = document.querySelector('.info-row');
                var infoToggleBtn = document.getElementById('infoToggleBtn');
                var phyCellInfoRow = document.querySelector('.phy-cell-info-row');
                var pingResultsRow = document.querySelector('.ping-results-row');
                
                var isManuallyOpened = infoRow && infoRow.classList.contains('open');
                
                if (isDashboard) {
                    if (homepic) homepic.style.display = "block";
                    if (infoRow) {
                        if (window.innerWidth <= 768) {
                            if (infoToggleBtn) infoToggleBtn.style.display = "none"; 
                            
                            infoRow.style.display = "block"; 
                            if (phyCellInfoRow) phyCellInfoRow.style.display = "block";
                            if (pingResultsRow) pingResultsRow.style.display = "block";
                        } else {
                            if (infoToggleBtn) infoToggleBtn.style.display = "none";
                            
                            if (!isManuallyOpened) {
                                infoRow.style.display = "grid"; 
                                if (phyCellInfoRow) phyCellInfoRow.style.display = "grid";
                                if (pingResultsRow) pingResultsRow.style.display = "block";
                            }

                        }
                    }
                    

                } else {

                    if (homepic) homepic.style.display = "none";
                    if (infoRow) infoRow.style.display = "none";
                    if (infoToggleBtn) infoToggleBtn.style.display = "none";
                }
            }
        });
        

        var lastUrl = location.href; 
        new MutationObserver(function() {
            var url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                
                if (typeof initInfoRowVisibility === 'function') {

                    var infoRow = document.querySelector('.info-row');
                    var wasManuallyOpened = infoRow && infoRow.classList.contains('open');
                    

                    initInfoRowVisibility();
                    

                    if (wasManuallyOpened && infoRow) {
                        if (!infoRow.classList.contains('open')) {
                            infoRow.classList.add('open');
                        }
                        
                        var isMobile = window.innerWidth <= 768;
                        infoRow.style.display = isMobile ? 'block' : 'grid';
                                                var infoToggleBtn = document.getElementById('infoToggleBtn');
                        if (infoToggleBtn && !infoToggleBtn.classList.contains('open')) {
                            infoToggleBtn.classList.add('open');
                        }
                    }
                }
                
                setTimeout(function() {
                    var contentElement = document.getElementById('Content');
                    var isDashboard = true;
                    
                    if (contentElement && contentElement.children.length > 0 && 
                        contentElement.querySelector('.dashboard-container') === null) {
                        isDashboard = false;
                    }
                    
                }, 500);
            }
        }).observe(document, {subtree: true, childList: true});
    })();
</script>

<!-- 添加PrintSysInfo函数定义 -->
<script>
// 全局变量，用于跟踪PrintSysInfo的最后执行时间和执行状态
var lastPrintSysInfoTime = 0;
var isPrintSysInfoRunning = false;

function PrintSysInfo() {
    return new Promise((resolve, reject) => {
        if (isPrintSysInfoRunning) {
            resolve({status: "running"});
            return;
        }
        
        var now = new Date().getTime();
        if (now - lastPrintSysInfoTime < 30000) {
            resolve({status: "throttled"});
            return;
        }
        
        isPrintSysInfoRunning = true;
        
        if (typeof $ === 'undefined') {
            isPrintSysInfoRunning = false;
            reject("jQuery未加载");
            return;
        }
        
        if (typeof getAuthHeader !== 'function') {
            
            window.getAuthHeader = function() {
                return "";
            };
        }
        
        var host = window.location.protocol + "//" + window.location.host + "/";
        var url = host + 'xml_action.cgi?method=get&module=duster&file=firewall';
        
        
        $.ajax({
            type: "GET",
            url: url,
            dataType: "xml",
            async: true,
            timeout: 30000, // 30秒超时
            beforeSend: function(xhr) {
                try {
                    xhr.setRequestHeader("Authorization", getAuthHeader("GET"));
                } catch (e) {
                }
            },
            success: function(firewallContent) {
                
                if(firewallContent) {
                    try {
                        var arg = $(firewallContent).find("arg").text();
                        
                        if(arg === "0") {
                            var command = $(firewallContent).find("command").text();
                            
                            if (typeof parseCommand !== 'function') {
                                window.parseCommand = function(cmd) {
                                    return { command: cmd };
                                };
                            }
                            
                            if (typeof updateSysInfoDisplay !== 'function') {
                                window.updateSysInfoDisplay = function(info) {
                                };
                            }
                            
                
                            const sysInfo = parseCommand(command);
                            updateSysInfoDisplay(sysInfo);
                            

                            lastPrintSysInfoTime = new Date().getTime();
                            isPrintSysInfoRunning = false;
                            
                            resolve(sysInfo);
                        } else {
                            isPrintSysInfoRunning = false;
                            reject("自检失败, arg=" + arg);
                        }
                    } catch (e) {
                        isPrintSysInfoRunning = false;
                        reject("处理数据失败: " + e.message);
                    }
                } else {
                    isPrintSysInfoRunning = false;
                    reject("获取防火墙配置失败");
                }
            },
            error: function(xhr, status, error) {
                isPrintSysInfoRunning = false;
                reject("防火墙请求失败: " + status);
            }
        });
    });
}

// 移除自动执行代码，只保留函数定义
// 在dashboard页面切换时和点击菜单时才会调用

// 为Dashboard菜单项添加点击事件监听器
function initDashboardClickHandler() {
    var dashboardMenuItems = document.querySelectorAll('a[href*="dashboard"]');
    if (dashboardMenuItems.length > 0) {
        dashboardMenuItems.forEach(function(item) {
            item.addEventListener('click', function(e) {
                PrintSysInfo().catch(function(error) {
                });
            });
        });
    }
}

// 初始化事件监听
document.addEventListener("DOMContentLoaded", function() {
    
    setTimeout(initDashboardClickHandler, 1000);
    
});

// 监听URL变化
(function() {
    var lastUrl = location.href;
    new MutationObserver(function() {
        var url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            
            if (url.indexOf('dashboard') > -1) {
            }
        }
    }).observe(document, {subtree: true, childList: true});
})();
</script>

<!-- 添加自动选择面板接口区的脚本 -->
<script>
// 自动选择面板接口区功能
function selectDashboardMenu() {
    var currentUrl = window.location.href;
    if (currentUrl.indexOf('#mInternetConn') > -1 || 
        currentUrl.indexOf('#mWLAN') > -1 || 
        currentUrl.indexOf('#mUserData') > -1) {
        return;
    }
    
    
    try {
        var homepic = document.getElementById('homepic');
        var infoRow = document.querySelector('.info-row');
        
        if (homepic && infoRow && 
            (homepic.style.display === 'block' || homepic.style.display === 'flex') && 
            (infoRow.style.display === 'block' || infoRow.style.display === 'grid')) {
            return;
        }
        
        if (typeof createMenu === 'function') {
            createMenu(1); 

            var dashboardItem = document.getElementById('ant-menu-0');
            if (dashboardItem) {
                var allMenuItems = document.querySelectorAll('.ant-menu-item');
                allMenuItems.forEach(function(item) {
                    item.classList.remove("ant-menu-item-selected");
                });
                
                dashboardItem.classList.add("ant-menu-item-selected");
            }
            
            var sidenavItem = document.getElementById('sidenav-1');
            if (sidenavItem) {
                sidenavItem.classList.add('active');
            }
            
            
            if (homepic) homepic.style.display = "flex";
            if (infoRow) {
                if (window.innerWidth <= 768) {
                    var infoToggleBtn = document.getElementById('infoToggleBtn');
                    if (infoToggleBtn) infoToggleBtn.style.display = "flex";
                } else {
                    infoRow.style.display = "grid";
                }
            }
            
        } else {
        }
    } catch (e) {
    }
}

// 页面加载完成后自动选择面板接口区
document.addEventListener("DOMContentLoaded", function() {
    setTimeout(selectDashboardMenu, 1000);
    
    var lastUrl = location.href;
    new MutationObserver(function() {
        var url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            
            if (url.indexOf('#') === -1 || url.indexOf('#dashboard') > -1) {
                setTimeout(selectDashboardMenu, 500);
            }
        }
    }).observe(document, {subtree: true, childList: true});
    
    var refreshButton = document.getElementById('refreshBtn');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            selectDashboardMenu();
        });
    }
    
    var logoContainer = document.querySelector('.logo-container');
    if (logoContainer) {
        logoContainer.style.cursor = 'pointer';
        logoContainer.addEventListener('click', function() {
            selectDashboardMenu();
        });
    }
});

// 在窗口加载完成后再次执行，以防DOMContentLoaded事件已经触发
window.addEventListener('load', function() {
    setTimeout(selectDashboardMenu, 1500);
});
</script>

<!-- 添加强制应用CSS样式的脚本 -->
<script type="text/javascript">
    // 页面加载完成后执行，强制应用CSS样式
    document.addEventListener("DOMContentLoaded", function() {
        applyPingResultsStyles();
        
        setInterval(applyPingResultsStyles, 1000);
        
        function applyPingResultsStyles() {
            var pingResultsContainer = document.querySelector('#pingResultsContainer');
            if (pingResultsContainer) {
                pingResultsContainer.style.display = 'flex';
                pingResultsContainer.style.flexDirection = window.innerWidth <= 768 ? 'column' : 'row';
                pingResultsContainer.style.flexWrap = 'wrap';
                pingResultsContainer.style.gap = '8px';
                
                var resultItems = pingResultsContainer.querySelectorAll('.ping-result-item');
                resultItems.forEach(function(item) {
                    item.style.display = 'flex';
                    item.style.justifyContent = 'space-between';
                    item.style.alignItems = 'center';
                    item.style.padding = '10px';
                    item.style.backgroundColor = '#fff';
                    item.style.borderRadius = '6px';
                    item.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)';
                    item.style.border = '1px solid #e8e8e8';
                    item.style.cursor = 'pointer';
                    
                    if (window.innerWidth <= 768) {
                        item.style.flex = '1 1 100%';
                        item.style.maxWidth = '100%';
                        item.style.marginBottom = '8px';
                    } else {
                        item.style.flex = '1 1 45%'; 
                        item.style.minWidth = '300px';
                        item.style.maxWidth = 'calc(50% - 8px)'; 
                        item.style.marginBottom = '0';
                    }
                    
                    var urlEl = item.querySelector('.ping-result-url');
                    if (urlEl) {
                        urlEl.style.flex = '1';
                        urlEl.style.fontSize = '14px';
                        urlEl.style.color = '#333';
                        urlEl.style.marginRight = '8px';
                    }
                    
                    var statusEl = item.querySelector('.ping-result-status');
                    if (statusEl) {
                        statusEl.style.fontWeight = 'bold';
                        statusEl.style.padding = '4px 8px';
                        statusEl.style.borderRadius = '4px';
                        statusEl.style.fontSize = '13px';
                        statusEl.style.textAlign = 'center';
                        statusEl.style.minWidth = '60px';
                        statusEl.style.whiteSpace = 'nowrap';
                        
                        if (statusEl.classList.contains('ping-success')) {
                            statusEl.style.backgroundColor = '#e6f7e9';
                            statusEl.style.color = '#28a745';
                        } else if (statusEl.classList.contains('ping-fail')) {
                            statusEl.style.backgroundColor = '#fce8ea';
                            statusEl.style.color = '#dc3545';
                        }
                    }
                });
                
                var placeholder = pingResultsContainer.querySelector('.ping-result-placeholder');
                if (placeholder) {
                    placeholder.style.textAlign = 'center';
                    placeholder.style.color = '#999';
                    placeholder.style.padding = '10px';
                    placeholder.style.width = '100%';
                }
            }
        }
        
        window.addEventListener('resize', applyPingResultsStyles);
    });
</script>
<script>
function triggerSysInfo() {
    var btn = document.getElementById('sysInfoBtn');
    if (btn) {
        btn.disabled = true;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin" style="margin-right: 4px;"></i>诊断中...';
    }
    
    if (typeof PrintSysInfo === 'function') {
        PrintSysInfo().then(function() {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="fa fa-stethoscope" style="margin-right: 4px;"></i>系统诊断';
            }
        }).catch(function(error) {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="fa fa-exclamation-triangle" style="margin-right: 4px;"></i>诊断失败';
                setTimeout(function() {
                    btn.innerHTML = '<i class="fa fa-stethoscope" style="margin-right: 4px;"></i>系统诊断';
                }, 3000);
            }
        });
    } else {
        if (btn) {
            btn.disabled = false;
            btn.innerHTML = '<i class="fa fa-stethoscope" style="margin-right: 4px;"></i>系统诊断';
        }
    }
}
    }
</script>
</body>
