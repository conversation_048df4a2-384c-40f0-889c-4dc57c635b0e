<div class="content">
  <div class="form-section">
    <div class="form-group">
      <a href='#' id='inter_help' class='help' onclick="getHelp('InternetConnection')">&nbsp;</a>
      <form  id='adminform'>
        <label id="title" class="title"></label>
        <label id='lICS' class="title" style="display: none"> </label>
        <label id='lMICI'></label>
        <div id='selectpopup' style="display:block">
        <select id='micdropdown' onchange='micdropdownChanged()'>
            <option id='dropdownDisable' value='disabled'>Disabled</option>
            <option id='dropdownEnable' value='cellular'>Cellular</option>
        </select>
        </div>
        <br />
        <div id='workmode' style='display:none'>
        <label id='lWorkMode' ></label>
        <select id='WorkModeropdown' onchange='workmoderopdownChanged()'>
            <option id='dropdownMultimode' value='1'>4G/3G/2G multimode</option>
            <option id='dropdown4Gonly' value='2'>4G only</option>
            <option id='dropdown43Gonly' value='3'>4G/3G</option>
            <option id='dropdown42Gonly' value='8'>4G/2G</option>
            <option id='dropdown32Gonly' value='4'>3G/2G</option>        
            <option id='dropdown3Gonly' value='5'>3G only</option>
            <option id='dropdown2Gonly' value='6'>2G only</option>
            <option id='dropdownDisableNw' value='7'>disable NW setting</option>
            </select>
        </div>
        <div id='bootmode' style='display:none'>
         <label id='lBootMode' ></label>
         <select id='BootModeropdown' onchange='bootmoderopdownChanged()'>
            <option id='dropdown4GPre' value='1'>4G Preferred</option>
            <option id='dropdown3GPre' value='2'>3G Preferred</option>
            <option id='dropdown2GPre' value='7'>2G Preferred</option>
         </select>
        </div>    
         <div id='bootmode1' style='display:none'>
             <label id='lBootMode1' ></label>
             <select id='BootModeropdown1'>
                <option id='dropdown14GPre' value='3'>4G Preferred</option>
                <option id='dropdown13GPre' value='4'>3G Preferred</option>
             </select>
        </div>

        <div id='bootmode3' style='display:none'>
             <label id='lBootMode3' ></label>
             <select id='BootModeropdown3'>
                <option id='dropdown14GPre' value='9'>4G Preferred</option>
                <option id='dropdown2GPre' value='8'>2G Preferred</option>
             </select>
        </div>
        
         <div id='bootmode2' style='display:none'>
             <label id='lBootMode2' ></label>
             <select id='BootModeropdown2'>
                <option id='dropdown3GPreInBootMode2' value='5'>3G Preferred</option>
                <option id='dropdown2GPreInBootMode2' value='6'>2G Preferred</option>
             </select>
        </div>
        <div id='preferredLTEType' style='display:none'>
        <label id='lsetLikeLTEType'></label>
        <select id='setLikeLTETypedropdown'>
                 <option id='dropdownTDPre' value='0'>TD-LTE preferred</option>
                 <option id='dropdownFDDPre' value='1'>LTE FDD preferred</option>
         </select>
         </div>
          
        <div id='connectmode' style='display:block'>
            <label id="lCConnMode"></label>
            <select id='Cconndropdown'  onchange='conndropdownChanged()'>
                <option id='dropdown_auto' value='0'>Auto</option>
                <option id='dropdown_manual' value='1'>Manual</option>
            </select>
            <input type="checkbox" id="RoamingDisableAutoDialCheckBox" style="margin-left:12px"/><span id="lRoamingDisableAutoDialTip" style="margin-left:3px">disable auto dialup when roaming</span>
        </div>
        <div id='divMtu' style='display:block'>
            <label id="lMTULabel">MTU</label>        
            <input type="text" id="txtMtuValue" maxlength="4"/><em style="margin-left:10px;color:red;">(1000-1500)</em>
            <label id="lMtuInvalidTip" style="display: none;color:red;">MTU error</label> 
        </div>
        <div id='divAutoAPN' style='display:block'>
            <label id="lAutoAPNLabel" style='margin-top:6px'>Auto APN</label>
            <input type="checkbox" id="AutoConfigureAPNCheckBox" style="margin-right:8px"/><span id="lAutoConfigureAPNCheckBox" style="margin-left:3px">Auto configure APN</span>
        </div>

        <div id='divDialInRoaming' style='display:none'>
        <label  id="DialInRoamingLabel"></label>
        <select id='DialInRoamingSel'>
            <option id='EnabledDialInRoaming' value='0'>Enabled</option>
            <option id='DisabledDialInRoaming' value='1'>Disabled</option>
        </select>
        </div>
        
        <div id='divEngineeringModel' style='display:block'>
        <label  id="EngineeringModelLabel"></label>
        <select id='EngineeringModelSel' onchange='EngineeringModelChanged()'>
            <option id='CloseEngineeringModel' value='0'>Disabled</option>
            <option id='OpenEngineeringModel' value='1'>Enabled</option>
        </select>
        </div>
        <div id='divQueryTimeInterval' style='display:block'>
            <label id="queryTimeIntervalLabel">TimeInterval</label>        
            <input type="text" id="txtqueryTimeInterval" maxlength="4"/><em style="margin-left:10px;color:red;" id="queryTimeIntervalUnitLabel">min</em>
        </div>
       <br />

    <div id='Cellular_div' style='display: none'>
        <div id='PDP_default'>
            <label id='lPDP_default'>
            </label>
            <div id='p_PDP_default'>
                <input style='margin-right:8px' id='pPDPdef_chk' name='Default Primary PDP' type='checkbox' onclick='pPDPdefChange()'/>
                <label id='pPDPdef_lable' onmouseover='PDPColorChange("pPDPdef_lable")' onmouseout='PDPColorRestore("pPDPdef_lable")'
                    onclick='pPDPdefClick()' style="display: inline; color: #000;">
                    Default Primary PDP</label>            
                <br class="clear" />
            </div>
            <br />
        </div>
    </div>
         <div id='Popup_PDP' style="display: none">
            <div class="popUpBox popUpBox2" style='width:500px;' >
                <h1 id="h1Popup_PDP" style='width:450px;'></h1>
                <a href="#" class="close" onclick="btnCancel()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />
                <div class="pBoxCont" >
                    <label id="lRuleename"></label>
                    <input type="text" size="30" id="txtRulename" maxlength="25" disabled=""  readonly ="readonly"/>
                    <br style="clear:both" />
                    <div id="divApnNmae" style="display:none">
                    <label id="lAPN"></label>
                    <input type="text" size="30" id="txtAPNname" maxlength="25"/>
                    <br style="clear:both" />
                    <label id="lLteAPN"></label>
                    <input type="text" size="30" id="txtLteAPNname" maxlength="25"/>
                    <br style="clear:both" />
                    </div>
                     <div id='connnum_div' style='display:none'>
	                     <label id="lconnnum"></label>
	                    <input type="text" size="30" id="txtconnnum" maxlength="25"/>
	                    <br style="clear:both" />
	                    <label id="lpconnnum"></label>
	                    <input type="text" size="30" id="txtpconnnum" maxlength="25"/>
	                    <br style="clear:both" />
	                      <label id="lsecondary"></label>
	                    <input type="text" size="30" id="txtsecondary" maxlength="25"/>
	                    <br style="clear:both" />
	                     <label id="ldefault"></label>
	                    <input type="text" size="30" id="txtdefault" maxlength="25"/>
	                    <br style="clear:both" />
	                      <label id="lchkenable"></label>
	                    <input type="text" size="30" id="txtenable" maxlength="25"/>
	                    <br style="clear:both" />
                </div>
                <div id="divIpType" style="display:none">
		        <label id="lIPType"></label>
		        <select id='lIPTypedropdown' >
	        	    <option id='dropdown_IPV4V6' value='0'>IPV4V6</option>
		            <option id='dropdown_IPV4' value='1'>IPV4</option>
		            <option id='dropdown_IPV6' value='2'>IPV6</option>
		        </select>
		        <br style="clear:both" />
		        </div>
		        <div id ='lQOSEnbale'>
		        	 <label id="lQOSEnbaletitle">Enable QOS</label>		        	
		        	 <input id='1QOSEnablechk' type='checkbox' class='chk11' onchange='QOSEnbaleChange()'  onclick='QOSEnbaleChange()' style="width:20px;height:20px"/>
	  	  			 <br class="clear" />
		        </div>
		        <div id="lQOSEngine" style='display: none'>
			        <label id="lQOSQCI">QCI</label>
			        <input name="" type="text" id="txtQOSQCI" class="textfield"  onfocus='$("#qciCheckError").hide()'/> <strong>(number)</strong>			        
		        </div>
		        <br style="clear:both" />
		        <label id="l2G3GAuthType">2G3G Authentication Type</label>
		        <select id='Sel2G3GAuthType' onchange="f2G3GAuthTypeChanged()">
	        	    <option  value='NONE'>NONE</option>
		            <option  value='PAP'>PAP</option>
		            <option  value='CHAP'>CHAP</option>
		        </select>
		        <div id="div2G3GAuthEnabled">
		         <br style="clear:both" />
	                 <label id="l2G3GUser"></label>
	                <input type="text" size="30" id="txt2G3GUser" maxlength="25"/>
	                <br style="clear:both" />
	                  <label id="l2G3GPassword"></label>
	                <input type="text" size="30" id="txt2G3GPassword" maxlength="25"/>		        
		        </div>
		        <br style="clear:both" />
		        <label id="l4GAuthType">4G Authentication Type</label>
		        <select id='Sel4GAuthType' onchange="LteAuthTypeChanged()">
	        	    <option  value='NONE'>NONE</option>
		            <option  value='PAP'>PAP</option>
		            <option  value='CHAP'>CHAP</option>
		        </select>
		        <div id="div4GAuthEnabled">
		         <br style="clear:both" />
	                 <label id="l4GUser"></label>
	                <input type="text" size="30" id="txt4GUser" maxlength="25"/>
	                <br style="clear:both" />
	                  <label id="l4GPassword"></label>
	                <input type="text" size="30" id="txt4GPassword" maxlength="25"/>		        
		        </div>
		        <br style="clear:both" />
		        <label id="lErrorLogs" class="lable13" style="display: none"></label>
		        <label  id="qciCheckError" class="lable13" style="display: none">QCI  must be an integer and greater than zero </label>
                <div  class="buttonRow1">
                    <a href="#." id="btnCancel"  onclick="btnCancel()" class="cancel">Cancel</a>
                    <span class="btnWrp"><input id="btnOK_PDP" type="button" onclick="btnOKPDPSetting()" /></span>

                </div>
            </div>
         </div>
         </div>
         <div id='ManualNetwork_div' style='display: none'>
            <div>
                <label id='lMannualNetwork'></label>
                <select id='Networkdropdown'></select>
                <br /><br />
            </div>
         </div>

        <div id="MBMannualNetwork" style="display: none" >
            <div class="popUpBox popUpBox2" style='width:500px;' >
             <h1 id="h1MannualNetwork" style='width:450px;'></h1>
             <a href="#" class="close" onclick="btnCancelMannualNetwork()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />
            </div>
        </div>

    <div id='check2div' style='display: none' ><label id='lCustomDNS'>Custom DNS</label>
         <input id='check2' type='checkbox' onchange='check1Change()'  onclick='check1Change()' class="chk11"/>
            <label id='disptext3'  class="lable11 subttl"></label>
        <br class="clear" /></div>

    <div id='manual_network_check2div' style='display: none' ><label id='lManualNetworkStart'></label>
         <input id='manual_network_check2' type='checkbox' onchange='ManualNetworkChange()'  onclick='ManualNetworkChange()' class="chk11"/>
            <label id='dispmanualnetworktext'  class="lable11 subttl"></label>
        <br class="clear" /></div>
    <div id='BgScanNetwork' style='display: none'>
        <label id="lBgScanTime"></label>
        <select id='BgScanTimedropdown'  onchange='BgScanTimeDropdown()'>
                <option value='0'>Now</option>
                <option value='900'>15 minutes</option>
                <option value='1800'>30 minutes</option>
                <option value='3600'>60 minutes</option>
                <option value='disable'>Disable</option>
        </select></div>
        <div id='customeDNS' style='display: none'>
            <label id='lCustomeDNS1'></label><br class="clear clearCrm" /><div id="divCustomeDNS1"> </div><br class="clear" />

            <label id='lCustomeDNS2'></label><br class="clear clearCrm" /><div id="divCustomeDNS2"> </div><br class="clear" /><br class="clear" />
        </div>

      <div align='center'>
            <label class='error' id='lIPErrorMsg'  style='display: none'></label>
        </div>
        <div class="formBox" id="divFormBox"></div>
        <div align='right' id="divSaveButton"><span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='setData()' /></span>
        </div>
    </form>
  </div>
</div>


