/**
 * 主题切换器 - 蓝白配色主题
 * 包含登录界面增强功能
 */

/**
 * 主题切换和响应式布局管理JS
 */

// 主题切换功能
function switchTheme(theme) {
    var currentTheme = localStorage.getItem('theme') || 'blue-white';
    
    if (theme) {
        currentTheme = theme;
    } else {
        currentTheme = currentTheme === 'blue-white' ? 'default' : 'blue-white';
    }
    localStorage.setItem('theme', currentTheme);
    
    applyTheme(currentTheme);
    
    return currentTheme;
}

function applyTheme(theme) {
    var oldThemeLink = document.getElementById('theme-css');
    if (oldThemeLink) {
        oldThemeLink.remove();
    }
    
    if (theme === 'blue-white') {
        var link = document.createElement('link');
        link.id = 'theme-css';
        link.rel = 'stylesheet';
        link.href = 'css/blue-white-theme.css';
        document.head.appendChild(link);
    }
    
    updateThemeElements(theme);
}

function updateThemeElements(theme) {
    if (theme === 'blue-white') {
        enhanceLoginPage();
    } else {
        resetLoginPage();
    }
}

function enhanceLoginPage() {
    if (window.location.href.indexOf('index.html') > -1 || document.title.indexOf('Login') > -1) {
        var usernameInput = document.querySelector('input[name="Username"]');
        if (usernameInput) {
            usernameInput.value = 'admin';
            usernameInput.setAttribute('type', 'hidden');
            
            var usernameDiv = usernameInput.parentElement;
            if (usernameDiv && !document.querySelector('.user-avatar')) {
                var avatarDiv = document.createElement('div');
                avatarDiv.className = 'user-avatar';
                avatarDiv.innerHTML = '<i class="fa fa-user"></i>';
                
                var adminLabel = document.createElement('div');
                adminLabel.className = 'admin-label';
                adminLabel.textContent = 'Administrator';
                
                usernameDiv.appendChild(avatarDiv);
                usernameDiv.appendChild(adminLabel);
            }
        }
        
        var loginContainer = document.querySelector('.Box');
        if (loginContainer) {
            loginContainer.classList.add('login-container');
            loginContainer.style.background = 'linear-gradient(135deg, #bbdefb 0%, #1976d2 100%)';
        }
        
        var loginButton = document.querySelector('input.button');
        if (loginButton) {
            loginButton.classList.add('login-button');
        }
    }
}

function resetLoginPage() {
    if (window.location.href.indexOf('index.html') > -1 || document.title.indexOf('Login') > -1) {
        var usernameInput = document.querySelector('input[name="Username"]');
        if (usernameInput) {
            usernameInput.value = '';
            usernameInput.setAttribute('type', 'text');
            
            var avatarDiv = document.querySelector('.user-avatar');
            if (avatarDiv) avatarDiv.remove();
            
            var adminLabel = document.querySelector('.admin-label');
            if (adminLabel) adminLabel.remove();
        }
        
        var loginContainer = document.querySelector('.Box');
        if (loginContainer) {
            loginContainer.classList.remove('login-container');
            loginContainer.style.background = '';
        }
        
        var loginButton = document.querySelector('input.button');
        if (loginButton) {
            loginButton.classList.remove('login-button');
        }
    }
}

function adjustResponsiveLayout() {
    if (window.location.href.indexOf('adminApp.html') > -1 || document.getElementById('mainColumn')) {
        adjustInfoItems();
        
        adjustTopQuickLinks();
        
        setupMobileInfoDropdown();
    }
}

function setupMobileInfoDropdown() {
    var infoToggleBtn = document.getElementById('infoToggleBtn');
    var infoRow = document.querySelector('.info-row');
    
    if (!infoToggleBtn || !infoRow) return;
    
    if (window.innerWidth <= 768) {
        infoToggleBtn.style.display = 'flex';
        infoRow.classList.remove('open');
    } else {
        infoToggleBtn.style.display = 'none';
        infoRow.style.display = 'grid';
        infoRow.classList.remove('open');
    }
    
}

function adjustInfoItems() {
    var infoRow = document.querySelector('.info-row');
    if (!infoRow) return;
    
    var infoItems = infoRow.querySelectorAll('.info-item');
    if (!infoItems.length) return;
    
    if (window.innerWidth <= 768) {
    } else {
        infoRow.style.gridTemplateColumns = 'repeat(240px, 1fr)';
        infoRow.style.gridTemplateRows = 'repeat(3, auto)';
        infoRow.style.display = 'grid'; 
    }
}

function adjustTopQuickLinks() {
    var topQuickLinks = document.querySelector('.top-quick-links');
    if (!topQuickLinks) return;
    
    if (window.innerWidth <= 768) {
        topQuickLinks.style.position = 'relative';
        topQuickLinks.style.top = '0';
        topQuickLinks.style.right = '0';
        topQuickLinks.style.width = '90%';
        topQuickLinks.style.margin = '10px auto';
        topQuickLinks.style.textAlign = 'center';
    } else {
        topQuickLinks.style.position = 'absolute';
        topQuickLinks.style.top = '10px';
        topQuickLinks.style.right = '20px';
        topQuickLinks.style.width = 'auto';
        topQuickLinks.style.margin = '0';
        topQuickLinks.style.textAlign = 'left';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    var savedTheme = localStorage.getItem('theme') || 'blue-white';
    applyTheme(savedTheme);
    
    adjustResponsiveLayout();
    window.addEventListener('resize', adjustResponsiveLayout);
    
    setupMobileInfoDropdown();
});

window.addEventListener('load', function() {
    setTimeout(function() {
        adjustResponsiveLayout();
        setupMobileInfoDropdown();
    }, 100);
});

function initTheme() {
    var savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.body.className = savedTheme;
    } else {
        document.body.className = 'theme-blue';
        localStorage.setItem('theme', 'theme-blue');
    }
    
    if (document.querySelector('.login-container')) {
        enhanceLoginPage();
    }
    
}

function toggleTheme() {
    var currentTheme = document.body.className;
    
    document.body.className = 'theme-blue';
    localStorage.setItem('theme', 'theme-blue');
    
    return true;
}

function initThemeSwitcher() {
    document.addEventListener('DOMContentLoaded', function() {
        var themeToggleBtn = document.getElementById('themeToggle');
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', function() {
                toggleTheme();
            });
        }
        
        initTheme();
    });
} 