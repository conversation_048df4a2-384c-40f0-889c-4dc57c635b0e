<a href='#' class='help' onclick="getHelp('TimeSetting')">&nbsp;</a>

    <label id="title" class="title"></label>
    <div id= 'TimedateSetting' style='display:block'>
    <div class="formBox">
    <label id='lYear'></label>
    <input type='text' name='router_year'  maxlength="4" value='' id='tbrouter_year' class='textfield1' />
     <strong class="dot"> / </strong>
    <input type='text' name='router_month'  maxlength="2" value='' id='tbrouter_month' class='textfield1' />
     <strong class="dot"> / </strong>
    <input type='text' name='router_day'  maxlength="2" value='' id='tbrouter_day' class='textfield1' />
    <br style="clear:both" />
    <label id='lHour'></label>
    <input type='text' name='router_hour'  maxlength="2" value='' id='tbrouter_hour' class='textfield1' />
    <strong class="dot"> : </strong>
    <input type='text' name='router_minute'  maxlength="2" value='' id='tbrouter_minute' class='textfield1' />
    <strong class="dot"> : </strong>
    <input type='text' name='router_second'  maxlength="2" value='' id='tbrouter_second' class='textfield1' />
     <br style="clear:both" />
     <div>    
        <label id="lNtpStatus"></label>    
        <input type="radio" id="NtpEnabledStatus" name="NtpStatusRadio"/><span style="margin-right:40px;" id="lNtpEnabled"></span>
        <input type="radio" id="NtpDisabledStatus" name="NtpStatusRadio"/><span id="lNtpDisabled"></span>
        </div>
        
     <br style="clear:both" />
      <div align='center'>
        <label class='error' id='lTimeErrorLogs'  style='display: none'></label>
    </div> 
     </div>
     <div align="right">
      <span class="btnWrp">
      <input id="btSaveDateTime" type="button" value='Save Time' onclick='btnSaveDateandTime()' />
      </span>
      </div>  
     <br style="clear:both" />
     </div> 
     <!--<div id='NTPSetting' style='display:block'>
     <label id='lNTPServer' ></label>
      <input id="ipNTPControl_text" class='textNTPfield' type="text">
      <br style="clear:both" /></div>
      
           
      <div id='timezoneselction' style='display:block'>
      <label id='lManualTimeSetting' ></label>
       <span>   </span>
        <input id="SyncupwithNtpTimeChk" type="checkbox">
        <span id="SyncupwithNtpTimeTip" class = "subttl">I want to get NTP time when system start</span>
       <br style="clear:both" /> 
       <label id='lTimeZone' ></label>
       <select id='SelTimezonedropdown' style="width:350px">
        <option id='lselTZGMTEast12' value='+720'>GMT+12 Beijing</option>
        <option id='lselTZGMTEast11' value='+660'>GMT+11 Beijing</option>
        <option id='lselTZGMTEast10' value='+600'>GMT+10 Beijing</option>
        <option id='lselTZGMTEast9' value='+540'>GMT+9 Sapporo Osaka Seoul Tokyo yakutsk</option>
        <option id='lselTZGMTEast8' value='+480'>GMT+8 Beijing Chongqing Hongkong Urumqi</option>
        <option id='lselTZGMTEast7' value='+420'>GMT+7 Jakarta Bangkok Hanoi</option>
        <option id='lselTZGMTEast6' value='+360'>GMT+6 Astana Dhaka Ekaterinburg</option>
        <option id='lselTZGMTEast5' value='+300'>GMT+5 tashkent islamabad karachi</option>
        <option id='lselTZGMTEast4' value='+240'>GMT+4 Moscow Yerevan Tbilisi St.Petersburg Muscat</option>
        <option id='lselTZGMTEast3' value='+180'>GMT+3 Nairobi Kuwait Riyadh</option>
        <option id='lselTZGMTEast2' value='+120'>GMT+2 Cairo, Helsinki, kaliningrad, South Africa, Warsaw</option>
        <option id='lselTZGMTEast1' value='+60'> GMT+1 Berlin Brussels Copenhagen Madrid Paris Rome</option>
        <option id='lselTZGMT'      value='0 '> GMT Greenwich</option>
        <option id='lselTZGMTWest1' value='-60'>GMT-1 Dublin London Lisbon casablanca</option>
        <option id='lselTZGMTWest2' value='-120'>GMT-2 Ascencion Saint Helena</option>
        <option id='lselTZGMTWest3' value='-180'>GMT-3 Brasilia Buenos Aires Georgetown falkland-islands</option>
        <option id='lselTZGMTWest4' value='-240'>GMT-4 Atlantic time (Canada)  Caracas  la-paz4</option>
        <option id='lselTZGMTWest5' value='-300'>GMT-5 Eastern time(USA and Canada) Bogota  Lima  Quito</option>
        <option id='lselTZGMTWest6' value='-360'>GMT-6 Central time(USA and Canada) Mexico City</option>
        <option id='lselTZGMTWest7' value='-420'>GMT-7 mountain time(USA and Canada)  Arizona</option>
        <option id='lselTZGMTWest8' value='-480'>GMT-8 Pacific time(USA and Canada) Tijuana</option>
        <option id='lselTZGMTWest9' value='-540'>GMT-9 Alaska</option>
        <option id='lselTZGMTWest10' value='-600'>GMT-10 Hawaii</option>
        <option id='lselTZGMTWest11' value='-660'>GMT-11 Midway Islands  Samoa islands</option>
        <option id='lselTZGMTWest12' value='-720'>GMT-12 Eniwetok Kwajalein</option>     
        </select>
        
     <br style="clear:both" /></div>
       
      <span class="btnWrp">
        <input id="btUpdateNTPtime" type="button" value='Update Current Time Now' onclick='btnUpdateNtpTime()'></span> <span>&nbsp; &nbsp;&nbsp;</span><span id="UpdateTimeWaiting" style="color:red;display:none;" > Updating time,please waiting......</span>
     <br style="clear:both" />
      
     <div align='center' class="formBox">
        <label class='error' id='lTimeErrorLogs'  style='display: none'></label>
    </div>
    <label id="lTimeErrorLogs" class="lable13" style="display: none"></label>

      <div align='right'><span class="btnWrp"><input type='button' id='btUpdate_time' value='Save' onclick='btnTimeSetting()' />
     <br style="clear:both" /></div>-->
   
