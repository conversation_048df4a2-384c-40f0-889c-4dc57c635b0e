<div class="content">
  <div class="form-section">
    <div class="form-group">
      <a href='#' class='help' onclick="getHelp('WirelessMACFilters')">&nbsp;</a>
      <label id="title" class="title"></label>
      <div>
        <label id='lMF'></label>
        <div id='rdRadioMACFilters' onclick='showMACFilters()' class="inlineDiv"> </div>
      </div>
      <div id='mode_settings'>
        <label id='lMS'></label>
        <div id='rdRadioModeSettings' onclick='changeMode()' class="inlineDiv"> </div>
      </div>
      <div align='right' class="formBox"><span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='setData()' /></span></div>
      <br style="clear:both" />
      <div id='mac_filters'>
        <label id="lErrorLogs1" class="lable14" style="display: none"></label>
        <br style="clear:both" />
        <div style="float: right;"><span class="btnWrp"><input id="btnAddMACFilter" type="button" value="Add" onclick="addMACFilter()" /></span> </div>
        <label id="lDenyList"> </label>
        <table width="350px" id="tableMACFilters" border="0" cellspacing="0" cellpadding="0" class="dataTbl10 example table-stripeclass:alternate" style="margin-top: 5px">
          <thead>
            <tr>
              <th id="ltAllowDeny" width="330px"></th>
              <th width="20px" id="lListCheckall" name="DeleteAll"></th>
            </tr>
          </thead>
          <tbody>
          </tbody>
        </table>
        <div id='DeleteListdiv' align='right' style='display: none'><span class="btnWrp"><input type='button' id='btUpdate_Delete' value='Delete' onclick='DeleteList()' /></span>
        </div>
        <div id="DeleteListConfigure" style="display: none" >
          <div class="popUpBox popUpBox2" style='width:400px;' >
            <h1 id="h1dellistConfigure" style='width:350px;'></h1>
            <a href="#" class="close" onclick="btnCancelDeleteListConfigure()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />
            <p style='margin-left:10px; margin-right:10px; padding: 5px'> <font id="lDeleteListMessage" color="#FF0000" size="+1">Do you want to delete the MAC lists selected?
                 </font>
            </p>
            <div class="pBoxCont" >
              <div  class="buttonRow1">
                <a href="#." id="btnCancel"  onclick="btnCancelDeleteListConfigure()" class="cancel">Cancel</a>
                <span class="btnWrp"><input id="btnOK_confirm" type="button"  value="Confirm" onclick="btnDeleteListOKConfigure()" /></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="MBMACFilterDlg" style="display: none" >
        <div class="popUpBox popUpBox2" style='width:500px' >
          <h1 id="h1AddMACFilter"   style='width:450px;'></h1>
          <a href="#" class="close" onclick="btnCancelClickedMACFilters()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />
          <div class="pBoxCont">
            <label id="lMACAddress"></label>
            <div class="boxCont2">  <input name="input" type="text" size="10" id="txtMac1" class="sml" maxlength="2" onkeyup='setFocus("txtMac1")'/> <strong>&middot;</strong>
              <input name="input" type="text" size="10" id="txtMac2" class="sml" maxlength="2" onkeyup='setFocus("txtMac2")'/> <strong>&middot;</strong>
              <input name="input" type="text" size="10" id="txtMac3" class="sml" maxlength="2" onkeyup='setFocus("txtMac3")'/> <strong>&middot;</strong>
              <input name="input" type="text" size="10" id="txtMac4" class="sml" maxlength="2" onkeyup='setFocus("txtMac4")'/> <strong>&middot;</strong>
              <input name="input" type="text" size="10" id="txtMac5" class="sml" maxlength="2" onkeyup='setFocus("txtMac5")'/> <strong>&middot;</strong>
              <input name="input" type="text" size="10" id="txtMac6" class="sml" maxlength="2"/>
              <br style="clear:both" /></div>
            <label id="lMacError" class="lable13" style="display: none"></label>
            <div class="bottonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:right" >
              <a href="#." id="btnCancel" onclick="btnCancelClickedMACFilters()" class="cancel">Cancel</a>
              <span class="btnWrp"><input id="btnAddMACFilter" type="button"  value="Add" onclick="btnAddClickedMACFilters()" /></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

