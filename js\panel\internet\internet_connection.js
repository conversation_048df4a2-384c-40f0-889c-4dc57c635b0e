var flag_hide = 1;
var cellular_save = 0;
var _WiFiIntervalSelectID;
var _WiFiConnInterval = 5000;
var PF_INDEX = 1;
var PF_DELETE_INDEX = 0;
var PDPCheckFlags = new Array(6);
var InternetconnectFlag;
var NetworkModeflag;
var bDisabledAutoDialInRoam = false;
var ConnectionModeflag;
var Preferred_NetworkModeflag;
var Preferred_LTETypeflag;
var mtuValue;
var gdAutoConfAPN = false;
var gEngineerModel = 0;
var gEngineerQueryTimeInterval;
var gDialInRoaming = 0;

(function($) {

    $.fn.objInternetConn = function(InIt) {
        var ip_divCustomeDNS1;
        var ip_divCustomeDNS2;
        var rdRadioMode;
        var network_mode;
        var prefer_bootmode;
        var prefer_bootmode1;
        var prefer_bootmode2;
		var prefer_bootmode3;
        var xmlName = '';
        var controlMapExisting = new Array(0);
        var PDPMapExisting = new Array(0);
        var controlMapCurrent = new Array(0);
        var arrayISPProvider = new Array(0);
        var arrayTF1Provider = new Array(0);
        var arrayTF2Provider = new Array(0);
        var arrayTF3Provider = new Array(0);
        var arrayTF4Provider = new Array(0);
        var arrayMannualNetwork = new Array(0);
        var arrayDusterNetwork = new Array(0);
        var pre_NW_mode = "0";
        var pre_prefer_bootmode = "1";
        var pre_prefer_bootmode1 = "3";
        var pre_prefer_bootmode2 = "5";
		var pre_prefer_bootmode3 = "7";
        var prefer_lte_type = '';
        var pre_prefer_lte_type = '';
        var bAutoSwitchFlag = false;

        for (var i = 0; i < PDPCheckFlags.length; i++) {
            PDPCheckFlags[i] = 0;
        }


        var active_isp = '';
        var indexWN = 0;
        _arrayWirelessNws = new Array(0);

        this.onLoad = function(flag) {

            if (flag) {
                this.loadHTML();
                buttonLocaliztion("btUpdate");
                this.addIPBoxes();

                $("#RoamingDisableAutoDialCheckBox").click(function() {
                    if ($(this).attr("checked")) {
                        $("#Cconndropdown").attr("disabled", true);

                    } else {
                        $("#Cconndropdown").attr("disabled", false);
                    }
                });

                $("#txtMtuValue").focus(function() {
                    $("#lMtuInvalidTip").hide();
                });
            }

            var titleElement = document.getElementById("title");
            if (titleElement) {
                titleElement.innerHTML = jQuery.i18n.prop(InIt);
            }
            var errorElement = document.getElementById('lIPErrorMsg');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
            lableLocaliztion(document.getElementsByTagName("label"));
            lableLocaliztion(document.getElementsByTagName("span"));
            lableLocaliztion(document.getElementsByTagName("em"));
            pElementLocaliztion(document.getElementsByTagName("option"));

            this.dispalyAllNone();
            this.clearControlArray();

            var selectPopup = document.getElementById("selectpopup");
            if (selectPopup) {
                selectPopup.style.display = "block";
            }
            $("#lAutoConfigureAPNCheckBox").text(jQuery.i18n.prop("lAutoConfigureAPNCheckBox"));
            clearInterval(_WiFiIntervalID);
            xml = getData(xmlName);


            connectedmode = $(xml).find("connect_mode").text();
            bDisabledAutoDialInRoam = ($(xml).find("Roaming_disable_auto_dial").text() == "1") ? true : false;
            $("#RoamingDisableAutoDialCheckBox").attr("checked", bDisabledAutoDialInRoam);
            mtuValue = $(xml).find("mtu").text();
            gdAutoConfAPN = ($(xml).find("auto_apn").text() == "1") ? true : false;
            $("#AutoConfigureAPNCheckBox").attr("checked", gdAutoConfAPN);
            $("#txtMtuValue").val(mtuValue);
            var connDropdown = document.getElementById('Cconndropdown');
            if (connDropdown) {
                connDropdown.value = connectedmode;
            }
            ConnectionModeflag = connectedmode;
            proto = $(xml).find("proto").text();
            manual_network_check = $(xml).find("manual_network_start").text();
            var manualNetworkCheck = document.getElementById("manual_network_check2");
            if (manualNetworkCheck) {
                if (manual_network_check == '1' || manual_network_check == '2')
                    manualNetworkCheck.checked = true;
                else
                    manualNetworkCheck.checked = false;
            }
		
	    gEngineerModel = $(xml).find("Engineering_mode").text();
            gEngineerQueryTimeInterval = $(xml).find("query_time_interval").text();
            $("#EngineeringModelSel").val(gEngineerModel);
			$("#txtqueryTimeInterval").val(gEngineerQueryTimeInterval);
      

            gVersion = $(xml).find("version_flag").text();
            var IsSwitchDisplay =  $(xml).find("disable_switch").text();

            //hide auto switch interface in LWG-only and LTG-only modes
	    
	    	gDialInRoaming = $(xml).find("Roaming_disable_dial").text();
			$("#DialInRoamingSel").val(gDialInRoaming);

            var micDropdown = document.getElementById("micdropdown");
            if (micDropdown) {
                micDropdown.value = proto;
            }
            InternetconnectFlag = proto;
            if (manual_network_check == '2')
                proto = 'manual_network';
            switch (proto) {
                case 'cellular': 
                    {
                        $("#divEngineeringModel").show();
						if (1 == gEngineerModel)
						{
                        	$("#divQueryTimeInterval").show();
						}
                        document.getElementById("Cellular_div").style.display = "block";
                        document.getElementById("connectmode").style.display = "block";
                        document.getElementById("divMtu").style.display = "block";
                        // document.getElementById("divAutoAPN").style.display = "none";
                        document.getElementById("workmode").style.display = "block";
                        document.getElementById("lWorkMode").innerHTML = jQuery.i18n.prop("lWorkMode");
                        network_mode = $(xml).find("NW_mode").text();
                        document.getElementById('WorkModeropdown').value = network_mode;
                        NetworkModeflag = network_mode;
                        pre_NW_mode = network_mode;
                        prefer_lte_type = $(xml).find("prefer_lte_type").text();
                        pre_prefer_lte_type = prefer_lte_type;
                        Preferred_LTETypeflag = prefer_lte_type;
                        if (network_mode == '1') {
                            document.getElementById("bootmode").style.display = "block";
                            document.getElementById("lBootMode").innerHTML = jQuery.i18n.prop("lBootMode");
                            prefer_bootmode = $(xml).find("prefer_mode").text();
                            pre_prefer_bootmode = prefer_bootmode;
                            document.getElementById('BootModeropdown').value = prefer_bootmode;
                            Preferred_NetworkModeflag = prefer_bootmode;
                        } else
                            document.getElementById("bootmode").style.display = "none";

                    if (network_mode == '3') {
                        document.getElementById("bootmode1").style.display = "block";
                        document.getElementById("lBootMode1").innerHTML = jQuery.i18n.prop("lBootMode1");
                        prefer_bootmode1 = $(xml).find("prefer_mode").text();
                        pre_prefer_bootmode1 = prefer_bootmode1;
                        if (prefer_bootmode1 != '4')
                            prefer_bootmode1 = '3';
                        document.getElementById('BootModeropdown1').value = prefer_bootmode1;
                        Preferred_NetworkModeflag = prefer_bootmode1;
                    } else
                        document.getElementById("bootmode1").style.display = "none";
					

                    if (network_mode == '4') {
                        document.getElementById("bootmode2").style.display = "block";
                        document.getElementById("lBootMode2").innerHTML = jQuery.i18n.prop("lBootMode2");
                        prefer_bootmode2 = $(xml).find("prefer_mode").text();
                        pre_prefer_bootmode2 = prefer_bootmode2;
                        if (prefer_bootmode2 != '6')
                            prefer_bootmode2 = '5';
                        document.getElementById('BootModeropdown2').value = prefer_bootmode2;
                        Preferred_NetworkModeflag = prefer_bootmode2;
                    } else
                        document.getElementById("bootmode2").style.display = "none";

					if (network_mode == '8') {
                        document.getElementById("bootmode3").style.display = "block";
                        document.getElementById("lBootMode3").innerHTML = jQuery.i18n.prop("lBootMode3");
                        prefer_bootmode3 = $(xml).find("prefer_mode").text();
                        pre_prefer_bootmode3 = prefer_bootmode3;
                        if (prefer_bootmode3 != '8')
                            prefer_bootmode3 = '9';
                        document.getElementById('BootModeropdown3').value = prefer_bootmode3;
                        Preferred_NetworkModeflag = prefer_bootmode3;
                    } else
                        document.getElementById("bootmode3").style.display = "none";

                    if ((network_mode != '5') && (network_mode != '4')&&(network_mode != '6')) {
                        document.getElementById("preferredLTEType").style.display = "block";
                        document.getElementById("lsetLikeLTEType").innerHTML = jQuery.i18n.prop("lsetLikeLTEType");
                        document.getElementById('setLikeLTETypedropdown').value = prefer_lte_type;

                    } else
                        document.getElementById("preferredLTEType").style.display = "none";
                    //this.loadCellularData(false);
                    this.loadPDPData(false);
                    break;
                }

                case 'disabled': {
                    this.loadDisabledData();
                    break;
                }
            }
            this.copyControlArray();
            

        }
        this.addIPBoxes = function() {
            ip_divCustomeDNS1 = $("#divCustomeDNS1").ip_address("divCustomeDNS1");
            ip_divCustomeDNS2 = $("#divCustomeDNS2").ip_address("divCustomeDNS2");
        }
        this.clearIPBoxes = function() {
            ip_divCustomeDNS1 = null;
            document.getElementById("divCustomeDNS1").innerHTML = "";
            ip_divCustomeDNS2 = null;
            document.getElementById("divCustomeDNS2").innerHTML = "";
        }
        this.dispalyAllNone = function() {
            var elements = [
                "Cellular_div", "lIPErrorMsg", "workmode", "bootmode", 
                "bootmode1", "bootmode2", "bootmode3", "connectmode", "divMtu"
            ];
            
            elements.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    element.style.display = "none";
                }
            });
            
            $("#divEngineeringModel").hide();
            $("#divQueryTimeInterval").hide();
        }
        this.clearControlArray = function() {
            controlMapExisting = null;
            controlMapCurrent = null;
            controlMapExisting = new Array(0);
            controlMapCurrent = new Array(0);
            arrayTF1Provider = null;
            arrayTF2Provider = null;
            arrayTF3Provider = null;
            arrayTF4Provider = null;
            arrayTF1Provider = new Array(0);
            arrayTF2Provider = new Array(0);
            arrayTF3Provider = new Array(0);
            arrayTF4Provider = new Array(0);
        }

        this.MtuValid = function() {
            var strMtuValue = $("#txtMtuValue").val();
            var r = /^\d{4}$/;
            var ret = r.test(strMtuValue);
            if (ret) {
                var value = parseInt(strMtuValue);
                if (value < 1000 || value > 1500) {
                    ret = false;
                }
            }
            return ret;
        }

        this.onPost = function(flag) {
            //enable version switch in LTG and LWG modes 

            if (this.isValid()) {
                document.getElementById('lIPErrorMsg').style.display = 'none';
                var dropdownvalue = document.getElementById("micdropdown").value;
                if (dropdownvalue == 'manual_network')
                    SelectNetworkChanged();
                else if (dropdownvalue == 'cellular') {
                    if (!this.MtuValid()) {
                        $("#lMtuInvalidTip").show();
                        return;
                    }
                    var _controlMap = this.getPostData();
                    for (var i = 0; i < _controlMap.length; i++) {
                        if (0 == i) {
                            if (PDPCheckFlags[i] != document.getElementById('pPDPdef_chk').checked) {
                                postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(_controlMap[i])));
                            }
                        }
						if (1 == i)
							postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(_controlMap[i])));
                    }
                } else {
                	if("disabled" != InternetconnectFlag)
                	{			
            			var mapData = new Array();
            			mapData = putMapElement(mapData,"RGW/wan/proto", "disabled", 0);
						postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
					}
                   
                }
            }  
        }
		
        this.onPostSuccess = function() {

            this.onLoad(false);           
        }
        this.isValid = function() {
            var protoData = document.getElementById("micdropdown").value;
            if (protoData == 'static') {
                if (!(ip_Gateway_Address.validIP(true) && ip_IP_Address.validIP(true) && ip_Mask.validIP(true) && ip_Primary_DNS_Address.validIP(true) && ip_Secondary_DNS_Address.validIP(false))) {
                    document.getElementById('lIPErrorMsg').style.display = 'block';
                    document.getElementById('lIPErrorMsg').innerHTML = jQuery.i18n.prop('lIPErrorMsg');
                    return false;
                }
            }

            return true;

        }
        this.getPostData = function() {
            var prototype = document.getElementById("micdropdown").value;
            switch (prototype) {
                case 'cellular': {
                    cellular_save = 1;
                    flag_hide = 1;
                    return this.getCellularPostData();
                    break;
                }
                case 'disabled': {
                    flag_hide = 1;
                    cellular_save = 1;
                    return this.getDisabledPostData();
                    break;
                }
            }
        }
        this.setXMLName = function(_xmlname) {
            xmlName = _xmlname;
        }
        this.loadHTML = function() {
            var contentElement = document.getElementById('Content');
            if (contentElement) {
                contentElement.innerHTML = "";
                try {
                    var htmlContent = callProductHTML("html/internet/internet_connection.html");
                    if (htmlContent && htmlContent.length > 0) {
                        contentElement.innerHTML = htmlContent;
                    } else {
                        contentElement.innerHTML = "<div class='error-message'>无法加载页面内容</div>";
                    }
                } catch (e) {
                    contentElement.innerHTML = "<div class='error-message'>页面加载失败: " + e.message + "</div>";
                }
            } else {
            }
        }
        this.updateIndex = function() {
            return index++;
        }
        this.loadManualNetwork = function(flag) {

            var Network_name = "";
            var duster_name = "";
            var arrayindex;
            var VarMannualNetwork;
            //document.getElementById("ManualNetwork_div").style.display = "block";
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, 0, "RGW/wan/proto", proto);
            VarMannualNetwork = $(xml).find("plmm_name").text();
            // arrayDusterNetwork.push(VarMannualNetwork);
            $(xml).find("cellular").each(function() {
                $(this).find("mannual_network_list").each(function() {
                    $(this).find("Item").each(function() {
                        arrayDusterNetwork[arrayindex] = new Array(1);
                        arrayDusterNetwork[arrayindex][0] = $(this).find("plmm_name").text();
                        duster_name = $(this).find("plmm_name").text();
                        arrayindex++;
                        switch (duster_name) {
                            case "0":
                                Network_name = 'CMCC 2G';
                                break;
                            case "1":
                                Network_name = 'CMCC 2G C';
                                break;
                            case "2":
                                Network_name = 'CMCC 3G';
                                break;
                            case "3":
                                Network_name = 'CMCC 2G (EDGE)';
                                break;
                            case "4":
                                Network_name = 'CMCC 3G(HSDPA)';
                                break;
                            case "5":
                                Network_name = 'CMCC 3G(HSUPA)';
                                break;
                            case "6":
                                Network_name = 'CMCC 3G(HSDPA+HSUPA)';
                                break;
                            case "7":
                                Network_name = 'CMCC 3G(LTE)';
                                break;
                            case "8":
                                Network_name = 'CUCC 2G';
                                break;
                            case "9":
                                Network_name = 'CUCC 2G C';
                                break;
                            case "10":
                                Network_name = 'CUCC 3G';
                                break;
                            case "11":
                                Network_name = 'CUCC 2G (EDGE)';
                                break;
                            case "12":
                                Network_name = 'CUCC 3G(HSDPA)';
                                break;
                            case "13":
                                Network_name = 'CUCC 3G(HSUPA)';
                                break;
                            case "14":
                                Network_name = 'CUCC 3G(HSDPA+HSUPA)';
                                break;
                            case "15":
                                Network_name = 'CUCC 3G(LTE)';
                                break;
                            case "16":
                                Network_name = 'CTM 2G';
                                break;
                            case "17":
                                Network_name = 'CTM 2G C';
                                break;
                            case "18":
                                Network_name = 'CTM 3G';
                                break;
                            case "19":
                                Network_name = 'CTM 2G (EDGE)';
                                break;
                            case "20":
                                Network_name = 'CTM 3G(HSDPA)';
                                break;
                            case "21":
                                Network_name = 'CTM 3G(HSUPA)';
                                break;
                            case "22":
                                Network_name = 'CTM 3G(HSDPA+HSUPA)';
                                break;
                            case "23":
                                Network_name = 'CTM 3G(LTE)';
                                break;
                        }
                        var opt = document.createElement("option");
                        opt.text = Network_name;
                        opt.value = duster_name;
                    });
                });
            });

        }
        this.loadCellularData = function(flag) {
            var indexLoad = 1;
            var indexISP = 0;
            var indexNetwork = 0;
            return;
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, 0, "RGW/wan/proto", proto);

            var arrayLabels = document.getElementsByTagName("th");
            lableLocaliztion(arrayLabels);
            arrayLabels = document.getElementsByTagName("td");
            lableLocaliztion(arrayLabels);
            arrayLabels = document.getElementsByTagName("h1");
            lableLocaliztion(arrayLabels);

            document.getElementById("Cellular_div").style.display = "none";
            //   document.getElementById("check2div").style.display = "none";

			//JS_DELTE_ME
            //document.getElementById("WiFi_div").style.display = "none";
            document.getElementById("ManualNetwork_div").style.display = "none";
            var cdns1 = $(xml).find("cdns1").text();
            var cdns2 = $(xml).find("cdns2").text();
            var cdns_enable = $(xml).find("cdns_enable").text();

            if (cdns_enable == '1') {
                document.getElementById("check2").checked = true;
                document.getElementById("customeDNS").style.display = "block";
            } else
                document.getElementById("check2").checked = false;
            ip_divCustomeDNS1.setIP(cdns1);
            ip_divCustomeDNS2.setIP(cdns2);

            var ISP_name = "";
            var bgscan_value = "";
            $(xml).find("cellular").each(function() {
                bgscan_value = $(this).find("bgscan_time").text();
                $(this).find("isp_supported_list").each(function() {
                    $(this).find("Item").each(function() {
                        ISP_name = $(this).find("ISP").text();
                        arrayISPProvider[indexISP] = new Array(10);
                        arrayISPProvider[indexISP][0] = ISP_name;
                        var opt = document.createElement("option");
                        document.getElementById("Profiledropdown").options.add(opt);
                        opt.text = ISP_name;
                        opt.value = ISP_name;
                        arrayISPProvider[indexISP][1] = $(this).find("uname").text();
                        arrayISPProvider[indexISP][2] = $(this).find("pswd").text();
                        arrayISPProvider[indexISP][3] = $(this).find("baud").text();
                        arrayISPProvider[indexISP][4] = $(this).find("int1").text();
                        arrayISPProvider[indexISP][5] = $(this).find("int2").text();
                        arrayISPProvider[indexISP][6] = $(this).find("num").text();
                        arrayISPProvider[indexISP][7] = $(this).find("auth").text();
                        arrayISPProvider[indexISP][8] = $(this).find("connmode").text();
                        arrayISPProvider[indexISP][9] = $(this).find("idl").text();
                        indexISP++;
                    });
                });
            });

            if (arrayISPProvider[indexISP])
                delete (arrayISPProvider[indexISP]);

            this.loadTableData(arrayISPProvider);

            var ispValue = "";
            var init1 = "";
            var l = "";
            var username = "";
            var password = "";
            var value = 0;
            var nw_mode = $(xml).find("NW_mode").text();
            var pre_mode = $(xml).find("prefer_mode").text();
            var lte_type = $(xml).find("prefer_lte_type").text();

            ispValue = $(xml).find("ISP_name").text();

            document.getElementById("Profiledropdown").value = ispValue;
            document.getElementById("BgScanTimedropdown").value = bgscan_value;

            for (i = 0; i < arrayISPProvider.length; i++) {
                if (ispValue == arrayISPProvider[i][0])
                    value = i;
            }

            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/ISP_name", arrayISPProvider[value][0]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/username", arrayISPProvider[value][1]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/password", arrayISPProvider[value][2]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/baudrate", arrayISPProvider[value][3]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/init1", arrayISPProvider[value][4]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/init2", arrayISPProvider[value][5]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/accessnumber", arrayISPProvider[value][6]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/advanced/auth_type", arrayISPProvider[value][7]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/advanced/connectmode", arrayISPProvider[value][8]);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/advanced/idle", arrayISPProvider[value][9]);

            if (document.getElementById("check2").checked) {
                controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cdns_enable", "1");
            } else
                controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cdns_enable", "0");
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cdns1", ip_divCustomeDNS1.getIP());
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cdns2", ip_divCustomeDNS2.getIP());
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/bgscan_time", bgscan_value);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/NW_mode", nw_mode);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/prefer_mode", pre_mode);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/NW_mode_action", "0");
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/prefer_mode_action", "0");
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/prefer_lte_type", lte_type);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, indexLoad++, "RGW/wan/cellular/prefer_lte_type_action", "0");
        }

        this.loadPDPDataToBox = function(index) {
            var length = arrayISPProvider.length;
            var arrayTableDataRow;
            localizePDPProfile();
            if (length > index) {
                arrayTableDataRow = arrayISPProvider[index];
                var Rulename = arrayTableDataRow[0];
                var ConnNum = arrayTableDataRow[1];
                var pConnNum = arrayTableDataRow[2];
                var Enable = arrayTableDataRow[3];
                var ConnType = arrayTableDataRow[4];
                var IsDefault = arrayTableDataRow[5];
                var IsSecondary = arrayTableDataRow[6];
                var APNname = arrayTableDataRow[7];
                var IPType = arrayTableDataRow[8];
                var QCI = arrayTableDataRow[9];
                var HasTFT = arrayTableDataRow[10];
                var LteAPNname = arrayTableDataRow[11];

                document.getElementById("txtRulename").value = Rulename;
                document.getElementById("txtAPNname").value = APNname;
                document.getElementById("txtLteAPNname").value = LteAPNname;
                document.getElementById("lIPTypedropdown").value = IPType;
                if (QCI > 0) {
                    document.getElementById("1QOSEnablechk").checked = true;
                    document.getElementById("lQOSEngine").style.display = "block";
                    document.getElementById("txtQOSQCI").value = QCI;
                } else {
                    document.getElementById("1QOSEnablechk").checked = false;
                    document.getElementById("lQOSEngine").style.display = "none";
                    document.getElementById("txtQOSQCI").value = "";
                }

                document.getElementById("txtenable").value = Enable;

                $("#Sel2G3GAuthType").val(arrayTableDataRow[12]);
                $("#txt2G3GUser").val(arrayTableDataRow[13]);
                $("#txt2G3GPassword").val(arrayTableDataRow[14]);
                if ("NONE" == arrayTableDataRow[12]) {
                    $("#div2G3GAuthEnabled").hide();
                } else {
                    $("#div2G3GAuthEnabled").show();
                }

                $("#Sel4GAuthType").val(arrayTableDataRow[15]);
                $("#txt4GUser").val(arrayTableDataRow[16]);
                $("#txt4GPassword").val(arrayTableDataRow[17]);
                if ("NONE" == arrayTableDataRow[15]) {
                    $("#div4GAuthEnabled").hide();
                } else {
                    $("#div4GAuthEnabled").show();
                }
            }
        }
		
        this.saveUpdateCheckbox = function(arrayTableData) {
            var num = arrayTableData.length;
            if (num > 0) {
                if (arrayTableData[0][3] == "1") {
                    document.getElementById("pPDPdef_chk").checked = true;
                    PDPCheckFlags[0] = 1;
                } else {
                    document.getElementById("pPDPdef_chk").checked = false;
                    PDPCheckFlags[0] = 0;
                }
            }

            pPDPdefChange();
        }

        this.savePDPDataLocal = function(arrayISPProvider) {
            var indexLoad = 0;
            var index = 0
            for (index = 0; index < arrayISPProvider.length; index++) {
                indexLoad = 0;
                PDPMapExisting[index] = new Array(19);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item#index", index);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/rulename", arrayISPProvider[index][0]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/connnum", arrayISPProvider[index][1]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/pconnnum", arrayISPProvider[index][2]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/enable", arrayISPProvider[index][3]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/conntype", arrayISPProvider[index][4]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/default", arrayISPProvider[index][5]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/secondary", arrayISPProvider[index][6]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/apn", arrayISPProvider[index][7]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/iptype", arrayISPProvider[index][8]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/qci", arrayISPProvider[index][9]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/hastft", arrayISPProvider[index][10]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/lte_apn", arrayISPProvider[index][11]);

                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/authtype2g3", arrayISPProvider[index][12]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/usr2g3", arrayISPProvider[index][13]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/paswd2g3", arrayISPProvider[index][14]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/authtype4g", arrayISPProvider[index][15]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/usr4g", arrayISPProvider[index][16]);
                PDPMapExisting[index] = g_objXML.putMapElement(PDPMapExisting[index], indexLoad++, "RGW/wan/cellular/pdp_supported_list/Item/paswd4g", arrayISPProvider[index][17]);
            }
        }

        this.loadPDPData = function(flag) {
            var rulename = "";
            var indexISP = 0;
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, 0, "RGW/wan/proto", proto);
            var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);
            $(xml).find("cellular").each(function() {
                $(this).find("pdp_supported_list").each(function() {
                    $(this).find("Item").each(function() {
                        rulename = $(this).find("rulename").text();
                        arrayISPProvider[indexISP] = new Array(18);
                        arrayISPProvider[indexISP][0] = rulename;
                        arrayISPProvider[indexISP][1] = $(this).find("connnum").text();
                        arrayISPProvider[indexISP][2] = $(this).find("pconnnum").text();
                        arrayISPProvider[indexISP][3] = $(this).find("enable").text();
                        arrayISPProvider[indexISP][4] = $(this).find("conntype").text();
                        arrayISPProvider[indexISP][5] = $(this).find("default").text();
                        arrayISPProvider[indexISP][6] = $(this).find("secondary").text();
                        arrayISPProvider[indexISP][7] = $(this).find("apn").text();
                        arrayISPProvider[indexISP][8] = $(this).find("iptype").text();
                        arrayISPProvider[indexISP][9] = $(this).find("qci").text();
                        arrayISPProvider[indexISP][10] = $(this).find("hastft").text();
                        arrayISPProvider[indexISP][11] = $(this).find("lte_apn").text();
                        arrayISPProvider[indexISP][12] = $(this).find("authtype2g3").text();
                        arrayISPProvider[indexISP][13] = $(this).find("usr2g3").text();
                        arrayISPProvider[indexISP][14] = $(this).find("paswd2g3").text();
                        arrayISPProvider[indexISP][15] = $(this).find("authtype4g").text();
                        arrayISPProvider[indexISP][16] = $(this).find("usr4g").text();
                        arrayISPProvider[indexISP][17] = $(this).find("paswd4g").text();
                        indexISP++;
                    });
                });
            });

            this.savePDPDataLocal(arrayISPProvider);
            this.saveUpdateCheckbox(arrayISPProvider);
        }

        this.loadTableData = function(arrayTableData) {
            var tableProfiles = document.getElementById('tableProfiles');
            var tBodytable = tableProfiles.getElementsByTagName('tbody')[0];
            clearTabaleRows('tableProfiles');
            if (arrayTableData.length == 0) {
                var row1 = tBodytable.insertRow(0);
                var rowCol1 = row1.insertCell(0);
                rowCol1.colSpan = 4;
                rowCol1.innerHTML = jQuery.i18n.prop("tableNoData");
            } else {
                var ispValue = $(xml).find("ISP_name").text();
                active_isp = ispValue;
                for (var i = 0; i < arrayTableData.length; i++) {
                    var arrayTableDataRow = arrayTableData[i];
                    var row = tBodytable.insertRow(-1);
                    var activeFlag = false;
                    var ProfileNameCol = row.insertCell(0);
                    var APNNameCol = row.insertCell(1);
                    var ConnModeCol = row.insertCell(2);
                    var closeCol = row.insertCell(3);

                    if (arrayTableDataRow[0] == ispValue) {
                        activeFlag = true;
                        arrayTableDataRow[8] = connectedmode;
                    }

                    var _name = decodeURIComponent(arrayTableDataRow[0]);
                    if (activeFlag) {
                        ProfileNameCol.innerHTML = "<a><img src=\"images/status-icon3.png\"</a>\&nbsp\&nbsp<a href='#' onclick='ProfileClicked(" + i + ")'>" + _name + "</a>";
                    } else {
                        ProfileNameCol.innerHTML = "<a><img src=\"images/status-icon2.png\"</a>\&nbsp\&nbsp<a href='#' onclick='ProfileClicked(" + i + ")'>" + _name + "</a>";
                    }
                    var APNName = arrayTableDataRow[4].substring(arrayTableDataRow[4].indexOf(',"') + 7, arrayTableDataRow[4].length);
                    var l = APNName.indexOf('",');
                    APNName = APNName.substring(0, l);
                    APNNameCol.innerHTML = APNName;
                    if (arrayTableDataRow[8] == "0") {
                        var ConnMode = jQuery.i18n.prop("lAlways");
                    } else {
                        var ConnMode = jQuery.i18n.prop("lManual");
                    }

                    ConnModeCol.innerHTML = ConnMode;
                    if (!activeFlag) {
                        closeCol.className = "close";
                        closeCol.innerHTML = "<a href='#' onclick='deleteProfile(" + i + ")'><img src='images/close.png' alt='' border='0' /></a>";
                    } else {
                        closeCol.className = "close";
                        closeCol.innerHTML = "&nbsp;&nbsp&nbsp";
                    }
                }
            }
            Table.stripe(tableProfiles, "alternate", "table-stripeclass");
        }

        this.getCellularPostData = function() {

            var pdp_number = arrayISPProvider.length;
            var indexLoad = 0;
            var proto_;

            var NW_modeObj = document.getElementById("WorkModeropdown");
            var NW_modeSelected = NW_modeObj.options[NW_modeObj.selectedIndex].value;
            var boot_modeObj = document.getElementById("BootModeropdown");
            var boot_modeSelected = boot_modeObj.options[boot_modeObj.selectedIndex].value;
            var boot_mode1Obj = document.getElementById("BootModeropdown1");
            var boot_mode1Selected = boot_mode1Obj.options[boot_mode1Obj.selectedIndex].value;
            var boot_mode2Obj = document.getElementById("BootModeropdown2");
            var boot_mode2Selected = boot_mode2Obj.options[boot_mode2Obj.selectedIndex].value;
            var selectLteTypeObj = document.getElementById("setLikeLTETypedropdown");
            var selectedLteType = selectLteTypeObj.options[selectLteTypeObj.selectedIndex].value;
            if(NW_modeSelected == "2"||NW_modeSelected == "5"||NW_modeSelected == "6")
                boot_modeSelected = "20"; //invalid value


			var boot_mode3Selected = $("#BootModeropdown3").val(); //
			
            var proto_ = document.getElementById("micdropdown").value;
            var connmode = document.getElementById("Cconndropdown").value;

            PDPMapExisting[pdp_number] = new Array();


			if(proto_ != InternetconnectFlag){
            	PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/proto", proto_);
			}

			if(connmode != ConnectionModeflag){
            	PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/connect_mode", connmode);
			}

			if(NW_modeSelected != NetworkModeflag){
            	PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/NW_mode", NW_modeSelected);
			}
           
            if (NW_modeSelected != pre_NW_mode) { //�ı���ģʽ������������ѡ��ģʽ
                PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/NW_mode_action", "1");
                if (1 == NW_modeSelected) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_modeSelected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                }

                else if (3 == NW_modeSelected) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_mode1Selected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                } else if (4 == NW_modeSelected) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_mode2Selected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                } else if (8 == NW_modeSelected) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_mode3Selected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                }
            } else {
                if (1 == NW_modeSelected && boot_modeSelected != pre_prefer_bootmode) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_modeSelected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                }

                else if (3 == NW_modeSelected && boot_mode1Selected != pre_prefer_bootmode1) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_mode1Selected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                } else if (4 == NW_modeSelected && boot_mode2Selected != pre_prefer_bootmode2) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_mode2Selected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                }else if (8 == NW_modeSelected && boot_mode3Selected != pre_prefer_bootmode3) {
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode", boot_mode3Selected);
                    PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_mode_action", "1");
                }
            }


			if(selectedLteType != Preferred_LTETypeflag){				
            	PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_lte_type", selectedLteType);
			}

			if (selectedLteType != pre_prefer_lte_type){
                PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/cellular/prefer_lte_type_action", "1");
            }


			if(bDisabledAutoDialInRoam != $("#RoamingDisableAutoDialCheckBox").attr("checked"))
			{
				if($("#RoamingDisableAutoDialCheckBox").attr("checked"))
				{
					PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/Roaming_disable_auto_dial", 1);
				}
				else{
					PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/Roaming_disable_auto_dial", 0);
				}
				PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/Roaming_disable_auto_dial_action", 1);
			}

           
            //MTU            
            if (mtuValue != $("#txtMtuValue").val()) {
				g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/mtu", $("#txtMtuValue").val());
                g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/mtu_action", "1");
            } 
			
            // AutoAPN     
            if ($("#AutoConfigureAPNCheckBox").attr("checked") != gdAutoConfAPN) {
				if ($("#AutoConfigureAPNCheckBox").attr("checked")) {
                	PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/auto_apn", 1);
            	} else {
                	PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/auto_apn", 0);
            	}
				
                PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/auto_apn_action", 1);
            }
            //Engineer model
            if ($("#EngineeringModelSel").val() != gEngineerModel) {
                PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/Engineering_mode", $("#EngineeringModelSel").val());
				if(1 == $("#EngineeringModelSel").val()) //enabled
				{
					 PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/query_time_interval", $("#txtqueryTimeInterval").val());
				}
            }
			else
			{
				if(1 == gEngineerModel&& $("#txtqueryTimeInterval").val() != gEngineerQueryTimeInterval){
					PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/query_time_interval", $("#txtqueryTimeInterval").val());
				}
			}

			 // Dial in roaming status     
            if ($("#DialInRoamingSel").val() != gDialInRoaming) {				
                PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/Roaming_disable_dial", $("#DialInRoamingSel").val());      	
                PDPMapExisting[pdp_number] = g_objXML.putMapElement(PDPMapExisting[pdp_number], indexLoad++, "RGW/wan/Roaming_disable_dial_action", 1);
            } 

                   
            if (pdp_number > 0) {
                if (document.getElementById("pPDPdef_chk").checked)
                    PDPMapExisting[0][4][1] = "1";
                else
                    PDPMapExisting[0][4][1] = "0";
            }

            return PDPMapExisting;
        }

        this.getDisabledPostData = function() {
            var itemIndex = 0;
            mapData = null;
            mapData = new Array();
            putMapElement_test("RGW/wan/proto", "disabled", itemIndex++);
            return mapData;
        }

        this.getManualNetworkData = function() {

            var mapData = new Array(0);
            controlMapCurrent[0][1] = document.getElementById("micdropdown").value;
            mapData = g_objXML.copyArray(controlMapCurrent, mapData);
            mapData = g_objXML.getChangedArray(controlMapExisting, mapData, true);
            return mapData;
        }
        this.postItem = function(isDeleted, Rulename, Enable, APNname, LteApnName, IPType, ConnNum, pConnNum, IsSecondary, IsDefault, QCI, HasTFT, txt2G3GAuthType, txt2G3GUserName, txt2G3GPasswd, txt4GAuthType, txt4GUserName, txt4GPasswd) {
            //APNname = encodeURI(APNname);
            var itemIndex = 0;
            mapData = null;
            mapData = new Array();

            if (IsSecondary == 0)//Secondary apply TFT setting
                this.putMapElement("RGW/wan/cellular/tft_apply_action", "2", itemIndex++);

            if (isDeleted) {
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item#index", itemIndex, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/rulename#delete", Rulename, itemIndex++);
            } else {
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item#index", itemIndex, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/rulename", Rulename, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/connnum", ConnNum, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/pconnnum", pConnNum, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/enable", Enable, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/conntype", "0", itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/default", IsDefault, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/secondary", IsSecondary, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/apn", APNname, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/lte_apn", LteApnName, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/iptype", IPType, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/qci", QCI, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/hastft", HasTFT, itemIndex++);

                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/authtype2g3", txt2G3GAuthType, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/usr2g3", txt2G3GUserName, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/paswd2g3", txt2G3GPasswd, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/authtype4g", txt4GAuthType, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/usr4g", txt4GUserName, itemIndex++);
                this.putMapElement("RGW/wan/cellular/pdp_supported_list/Item/paswd4g", txt4GPasswd, itemIndex++);
            }

            if (mapData.length > 0) {
                postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
            }
        }

        this.is_active_isp = function(isp) {
            if (isp == active_isp) {
                return true;
            }
            return false
        }
        this.putMapElement = function(xpath, value, index) {
            mapData[index] = new Array(2);
            mapData[index][0] = xpath;
            mapData[index][1] = value;
        }
        this.copyControlArray = function() {
            controlMapCurrent = g_objXML.copyArray(controlMapExisting, controlMapCurrent);
        }
        this.loadDisabledData = function() {
            var wifiDiv = document.getElementById("WiFi_div");
            if (wifiDiv) {
                wifiDiv.style.display = "none";
            }
            controlMapExisting = g_objXML.putMapElement(controlMapExisting, 0, "RGW/wan/proto", proto);
        }
        this.getTableProfilesDataRow = function(index) {
            return arrayISPProvider[index];
        }
        this.getTFTTableProfilesDataRow = function(index, connnum) {
            if (connnum == 2) {
                return arrayTF1Provider[index];
            } else if (connnum == 4) {
                return arrayTF2Provider[index];
            } else if (connnum == 5) {
                return arrayTF3Provider[index];
            } else if (connnum == 6) {
                return arrayTF4Provider[index];
            }
        }
        this.getTableWNsDataRow = function(index) {
            return _arrayWirelessNws[index];
        }

        return this.each(function() {
        });
    }
})(jQuery);


function setFocusID(id1, id) {
    var ip = document.getElementById(id1).value;
    /*if(ip>255){
            alert(jQuery.i18n.prop("lIPSettingInvalid"));
            // showAlert(jQuery.i18n.prop("lIPSettingInvalid"));
            document.getElementById(id1).focus();
            document.getElementById(id1).value = " ";
            document.getElementById(id1).focus();
            return;
        }*/
    if (document.getElementById(id1).value.toString().length == "3")
        document.getElementById(id).focus();
}

function deleteProfile(index) {
    document.getElementById("selectpopup").style.display = "none";
    var data = g_objContent.getTableProfilesDataRow(index);
    g_objContent.postItem(data[0], true);
}

function micdropdownChanged() {
    g_objContent.clearControlArray();
    var linkObj = document.getElementById("micdropdown");
    var value = linkObj.options[linkObj.selectedIndex].value;
    g_objContent.dispalyAllNone();
    clearInterval(_WiFiIntervalID);
    if (cellular_save == 0) {
        g_objContent.clearStatus();
    }

    if (value == "cellular") {
        //document.getElementById("Profiledropdown").innerHTML = "";
        $("#divEngineeringModel").show();
		if (1 == gEngineerModel)
		{
        	$("#divQueryTimeInterval").show();
		}
        document.getElementById("Cellular_div").style.display = "block";
        document.getElementById("connectmode").style.display = "block";
        document.getElementById("divMtu").style.display = "block";
        // document.getElementById("divAutoAPN").style.display = "none";
        g_objContent.loadPDPData(false);
        document.getElementById("workmode").style.display = "block";
        document.getElementById("lWorkMode").innerHTML = jQuery.i18n.prop("lWorkMode");
        network_mode = $(xml).find("NW_mode").text();
        document.getElementById('WorkModeropdown').value = network_mode;
        document.getElementById("preferredLTEType").style.display = "block";
        document.getElementById("lsetLikeLTEType").innerHTML = jQuery.i18n.prop("lsetLikeLTEType");
        pre_lte_type = $(xml).find("prefer_lte_type").text();
        document.getElementById('setLikeLTETypedropdown').value = pre_lte_type;
        Preferred_LTETypeflag = pre_lte_type;
        if (network_mode == '1') {
            document.getElementById("bootmode").style.display = "block";
            document.getElementById("lBootMode").innerHTML = jQuery.i18n.prop("lBootMode");
            prefer_bootmode = $(xml).find("prefer_mode").text();
            if ("1" == prefer_bootmode || "2" == prefer_bootmode || "7" == prefer_bootmode) {
                document.getElementById('BootModeropdown').value = prefer_bootmode;
            } else {
                document.getElementById('BootModeropdown').value = "1";
            }

            Preferred_NetworkModeflag = prefer_bootmode;
        } else
            document.getElementById("bootmode").style.display = "none";
        if (network_mode == '3') {
            document.getElementById("bootmode1").style.display = "block";
            document.getElementById("lBootMode1").innerHTML = jQuery.i18n.prop("lBootMode1");
            prefer_bootmode1 = $(xml).find("prefer_mode").text();
            if (prefer_bootmode1 != '4')
                prefer_bootmode1 = '3';
            document.getElementById('BootModeropdown1').value = prefer_bootmode1;
            Preferred_NetworkModeflag = prefer_bootmode1;
        } else
            document.getElementById("bootmode").style.display = "none";
        if (network_mode == '4') {
            document.getElementById("bootmode2").style.display = "block";
            document.getElementById("lBootMode2").innerHTML = jQuery.i18n.prop("lBootMode2");
            prefer_bootmode2 = $(xml).find("prefer_mode").text();
            if (prefer_bootmode2 != '6')
                prefer_bootmode2 = '5';
            document.getElementById('BootModeropdown2').value = prefer_bootmode2;
            Preferred_NetworkModeflag = prefer_bootmode2;
        } else
            document.getElementById("bootmode2").style.display = "none";

		if (network_mode == '8') {
            document.getElementById("bootmode3").style.display = "block";
            document.getElementById("lBootMode3").innerHTML = jQuery.i18n.prop("lBootMode3");
            prefer_bootmode3 = $(xml).find("prefer_mode").text();
            if (prefer_bootmode3 != '9')
                prefer_bootmode3 = '8';
            document.getElementById('BootModeropdown3').value = prefer_bootmode3;
            Preferred_NetworkModeflag = prefer_bootmode3;
        } else
            document.getElementById("bootmode3").style.display = "none";
		
        if (network_mode != '5' && network_mode != '4') {
            document.getElementById("preferredLTEType").style.display = "block";
            document.getElementById("lsetLikeLTEType").innerHTML = jQuery.i18n.prop("lsetLikeLTEType");
            pre_lte_type = $(xml).find("prefer_lte_type").text();
            document.getElementById('setLikeLTETypedropdown').value = pre_lte_type;
            Preferred_LTETypeflag = pre_lte_type;
        } else
            document.getElementById("preferredLTEType").style.display = "none";
    } else if (value == "wifi") {
        g_objContent.loadWiFiData(false);
        document.getElementById("btnScanWirelessNw").disabled = true;
    } else if (value == "disabled") {
        g_objContent.loadDisabledData();
        $("#divEngineeringModel").hide();
        $("#divQueryTimeInterval").hide();
        document.getElementById("workmode").style.display = "none";
        document.getElementById("bootmode").style.display = "none";
        document.getElementById("bootmode1").style.display = "none";
        document.getElementById("bootmode2").style.display = "none";
		document.getElementById("bootmode3").style.display = "none";
        document.getElementById("preferredLTEType").style.display = "none";
        // document.getElementById("divAutoAPN").style.display = "none";
    } else if (value == "manual_network") {
        MannualNetwork();
    }
    g_objContent.copyControlArray();
}
function workmoderopdownChanged() {
    var linkObj = document.getElementById("WorkModeropdown");
    var value = linkObj.options[linkObj.selectedIndex].value;

    if (value == '1') {
        document.getElementById("bootmode").style.display = "block";
        document.getElementById("lBootMode").innerHTML = jQuery.i18n.prop("lBootMode");
        prefer_bootmode = $(xml).find("prefer_mode").text();
        if ("1" == prefer_bootmode || "2" == prefer_bootmode || "7" == prefer_bootmode) {
            document.getElementById('BootModeropdown').value = prefer_bootmode;
        } else {
            document.getElementById('BootModeropdown').value = "1";
        }
        Preferred_NetworkModeflag = prefer_bootmode;
    } else {
        document.getElementById("bootmode").style.display = "none";
    }

    if (value == '3') {
        document.getElementById("bootmode1").style.display = "block";
        document.getElementById("lBootMode1").innerHTML = jQuery.i18n.prop("lBootMode1");
        prefer_bootmode1 = $(xml).find("prefer_mode").text();
        if (prefer_bootmode1 != '4')
            prefer_bootmode1 = '3';
        document.getElementById('BootModeropdown1').value = prefer_bootmode1;
        Preferred_NetworkModeflag = prefer_bootmode1;
    } else {
        document.getElementById("bootmode1").style.display = "none";
    }

    if (value == '4') {
        document.getElementById("bootmode2").style.display = "block";
        document.getElementById("lBootMode2").innerHTML = jQuery.i18n.prop("lBootMode2");
        prefer_bootmode2 = $(xml).find("prefer_mode").text();
        if (prefer_bootmode2 != '6')
            prefer_bootmode2 = '5';
        document.getElementById('BootModeropdown2').value = prefer_bootmode2;
        Preferred_NetworkModeflag = prefer_bootmode2;
    } else {
        document.getElementById("bootmode2").style.display = "none";
    }

	  if (value == '8') {
        document.getElementById("bootmode3").style.display = "block";
        document.getElementById("lBootMode3").innerHTML = jQuery.i18n.prop("lBootMode3");
        prefer_bootmode3 = $(xml).find("prefer_mode").text();
        if (prefer_bootmode3 != '9')
            prefer_bootmode3 = '8';
        document.getElementById('BootModeropdown3').value = prefer_bootmode3;
        Preferred_NetworkModeflag = prefer_bootmode3;
    } else {
        document.getElementById("bootmode3").style.display = "none";
    }

    if (value == '1' || value == '2' || value == '3' || value == '8') {
        document.getElementById("preferredLTEType").style.display = "block";
        document.getElementById("lsetLikeLTEType").innerHTML = jQuery.i18n.prop("lsetLikeLTEType");
        pre_lteType = $(xml).find("prefer_lte_type").text();
        document.getElementById('setLikeLTETypedropdown').value = pre_lteType;
        Preferred_LTETypeflag = pre_lteType;
    } else {
        document.getElementById("preferredLTEType").style.display = "none";
    }
}

function MannualNetwork() {
    //sm("MBMannualNetwork",450,250);
    //document.getElementById("h1MannualNetwork").innerHTML = jQuery.i18n.prop("h1MannualNetwork");

    var itemIndex = 0;
    mapData = null;
    mapData = new Array();
    putMapElement_test("RGW/wan/cellular/search_network", 1, itemIndex++);
    putMapElement_test("RGW/wan/proto", "manual_network", itemIndex++);
    if (mapData.length > 0) {
        postXML("wan", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
    }
}
function SelectNetworkChanged() {
    g_objContent.clearControlArray();
    var value = linkObj.options[linkObj.selectedIndex].value;
    g_objContent.dispalyAllNone();
    var itemIndex = 0;
    mapData = null;
    mapData = new Array();
    putMapElement_test("RGW/wan/cellular/network_param", value, itemIndex++);
    if (mapData.length > 0) {
        postXML("wan", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));

    }
}

function btnCancelMannualNetwork() {
    hm();
}
function btnCancelClickedProfile() {
    hm();
    document.getElementById("selectpopup").style.display = "block";
    // document.getElementById("selectpopup").style.display = "block";
}
function localizeMBAddNewProfile() {
    var arrayLabels = document.getElementsByTagName("label");
    lableLocaliztionMBProfile(arrayLabels);
}
function lableLocaliztionMBProfile(labelArray) {
    for (var i = 0; i < labelArray.length; i++) {
        if (jQuery.i18n.prop(labelArray[i].id) != null)
            getID(labelArray[i].id).innerHTML = jQuery.i18n.prop(labelArray[i].id);
    }
}

function localizePDPProfile() {
    localizeMBAddNewProfile();
    document.getElementById("h1Popup_PDP").innerHTML = jQuery.i18n.prop("h1Popup_PDP");
    document.getElementById("btnCancel").innerHTML = jQuery.i18n.prop("btnCancel");
    buttonLocaliztion(document.getElementById("btnOK_PDP").id);
}

function btnOKPDPSetting() {
    var Rulename = document.getElementById("txtRulename").value;
    var APNname = document.getElementById("txtAPNname").value;
    var LteApnName = document.getElementById("txtLteAPNname").value;
    var IPType = document.getElementById("lIPTypedropdown").value;
    //var QosEnable = document.getElementById("1QOSEnablechk").value;
    var QCI;
    var ConnNum = document.getElementById("txtconnnum").value;
    var pConnNum = document.getElementById("txtpconnnum").value;
    var IsSecondary = document.getElementById("txtsecondary").value;
    var IsDefault = document.getElementById("txtdefault").value;
    var Enable = document.getElementById("txtenable").value;

    var txt2G3GAuthType = $("#Sel2G3GAuthType").val();
    var txt2G3GUserName = $("#txt2G3GUser").val();
    var txt2G3GPasswd = $("#txt2G3GPassword").val();
    if ("NONE" == txt2G3GAuthType) {
        txt2G3GUserName = "any";
        txt2G3GPasswd = "any";
    }

    var txt4GAuthType = $("#Sel4GAuthType").val();
    var txt4GUserName = $("#txt4GUser").val();
    var txt4GPasswd = $("#txt4GPassword").val();
    if ("NONE" == txt4GAuthType) {
        txt4GPasswd = "any";
        txt4GUserName = "any";
    }

	//set APN/LteApn to "NULL" if APN/LteApn field is empty 
	if(0 == APNname.length){
		APNname = "NULL";
	}
	if(0 == LteApnName.length){
		LteApnName = "NULL";
	}

    var HasTFT = 0;
    var isDeleted = 0;
    if (document.getElementById("1QOSEnablechk").checked) {
        QCI = document.getElementById("txtQOSQCI").value;
        var regEx = /^[1-9]\d*$/; ///������
        if (!regEx.test(QCI)) {
            $("#qciCheckError").show();
            $("#qciCheckError").text(jQuery.i18n.prop("lQciCheckError"));
            return;
        }
    } else {
        QCI = 0;
    }

    if (("NONE" == txt2G3GAuthType && isChineseChar(txt2G3GUserName)) || ("NONE" == txt4GAuthType && isChineseChar(txt4GUserName))) {
        document.getElementById("lErrorLogs").style.display = "block";
        document.getElementById("lErrorLogs").innerHTML = jQuery.i18n.prop("APN_AHTU_USER_NAME_INVALIDEATE");
        return;
    }

    var errorString = validatePDP(Rulename, APNname, LteApnName);
    if (errorString == "OK") {
        hm();
        g_objContent.postItem(isDeleted, Rulename, Enable, APNname, LteApnName, IPType, ConnNum, pConnNum, IsSecondary, IsDefault, QCI, HasTFT, txt2G3GAuthType, txt2G3GUserName, txt2G3GPasswd,txt4GAuthType, txt4GUserName, txt4GPasswd);
    } else {
        document.getElementById("lErrorLogs").style.display = "block";
        document.getElementById("lErrorLogs").innerHTML = jQuery.i18n.prop(errorString);
    }
}
function btnOKClickedProfile() {
    var Profilename = document.getElementById("txtProfilename").value;
    var APNname = document.getElementById("txtAPNname").value;
    var LteAPNname = document.getElementById("txtLteAPNname").value;
    var Username = document.getElementById("txtUsername").value;
    var Password = document.getElementById("txtPassword").value;
    var AccessNumber = document.getElementById("txtAccessNumber").value;
    var ConnMode = document.getElementById("Cconndropdown").value;
    var Idle = "600";
    var idle = document.getElementById("txtIdle").value;

    var errorString = validate(Profilename, APNname, Username, Password, AccessNumber, ConnMode, Idle);
    if (errorString == "OK") {
        hm();
        g_objContent.postItem(Profilename, false, APNname, LteAPNname,Username, Password, AccessNumber, ConnMode, Idle);
    } else {
        document.getElementById("lErrorLogs").style.display = "block";
        document.getElementById("lErrorLogs").innerHTML = jQuery.i18n.prop(errorString);
    }
}
function check2Change() {
    if (document.getElementById("check1").checked)
        document.getElementById("localeDiv").style.display = "block";
    else
        document.getElementById("localeDiv").style.display = "none";
}
function check1Change() {
    if (document.getElementById("check2").checked)
        document.getElementById("customeDNS").style.display = "block";
    else
        document.getElementById("customeDNS").style.display = "none";
}

function ManualNetworkChange() {
    var manualnetworkchecked;
    if (document.getElementById("manual_network_check2").checked)
        manualnetworkchecked = 1;
    else
        manualnetworkchecked = 0;
    var itemIndex = 0;
    mapData = null;
    mapData = new Array();
    putMapElement_test("RGW/wan/cellular/manual_network_start", manualnetworkchecked, itemIndex++);
    if (mapData.length > 0) {
        postXML("wan", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
    }
}
function BgScanTimeDropdown() {
    var linkObj = document.getElementById("BgScanTimedropdown");
    var value = linkObj.options[linkObj.selectedIndex].value;

    document.getElementById("CIdle").style.display = "none";
}
function conndropdownChanged() {
    var linkObj = document.getElementById("Cconndropdown");
    var value = linkObj.options[linkObj.selectedIndex].value;

}
function validatePDP(Rulename, APNname,LteApnName) {
    if (Rulename == "")
        return "EMPTY_RULE_NAME";
    if (APNname == "" || LteApnName == "")
        return "EMPTY_APN_NAME";
    if (!deviceNameValidation(Rulename))
        return "SPECIAL_CHARS_ARE_NOT_ALLOWED";
    if (!validateApnName(APNname) || !validateApnName(LteApnName))
        return "APN_NAME_INVALIDEATE";

    return "OK";
}

function validateApnName(APNname) {
    if (isChineseChar(APNname)) {
        return false;
    }

    if (APNname.toString().indexOf("%") != -1)
        return false;
    else if (APNname.toString().indexOf("^") != -1)
        return false;
    else if (APNname.toString().indexOf(";") != -1)
        return false;
	else if (APNname.toString().indexOf(",") != -1)
        return false;

    return true;
}

function showConnectAlert(message) {
    clearInterval(_WiFiIntervalSelectID);
    clearInterval(_WiFiIntervalID);

    buttonLocaliztion("btnConnectW");
    document.getElementById("btnCancel1").innerHTML = jQuery.i18n.prop("btnModalCancle");
    document.getElementById("lConfirmMessage1").innerHTML = message;
    document.getElementById("lConnect1").innerHTML = jQuery.i18n.prop("lConnect");
    sm("ConfirmMB", 350, 150);
}
function showPSKAlert(message) {
    clearInterval(_WiFiIntervalSelectID);
    clearInterval(_WiFiIntervalID);
    buttonLocaliztion("btnConnectWP");
    document.getElementById("btnCancel2").innerHTML = jQuery.i18n.prop("btnModalCancle");
    // document.getElementById("lConfirmMessage2").innerHTML = message;
    document.getElementById("lConnect2").innerHTML = jQuery.i18n.prop("lConnect");
    document.getElementById("lpsk").innerHTML = jQuery.i18n.prop("lpsk");
    document.getElementById("lRetypepsk").innerHTML = jQuery.i18n.prop("lRetypepsk");

    sm("PSKMB", 350, 180);
}

function btnCancelConfigure() {
    hm();
}
function bootmoderopdownChanged() {
}
function btnOKConfigure() {
    var Username = document.getElementById("txtWUsername").value;
    var Password = document.getElementById("txtWPassword").value;

    var itemIndex = 0;
    mapData = null;
    mapData = new Array();
    putMapElement_test("RGW/wan/username_webportal", Username, itemIndex++);
    putMapElement_test("RGW/wan/password_webportal", Password, itemIndex++);
    if (mapData.length > 0) {
        postXML("wan", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
    }
}

function putMapElement_test(xpath, value, index) {
    mapData[index] = new Array(2);
    mapData[index][0] = xpath;
    mapData[index][1] = value;
}

function pPDPdefChange() {

}

function pPDPdefClick() {
    var connnum = 1;
    var pconnum = "NA";
    var is_secondary = 1;
    var is_default = 1;
    sm("Popup_PDP", 400, 200);
    localizePDPProfile();
    $("#divApnNmae").show();
    $("#divIpType").show();
    document.getElementById("txtconnnum").value = connnum;
    document.getElementById("txtpconnnum").value = pconnum;
    document.getElementById("txtsecondary").value = is_secondary;
    document.getElementById("txtdefault").value = is_default;
    g_objContent.loadPDPDataToBox(0);
    if (document.getElementById("pPDPdef_chk").checked)
        document.getElementById("txtenable").value = 1;
    else
        document.getElementById("txtenable").value = 0;
    g_objContent.loadPDPDataToBox(0);
}

function PDPColorChange(lableid) {
    var BackgroundNormalColor = "#FF0000";
    document.getElementById(lableid).style.color = BackgroundNormalColor;
    document.getElementById(lableid).style.cursor = "pointer";

}
function PDPColorRestore(lableid) {
    var BackgroundMouseoverColor = "#000000";
    document.getElementById(lableid).style.color = BackgroundMouseoverColor;
    document.getElementById(lableid).style.cursor = "default";
}

function btnCancel() {
    hm();
}
function QOSEnbaleChange() {
    if (document.getElementById("1QOSEnablechk").checked)
        document.getElementById("lQOSEngine").style.display = "block";
    else
        document.getElementById("lQOSEngine").style.display = "none";

    $("#qciCheckError").hide();
}

function IPV4RadioChange() {
    if (document.getElementById("IPV4Radio").checked) {
        document.getElementById("TFTv6Address").style.display = "none";
        document.getElementById("TFTv4Address").style.display = "block";
    }
}
function IPV6RadioChange() {
    if (document.getElementById("IPV6Radio").checked) {
        document.getElementById("TFTv4Address").style.display = "none";
        document.getElementById("TFTv6Address").style.display = "block";
    }
}
function NoIPChange() {
    if (document.getElementById("NoIPRadio").checked) {
        document.getElementById("TFTv4Address").style.display = "none";
        document.getElementById("TFTv6Address").style.display = "none";
    }
}

/*
function btnSaveTFTRule(Connnum) {

    var remote_ip;
    var IPType;
    var local_port;
    var remote_port;
    var Rulename = document.getElementById("txttftRulename").value;
    var HasTFT = 1;
    var isDeleted = 0;
    var localport_flag = 1;
    var remoteport_flag = 1;
    var isDeleted = 0;
    var pfindex = 0;
    var evaluation_index = 0;
    var protocol_number_flag = 1;
    var protocol_number = 0;
    var direction = 0;
    var netmask_flag = 1;
    var netmask;
    var netmask1, netmask2, netmask3, netmask4;
    var netmask5, netmask6, netmask7, netmask8;
    var netmask9, netmask10, netmask11, netmask12;
    var netmask13, netmask14, netmask15, netmask16;
    var linkObj = document.getElementById("directiondropdown");
    var value = linkObj.options[linkObj.selectedIndex].value;

    direction = value;
    if (document.getElementById("IPV6Radio").checked) {
        IPType = 2;
        remote_ip = document.getElementById("txtv6IP1").value + ":" +
                    document.getElementById("txtv6IP2").value + ":" +
                    document.getElementById("txtv6IP3").value + ":" +
                    document.getElementById("txtv6IP4").value + ":" +
                    document.getElementById("txtv6IP5").value + ":" +
                    document.getElementById("txtv6IP6").value + ":" +
                    document.getElementById("txtv6IP7").value + ":" +
                    document.getElementById("txtv6IP8").value + ":" +
                    document.getElementById("txtv6IP9").value + ":" +
                    document.getElementById("txtv6IP10").value + ":" +
                    document.getElementById("txtv6IP11").value + ":" +
                    document.getElementById("txtv6IP12").value + ":" +
                    document.getElementById("txtv6IP13").value + ":" +
                    document.getElementById("txtv6IP14").value + ":" +
                    document.getElementById("txtv6IP15").value + ":" +
                    document.getElementById("txtv6IP16").value;


        var netmask1 = document.getElementById("txtv6Netmask1").value;
        var netmask2 = document.getElementById("txtv6Netmask2").value;
        var netmask3 = document.getElementById("txtv6Netmask3").value;
        var netmask4 = document.getElementById("txtv6Netmask4").value;
        var netmask5 = document.getElementById("txtv6Netmask5").value;
        var netmask6 = document.getElementById("txtv6Netmask6").value;
        var netmask7 = document.getElementById("txtv6Netmask7").value;
        var netmask8 = document.getElementById("txtv6Netmask8").value;
        var netmask9 = document.getElementById("txtv6Netmask9").value;
        var netmask10 = document.getElementById("txtv6Netmask10").value;
        var netmask11 = document.getElementById("txtv6Netmask11").value;
        var netmask12 = document.getElementById("txtv6Netmask12").value;
        var netmask13 = document.getElementById("txtv6Netmask13").value;
        var netmask14 = document.getElementById("txtv6Netmask14").value;
        var netmask15 = document.getElementById("txtv6Netmask15").value;
        var netmask16 = document.getElementById("txtv6Netmask16").value;
        if ((netmask1 == "") || (netmask2 == "") || (netmask3 == "") || (netmask4 == "") ||
            (netmask5 == "") || (netmask6 == "") || (netmask7 == "") || (netmask8 == "") ||
            (netmask9 == "") || (netmask10 == "") || (netmask11 == "") || (netmask12 == "") ||
            (netmask13 == "") || (netmask14 == "") || (netmask15 == "") || (netmask16 == "")) {
            netmask_flag = 0;
            netmask = "0";
        }

        else {
            netmask = netmask1 + ":" +
                      netmask2 + ":" +
                      netmask3 + ":" +
                      netmask4 + ":" +
                      netmask5 + ":" +
                      netmask6 + ":" +
                      netmask7 + ":" +
                      netmask8 + ":" +
                      netmask9 + ":" +
                      netmask10 + ":" +
                      netmask11 + ":" +
                      netmask12 + ":" +
                      netmask13 + ":" +
                      netmask14 + ":" +
                      netmask15 + ":" +
                      netmask16;
        }



        var local_port2 = document.getElementById("txtLocalPortRange2").value;
        if ((local_port1 == "") || (local_port2 == "")) {
            localport_flag = 0;
            local_port1 = "0";
            local_port2 = "0";
        }

    } else if (document.getElementById("IPV4Radio").checked) {
        IPType = 1;
        remote_ip = document.getElementById("txtv4IP1").value + "." +
                    document.getElementById("txtv4IP2").value + "." +
                    document.getElementById("txtv4IP3").value + "." +
                    document.getElementById("txtv4IP4").value;
        netmask1 = document.getElementById("txtv4Netmask1").value;
        netmask2 = document.getElementById("txtv4Netmask2").value;
        netmask3 = document.getElementById("txtv4Netmask3").value;
        netmask4 = document.getElementById("txtv4Netmask4").value;
        if ((netmask1 == "") || (netmask2 == "") || (netmask3 == "") || (netmask4 == "")) {
            netmask_flag = 0;
            netmask = "0";
        } else {
            netmask = netmask1 + "." +
                      netmask2 + "." +
                      netmask3 + "." +
                      netmask4;
        }
    } else if (document.getElementById("NoIPRadio").checked) {
        IPType = 0;
        netmask_flag = 0
                       remote_ip = "0.0.0.0";
        netmask = "0";
    }
    var local_port1 = document.getElementById("txtLocalPortRange1").value;
    var local_port2 = document.getElementById("txtLocalPortRange2").value;
    if ((local_port1 == "") || (local_port2 == "")) {
        localport_flag = 0;
        local_port1 = "0";
        local_port2 ="0";
    }

    var remote_port1 = document.getElementById("txtRemotePortRange1").value;
    var remote_port2 = document.getElementById("txtRemotePortRange2").value;
    if ((remote_port1 == "") || (remote_port2 == "")) {
        remoteport_flag = 0;
        remote_port1 = "0";
        remote_port2 = "0";
    }

    local_port = local_port1 + ":" + local_port2;

    remote_port = remote_port1 + ":" + remote_port2;

    pfindex = document.getElementById("txttftPfindex").value;
    if (pfindex == "") {
        pfindex = 1;
    }


    evaluation_index = document.getElementById("txttftEvaindex").value;
    if (evaluation_index == "") {
        evaluation_index = 0;
    }


    protocol_number = document.getElementById("txttftProtocolnumber").value;
    if (protocol_number == "") {
        protocol_number_flag = 0;
        protocol_number = "0";
    }


    var errorflag = validateTFT(Rulename, remote_ip, IPType, local_port, remote_port, localport_flag, remoteport_flag, pfindex, evaluation_index, protocol_number,protocol_number_flag,netmask,netmask_flag);
    document.getElementById("lTFTRuleError").style.display = "block";
    if (errorflag == 0) {
        hm();
        document.getElementById("lTFTRuleError").style.display = "none";
        g_objContent.posttftItem(Rulename, isDeleted, Connnum, remote_ip, IPType, local_port, remote_port, localport_flag, remoteport_flag, netmask_flag, netmask, pfindex, evaluation_index, protocol_number_flag, protocol_number, direction);
    } else if (errorflag == 1) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lemptyTFTname");
    } else if (errorflag == 2) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lspecialTFTname");
    } else if (errorflag == 3) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lLocalPortInvalid");
    } else if (errorflag == 4) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lRemotePortInvalid");
    } else if (errorflag == 5) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lIncorrectIPAddress");
    } else if (errorflag == 6) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lDataPacketFilterIdError");
    } else if (errorflag == 7) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lAssessPrioritiesError");
    } else if (errorflag == 8) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lProtocolNumError");
    } else if (errorflag == 9) {
        document.getElementById("lTFTRuleError").innerHTML = jQuery.i18n.prop("lsubsetMaskError");
    }
}
*/

function LteAuthTypeChanged() {
    if ("NONE" == $("#Sel4GAuthType").val()) {
        $("#div4GAuthEnabled").hide();
    } else {
        $("#div4GAuthEnabled").show();
    }
}

function f2G3GAuthTypeChanged() {
    if ("NONE" == $("#Sel2G3GAuthType").val()) {
        $("#div2G3GAuthEnabled").hide();
    } else {
        $("#div2G3GAuthEnabled").show();
    }
}

function EngineeringModelChanged() {
    if (1 == $("#EngineeringModelSel").val()) {
        $("#divQueryTimeInterval").show();
        $("#txtqueryTimeInterval").val(gEngineerQueryTimeInterval);
    } else {
        $("#divQueryTimeInterval").hide();
    }    
}
