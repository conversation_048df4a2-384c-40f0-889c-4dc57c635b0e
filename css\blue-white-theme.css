/* 蓝白主题配色 */
:root {
  --main-blue: #1976d2;
  --light-blue: #bbdefb;
  --dark-blue: #0d47a1;
  --white: #ffffff;
  --light-gray: #f5f5f5;
  --text-dark: #333333;
  --text-light: #ffffff;
}

body {
  background-color: var(--white);
  color: var(--text-dark);
}

/* 导航和菜单样式 */
.navigation {
  background: linear-gradient(to right, var(--dark-blue), var(--main-blue)) !important;
}

.navigation ul li a:hover {
  background: var(--dark-blue) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.navigation ul li a.active {
  background: var(--dark-blue) !important;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3) !important;
}

/* 电池信息样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--light-blue);
  flex-wrap: wrap;
  position: relative;
  background-color: var(--white);
}

/* 右上角快速链接样式 */
.top-quick-links {
  position: absolute;
  top: 10px;
  right: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
  z-index: 100;
  transition: all 0.3s ease;
}

.top-quick-links:hover {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.25);
  background-color: rgba(255, 255, 255, 1);
}

.top-quick-links a {
  color: var(--main-blue) !important;
  font-weight: 500;
  text-decoration: none;
  padding: 2px 5px;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.top-quick-links a:hover {
  background-color: var(--light-blue);
  color: var(--dark-blue) !important;
}

.header-battery-info {
  display: flex;
  align-items: center;
  background-color: rgba(187, 222, 251, 0.3);
  padding: 8px 15px;
  border-radius: 30px;
  margin: 0 auto;
  border: 1px solid var(--light-blue);
  box-shadow: 0 2px 10px rgba(25, 118, 210, 0.15);
  justify-content: center;
  transition: all 0.3s ease;
}

.header-battery-info:hover {
  background-color: rgba(187, 222, 251, 0.5);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.25);
}

.header-info-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  padding: 2px 8px;
}

.header-info-item:last-child {
  margin-right: 0;
}

.header-info-item strong {
  margin-right: 5px;
  color: var(--main-blue);
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
}

.header-info-item .info-value {
  font-weight: bold;
  color: var(--text-dark);
  min-width: 30px;
  font-size: 14px;
}

.header-info-item .battery-icon {
  width: 32px;
  height: 16px;
  border: 2px solid var(--main-blue);
  border-radius: 3px;
  position: relative;
  margin-left: 8px;
  padding: 1px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-info-item .battery-icon:after {
  content: '';
  position: absolute;
  right: -4px;
  top: 4px;
  height: 8px;
  width: 3px;
  background: var(--main-blue);
  border-radius: 0 2px 2px 0;
}

.battery-level {
  height: 100%;
  background-color: #ddd;
  border-radius: 1px;
  transition: all 0.5s ease;
}

.battery-level.high {
  background: linear-gradient(to right, #81c784, #4caf50);
}

.battery-level.medium {
  background: linear-gradient(to right, #ffb74d, #ffa726);
}

.battery-level.low {
  background: linear-gradient(to right, #e57373, #f44336);
}

.battery-level.charging {
  background: linear-gradient(to right, #64b5f6, #2196f3);
  animation: charging 1.5s infinite;
}

@keyframes charging {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/* 信息展示行样式 */
.homepic {
  padding: 15px;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, auto);
  gap: 15px;
  margin-bottom: 20px;
  max-width: 1200px; /* 限制最大宽度 */
  width: 100%;
}

.info-item {
  background-color: rgba(187, 222, 251, 0.15);
  padding: 10px 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid rgba(25, 118, 210, 0.1);
  text-align: center;
}

.info-item:hover {
  background-color: rgba(187, 222, 251, 0.3);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
  transform: translateY(-2px);
}

.info-item strong {
  display: block;
  color: var(--main-blue);
  margin-bottom: 5px;
  font-size: 13px;
  font-weight: 600;
  text-align: center;
}

.info-item label {
  display: block;
  color: var(--text-dark);
  font-weight: 500;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

/* 左侧菜单 */
.leftMenu li a:hover {
  background-color: var(--light-blue) !important;
  color: var(--text-dark) !important;
}

.leftMenu li.on a, .leftMenu li.active {
  background-color: var(--main-blue) !important;
  color: var(--text-light) !important;
}

/* 盒子和容器 */
.homeBox {
  background: var(--light-gray) !important;
  border: 1px solid var(--light-blue) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.homeBox h2 {
  background: var(--main-blue) !important;
  color: var(--text-light) !important;
}

/* .boxInner a:hover {
  background: var(--light-blue) !important;
  transform: scale(1.05) !important;
} */

/* Antd 风格按钮样式 */
.content input.button, .btnWrp input, .liginbox input.button {
  background: var(--main-blue) !important;
  color: var(--text-light) !important;
  border: 1px solid var(--dark-blue) !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 0 rgba(25, 118, 210, 0.05);
  font-size: 14px;
  font-weight: 500;
  padding: 4px 15px;
  height: 32px;
  line-height: 1.5715;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(.645,.045,.355,1);
  outline: none;
}
.content input.button:hover, .btnWrp input:hover, .liginbox input.button:hover {
  background: var(--dark-blue) !important;
  border-color: var(--main-blue) !important;
  color: var(--text-light) !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}
.content input.button:active, .btnWrp input:active, .liginbox input.button:active {
  background: #1253a2 !important;
  border-color: #1253a2 !important;
  color: var(--text-light) !important;
}
@media only screen and (max-width: 600px) {
  .content input.button, .liginbox input.button {
    padding: 10px;
    font-size: 14px;
    height: 40px;
  }
}

/* 登录框样式 */
.liginbox {
  border-radius: 8px !important;
  background-color: var(--white) !important;
  overflow: hidden !important;
}

.title_box {
  background: var(--main-blue) !important;
  color: var(--text-light) !important;
  padding: 15px 28px !important;
}

.box1 {
  border: none !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.box2 {
  background: var(--light-blue) !important;
}

.box3 {
  background-color: var(--white) !important;
}

/* 移动菜单 */
.sidenav {
  background-color: var(--white) !important;
  border-right: 1px solid var(--main-blue) !important;
  z-index: 1000 !important;
}

.side-main-menu li a {
  color: var(--text-dark) !important;
  padding: 12px 15px !important;
  display: block !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  font-weight: 500 !important;
}

.side-main-menu li a:hover, .side-main-menu li a.on, .side-main-menu li a.active {
  background-color: var(--main-blue) !important;
  color: var(--text-light) !important;
}

.side-submenu-container {
  margin-top: 10px !important;
  border-top: 1px solid rgba(25, 118, 210, 0.1) !important;
}

.side-submenu-container ul {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.side-submenu-container ul li {
  padding: 0 !important;
  margin: 0 !important;
}

.side-submenu-container ul li a {
  padding: 10px 20px 10px 30px !important;
  display: block !important;
  color: var(--text-dark) !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  border-left: 4px solid transparent !important;
}

.side-submenu-container ul li a:hover, 
.side-submenu-container ul li a.active {
  background-color: rgba(187, 222, 251, 0.3) !important;
  color: var(--main-blue) !important;
  border-left-color: var(--main-blue) !important;
}

/* 移动端侧边栏底部快速链接样式 */
.side-footer {
  padding: 15px !important;
  border-top: 1px solid var(--light-blue) !important;
  text-align: center !important;
  position: absolute !important;
  bottom: 0 !important;
  width: 100% !important;
  background-color: rgba(187, 222, 251, 0.2) !important;
  box-sizing: border-box !important;
}

.side-footer a {
  color: var(--main-blue) !important;
  padding: 5px 8px !important;
  margin: 0 2px !important;
  border-radius: 4px !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.side-footer a:hover {
  background-color: var(--light-blue) !important;
  color: var(--dark-blue) !important;
}

/* 子菜单样式 */
#phoneMenu {
  list-style: none !important;
  padding: 0 0 60px 0 !important; /* 为底部菜单留出空间 */
  margin: 0 !important;
  overflow-y: auto !important;
  max-height: calc(100vh - 200px) !important;
}

.ant-menu-sub {
  padding: 0 !important;
  margin: 0 !important;
}

.ant-menu-sub .ant-menu-item {
  margin: 0 !important;
}

#phoneMenu li {
  padding: 0 !important;
  margin: 0 !important;
}

#phoneMenu .submenu-item {
  padding: 12px 20px 12px 30px !important;
  display: block !important;
  color: var(--text-dark) !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  border-left: 4px solid transparent !important;
  font-size: 15px !important;
}

#phoneMenu .submenu-item:hover,
#phoneMenu .submenu-item.active {
  background-color: rgba(187, 222, 251, 0.3) !important;
  color: var(--main-blue) !important;
  border-left-color: var(--main-blue) !important;
}

/* 下拉菜单样式 */
.dropdown-btn {
  padding: 12px 15px !important;
  width: 100% !important;
  text-align: left !important;
  border: none !important;
  background-color: transparent !important;
  color: var(--text-dark) !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  outline: none !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.dropdown-btn:hover,
.dropdown-btn.active {
  background-color: var(--main-blue) !important;
  color: var(--text-light) !important;
}

.dropdown-btn .menu-arrow {
  transition: transform 0.3s ease !important;
  font-size: 14px !important;
}

.dropdown-btn.active .menu-arrow {
  transform: rotate(180deg) !important;
}

.dropdown-container {
  display: none !important;
  background-color: rgba(187, 222, 251, 0.1) !important;
  padding: 5px 0 !important;
}

/* 移动菜单按钮 */
.mobileMenu {
  position: fixed !important;
  top: 15px !important;
  left: 15px !important;
  z-index: 100 !important;
  font-size: 24px !important;
  cursor: pointer !important;
  color: var(--main-blue) !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
  padding: 5px 10px !important;
  border-radius: 5px !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
  display: none !important;
}

@media screen and (max-width: 768px) {
  .mobileMenu {
    display: block !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
}

/* 页脚 */
.footer .footerMast {
  background: var(--main-blue) !important;
  color: var(--text-light) !important;
}

.footer .footerLft, .footer .footerRgt {
  background: var(--main-blue) !important;
}

.logoTxt {
  color: var(--text-light) !important;
}

/* 链接颜色 */
a {
  color: var(--dark-blue) !important;
}

a:hover {
  color: var(--main-blue) !important;
}

.ant-menu-wrapper .sidebar-quick-links a {
  color: var(--dark-blue) !important;
}

.ant-menu-wrapper .sidebar-quick-links a:hover {
  color: var(--main-blue) !important;
  text-decoration: underline !important;
}

/* 表格样式 */
.rowHead {
  background: var(--main-blue) !important;
  color: var(--text-light) !important;
}

.dataTbl10 th {
  background: var(--light-blue) !important;
}

/* 弹窗样式 */
.popUpBox {
  border: 1px solid var(--main-blue) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.pBoxCont h2 {
  color: var(--main-blue) !important;
  border-bottom: 1px solid var(--light-blue) !important;
}

/* 输入框样式 */
.content input.textfield, .content input.textfield1, .content select, 
.content textarea, .liginbox input.textfield, .liginbox input.textfield1, 
.liginbox select, .liginbox textarea {
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
}

.content input.textfield:focus, .content input.textfield1:focus, .content select:focus, 
.content textarea:focus, .liginbox input.textfield:focus, .liginbox input.textfield1:focus, 
.liginbox select:focus, .liginbox textarea:focus {
  border-color: var(--main-blue) !important;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
  outline: none !important;
}

/* 内容区域 */
.content {
  padding: 15px !important;
  background-color: var(--white) !important;
}

.content h1 {
  color: var(--main-blue) !important;
  border-bottom: 1px solid var(--light-blue) !important;
}

/* 修改登录界面样式 */
.login-box {
  background-color: #fff !important;
  border: 1px solid var(--main-blue) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.15) !important;
}

.login-button {
  background-color: var(--main-blue) !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 12px 20px !important;
  font-weight: bold !important;
  text-transform: uppercase !important;
  transition: all 0.3s ease !important;
}

.login-button:hover {
  background-color: var(--dark-blue) !important;
  box-shadow: 0 4px 8px rgba(13, 71, 161, 0.3) !important;
}

.error-message {
  color: #f44336 !important;
}

/* 登录页面增强样式 */
.login-container {
  animation: gradientBG 15s ease infinite;
  background-size: 200% 200% !important;
}

.user-avatar i {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.admin-label {
  position: relative;
  display: inline-block;
}

.admin-label::after {
  content: '';
  position: absolute;
  width: 50px;
  height: 2px;
  background: #bbdefb;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
}

.password-toggle {
  transition: all 0.3s ease;
}

.password-toggle:hover {
  color: var(--dark-blue) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

/* 移动端下拉式信息显示 */
.mobile-info-toggle {
  width: 100%;
  max-width: 1200px;
  background-color: var(--main-blue);
  color: white;
  padding: 12px 15px;
  border-radius: 6px;
  margin: 0 auto 10px;
  text-align: center;
  font-weight: 500;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: none; /* 默认隐藏 */
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 16px;
}

.mobile-info-toggle:hover {
  background-color: var(--dark-blue);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.mobile-info-toggle i {
  margin-left: 8px;
  transition: transform 0.3s ease;
  font-size: 14px;
}

/* 在media查询中定义特定的移动端显示规则 */
@media screen and (max-width: 768px) {
  /* 移动端下拉框显示规则 */
  .mobile-info-toggle {
    display: flex !important; /* 强制在移动端显示 */
    width: 90%;
  }
  
  /* 移动端默认隐藏信息行，通过JS控制显示 */
  .info-row {
    display: none !important; /* 默认强制隐藏 */
  }
  
  .info-row.open {
    display: grid !important; /* 强制在打开状态下显示 */
    grid-template-columns: repeat(1, 1fr);
    width: 90%;
    margin: 0 auto 15px;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    border: 1px solid var(--light-blue);
    border-top: none;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease-out;
  }
  
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* 移动端下拉框处于打开状态的样式 */
  .mobile-info-toggle.open {
    border-radius: 6px 6px 0 0;
    margin-bottom: 0;
    background-color: var(--dark-blue);
  }
  
  /* 旋转箭头图标 */
  .mobile-info-toggle.open i {
    transform: rotate(180deg);
  }
  
  /* 移动端列表项样式 */
  .info-row.open .info-item {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
    margin-bottom: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    text-align: left;
    padding: 12px 15px;
  }
  
  .info-row.open .info-item:last-child {
    border-bottom: none;
  }
  
  .info-row.open .info-item strong {
    text-align: left;
    margin-bottom: 0;
    margin-right: 10px;
    flex: 0 0 auto;
  }
  
  .info-row.open .info-item label {
    text-align: right;
    font-weight: 400;
  }
  
  .header-battery-info {
    padding: 5px 10px;
    flex-direction: column;
    width: 90%;
    margin-top: 40px;
  }
  
  .header-info-item {
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .top-quick-links {
    position: relative;
    top: 0;
    right: 0;
    width: 90%;
    margin: 10px auto;
    text-align: center;
    border-radius: 6px;
  }
}

@media screen and (max-width: 580px) {
  /* 较小屏幕上的调整 */
  .mobile-info-toggle,
  .info-row.open {
    width: 95%;
  }
}

@media screen and (max-width: 480px) {
  .header-battery-info {
    padding: 5px;
  }
  
  .header-info-item strong {
    font-size: 11px;
  }
  
  .header-info-item .battery-icon {
    width: 28px;
    height: 14px;
  }
  
  .top-quick-links {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .top-quick-links a {
    padding: 2px 3px;
  }
  
  /* 超小屏幕调整 */
  .info-item {
    font-size: 11px;
    padding: 10px;
  }
  
  .info-item strong {
    margin-bottom: 2px;
    font-size: 11px;
  }
  
  .info-item label {
    font-size: 11px;
  }
} 

/* 全局分组样式 */
.content {
  width: 100%;
  background: #f5f7fa;
  min-height: 100vh;
}
.form-section {
  margin: 0;
  padding: 24px 0;
  max-width: none;
}
.form-group {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
  padding: 24px 32px 16px 32px;
  margin-bottom: 28px;
  border: 1px solid #e6f7ff;
}
.form-group-title {
  font-size: 15px;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 18px;
  letter-spacing: 1px;
} 