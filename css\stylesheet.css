body 			{		margin: 0px; font:normal 12px <PERSON>l, Helvetica, sans-serif; color:#333; background:#fff}
a				{		text-decoration:none; color:#014188		}
a:hover			{		color:#333	}
a:focus	{	outline:0	}
a img {	border:0;	}
.clear			{	}
.clearCrm {	display: block; clear: both; }
form, .navigation ul li, .navigation ul, .homeBox h2, .boxInner h3, .boxInner p, .leftMenu, .leftMenu li 			{	margin:0; padding:0;	}
/* .mainColumn {	min-height:455px;	} */
 .mainColumn, .footer	{	max-width:964px;	margin:auto;	background:#fff;	display:flex;		}
/* .header {	background:url(../images/nav-left.png) no-repeat left bottom #fff;    	} */
/* .header {	flex-direction:row;	justify-content:space-between;	align-items:center;		flex-wrap:wrap;} */
.mainColumn {	display: flex;padding:20px;flex-wrap: wrap;}
.lable14 {	float: left; width:80%; text-align:left; margin:0 auto 10px auto; font-weight:bold; color:#f00; 	}
.logo, .loginArea,  .homeBox,  .footer div, .footer span, .leftBar, .content 
{	float:left;	}

.logo 			{	width:100px; padding:10px 7px;	}
.loginArea		{	width:840px; text-align:right; padding:25px 10px 0 0;   line-height:18px;	}
.loginArea span a	{	color:#000;	}
.loginArea span a:hover	{	text-decoration:underline	}
.navigation		{	width: 100%;background: linear-gradient(to right, #105bb1, #0480be); height: 50px; margin-left: 8px; border-radius: 8px; display: flex;	}
.navigation ul {	background: none; height: 50px; display: flex; justify-content: space-around; align-items: center; width: 100%;	}
.navigation ul, .navigation .date	{	 height:38px;    	}
.navigation .date	{	width:186px; text-align:right; font-size:11px;  color:#fff; line-height:32px; padding:0 4px 0 0	}
.navigation ul li 	{	list-style: none; padding: 0;	}
.navigation ul li a {	margin: 0; text-align: center; display: block; font-size: 16px; color: #ffffff; padding: 10px 20px; border-radius: 5px; transition: background 0.3s, box-shadow 0.3s;	}
.navigation ul li a:hover {	color: #ffffff; background: #094d96; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);	}
.navigation ul li a.active {
    font-weight: bold; /* 加粗字体 */
    background: #0e6b99; /* 选中项背景色 */
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3); /* 内阴影 */
}
.navigation ul li a.on {	font-weight:bold	}
.h2Router{font-size:20px;	font-weight:normal; color:#62635f; padding:3px 10px 4px 30px; background:#d3d8cc;margin:0;}
 .homeBoxesContainer{display: flex;margin-bottom: 20px; justify-content: space-between;min-width: 200px;max-width: 1000px;	 flex: 1 1 100%;}
.homeBox 			{	overflow: hidden;border-radius: 10px; background:#f0f6ea; min-height:420px; margin-bottom:10px;	box-sizing: border-box;width: 480px;	}
.marginLR 			{	overflow: hidden;border-radius: 10px;	 width: 100%; flex: 1 1 100%;}
.homeBox h2			{	font-size:20px;	font-weight:normal; color:#62635f; padding:3px 10px 4px 30px; background:#d3d8cc; }
.boxInner a			{	 display: block;background: #f0f6ea;padding: 10px 15px 10px 30px;color: #333;transition: transform 0.3s ease, background 0.3s ease;	 }
.boxInner a:hover {
    transform: scale(1.05); /* 放大1.05倍 */
    background: #e1f1fe; /* 改变背景颜色 */
}
.boxInner a span.heading1    {	font-size:16px; color:#52587a; font-weight:bold; padding-bottom:10px;  display:block 	}
.boxInner a label.link1    {	color:#52587a; font-weight:bold; float: right; display:block; text-decoration:underline	}

.brdr1 				{	border-bottom:1px solid #c4c8bd	}
.brdr2 				{	border-top:1px solid #fff	}

.leftBar			{	width:100%;	padding:10px;	background-color:#FAFAFA;		box-shadow:0 2px 5px rgba(0, 0, 0, 0.1);	overflow-y:auto;	margin-bottom:20px;	border-radius:8px;	}
.leftMenu 			{	list-style:none;	padding:0;	margin:0;	}
.leftMenu li {	margin-bottom:10px;	}
.leftMenu li a {	display:flex;	align-items:center;	padding:12px 20px;	font-size:16px;	color:black;	background-color:transparent;	border-radius:4px;	transition:background-color 0.3s,color 0.3s;	text-decoration:none;	}
.leftMenu li a .menu-icon {	margin-right:15px;	font-size:18px;	}
.leftMenu li a:hover {	background-color:#d8dadb;	color:black;	}
.leftMenu li.on a {	background-color:#1abc9c;	color:black;	}
.leftMenu li.active {	background-color:#c8e5fc;	color:black;border-radius: 5px;	}
/* .leftMenu li.on a .menu-icon {	color:black;	} */

.content			{		padding:15px 20px;	min-height:409px;  margin-bottom:5px; padding-right:10px; width:100%;	flex:1;	background-color:#fff;	border-radius:8px;	}
.contQS 			{	padding-right:242px; min-height:360px	}
.content label 	{	display:block;  padding:5px 0;   font-weight:bold; color:#666	}
.content label.subttl {	 font-weight:normal;     	}
.content label.subttl1 {        font-weight:bold; margin-top:0px;      }
.content h1  {	 color:#555789; font-weight:bold; font-size:15px; margin:0; padding:0 0 5px 0 ; 	}
.subhdng {	display:block; color:#000; padding:0 0 5px 0;	}
.content input.textfield, .content input.textfield1, .content select, select.combox1, select.combox2, .content input.textfield2	{ border:1px solid #999999; padding:2px 1px 1px 4px; color:#333;   font:normal 12px Arial, Helvetica, sans-serif; list-style:14px; width:180px;  margin-bottom:7px; height:18px			}
.content input.textNTPfield{ border:1px solid #999999; padding:2px 1px 1px 4px; color:#333;   font:normal 12px Arial, Helvetica, sans-serif; list-style:14px; width:300px;  margin-bottom:7px; height:18px			}

/* .content input.button	{	border:1px solid #7c7d7a; border-left-color:#a4a4a1;  border-top-color:#c0c1bd;    background:#c0c1bd;
/* .content input.button	{	border:1px solid #7c7d7a; border-left-color:#a4a4a1;  border-top-color:#c0c1bd;    background:#c0c1bd;
				-moz-border-radius: 3px; -webkit-border-radius : 3px; font:bold 13px Arial; color:#000000; cursor:pointer; padding:5px 15px;	}*/

.content input.button	{	border:1px solid #959692; background: url(../images/button-bg2.png) repeat-x #d9d9d9;  -moz-border-radius: 3px; -webkit-border-radius : 3px; font:bold 13px Arial; color:#000000; cursor:pointer; padding:5px 15px;	}
.content a.help		{	height:26px; width:26px;  background:url(../images/help.png) no-repeat; float:right; margin-bottom:-15px		}
.content select, select.combox1, .select.combox2 		{	padding:1px; height:35px; width:187px;	}
select.combox1 {   margin:10px 0 0 20px; }
select.combox2 {    width: 340px;  margin:15px 0 0 20px; }
.content .formBox	{	border-bottom:1px solid #adaeab; padding-bottom:10px; margin-bottom:5px;	}
.content input.textfield1 {	width:45px; margin:0 0 -5px 0	}
.content input.chk	{	margin:3px 5px -2px 5px; padding:0; margin:-8px 5px 8px 5px	}
.lablText	{	margin:0; font-size:12px; line-height:24px;   padding:0;  position:absolute; line-height:30px;	}
.content input.textfield2	{	width:80px; float:left; margin:-2px 5px 2px 0		}
strong.dot 	{	  font-size:14px; color:#333; margin:0 5px; 	}
.content select.combo1	{	width:100px;	 float:left; margin:2px 0 -2px 0	}

.title_row {	  margin-bottom:5px;  	}
.title_row span {	float:left; background: url(../images/tab-bg1.png) no-repeat right top #e9dfdf ; padding:0px 22px 0px 8px; font-size:14px; line-height:28px; color:#666; margin-right:2px 	}
.title_row span.on {	background: url(../images/tab-bg2.png) no-repeat right top #545785; color:#ffffff	}

.devices  {	float:left; width:300px; padding:20px 0 0 0; margin:5px 0 0 0; font-size:13px; font-weight:bold 	}
.devices img, .devices label {	float:left; margin:0 5px 0 0	} .devices img {	margin-top:-15px	}
.footer				{	 width:964px; 	}
.footer div.footerLft, .footer div.footerRgt {	 background:url(../images/footer-bg-lt.png) no-repeat ; float:left;width:10px; height:39px;	}
.footer div.footerRgt {	 background:url(../images/footer-bg-rt.png) no-repeat ; }
.footer .footerMast  {	background:#bbb; height:39px; width:944px; float:left 	}
.footer .footerMast div			{	width:644px; padding:13px 0 0 15px;  }
.footer span		{	height:26px; margin:12px 0 0 0		}
/* .footer span.footerTxt		{	width:119px; padding:0px 0 0 10px;   margin:12px 0 0 0   } */
.footer span.logoTxt{	font-size:12px; font-weight:bold;  letter-spacing:1px;width:170px; margin:10px 0 0 400px;  	}

/* .loginBody {	background:#f2f2f2	} */
.liginbox   {	width:600px;	margin:300px auto 0 auto;  color:#666666;align-items: center;justify-content: center; 	}
.liginbox	span	{	padding-left:95px;	}
.liginbox	span_webdav	{	padding-left:0px;	}

.box1		 {  border:1px solid #5b7176; margin:10px 0; padding:1px; background:#fff	}
.title_box   {	background:url(../images/nav-bg.png) repeat-x; padding:7px 28px; color:#fff; font-weight:bold;	}
.box2		 {	text-align:right; padding:7px 20px; background:url(../images/login-bg.png) repeat-x #d2deba 	}
.box2 select {	width:125px; padding:2px; height:24px; border:1px solid #5b7176; color:#666;  	}
.box3 		 {	border-top:1px solid #fff; padding:15px 18px 15px 30px;	}
.quicksetupbox3 		 {	border-top:1px solid #fff; padding:15px 18px 15px 30px; background:url(../images/button-bg2.png) repeat-x 0 bottom	}
.liginbox label	{	display:block; font-size:14px; font-weight:normal	}
.liginbox label.version { display: inline; font-size: 12px; padding-left:50px;      }
.liginbox label.copy { display: inline-block; font-size: 12px; padding-left: 5px; width: 450px;     }
.liginbox input	{ border:1px solid #909090; padding:1px 4px 3px 5px; height:23px; font-size:14px; line-height:14px; color:#666; margin:7px 0 15px 0; padding-top:6px\9; height:20px\9; }
.liginbox input.button	{	border:1px solid #192e56; background:url(../images/button-bg.png) repeat-x; font:normal 17px Arial; color:#fff; cursor:pointer; height:34px; padding:2px 14px; -moz-border-radius: 3px; -webkit-border-radius : 3px;}
.title_box label {font-size:16px; }

.dstaTbl1 {	border:1px solid #99cc66; border-left:0; margin:40px 5px 0 0	}
.dstaTbl1 td, .dstaTbl1 th	{	border-left:1px solid #99cc66; padding:6px 3px 6px 9px; text-align:left	}
.rowHead	{	background:#c4df8f	}
.rowOdd	{	background:#fff	}
.rowEven	{	background:#f1f1f1	}

.dstaTbl1 .status {	position:absolute;  margin:1px 0 0 180px; cursor:pointer;	}

.message1	{	background:url(../images/msg-bg.png) repeat-x; border:1px solid #d8dad5; -moz-border-radius: 5px; -webkit-border-radius : 5px; height:36px; 	}
.message1 span		{	display:block; padding:11px 0 8px 45px;  	}
.message1 span.sentMsg	{	background:url(../images/status-icon4.png) no-repeat 10px 7px;	}
.message1 span.errMsg	{	background:url(../images/status-icon5.png) no-repeat 13px 7px;	}

.arrow {    margin: -30px 0 30px 0; }
.lable11, .chk11 {  float: left;   }
.chk11 {    margin: 3px 8px -3px 0; padding: 0; border: 0; margin:0px 8px 3px 0 }
.chk12 {    margin: 7px 8px -7px 0; margin: 3px 8px -10px 0; }
.boldTxt {  font-weight: bold }
.content label.error, label.error 	{	font-size:11px; font-weight:normal; color:#f00	}


.dataTbl10 {	border:1px solid #99cc66;  margin:15px 0 	}
.dataTbl10 th {	background:#c4df8f	}
.dataTbl10 th, .dataTbl10 td  {	text-align:left; padding: 5px 6px; border-left:1px solid #99cc66 		}
.dataTbl10 th.close, .dataTbl10 td.close, .dataTbl10 {	border-left:0;   	}
.dataTbl10 .rowEven {	background:#e0e4d8	}


.exit {	float:left; font-weight:bold; margin:10px 0 -10px 0; cursor:pointer	}
.exit:hover {	 color:#000	}
.content label.title {	 color:#555789; font-weight:bold; font-size:14px; margin-top:0;	}

.unblock {	background:url(../images/status-icon1.png) no-repeat 0 0px; padding-left:18px		}
.block {	background:url(../images/status-icon6.png) no-repeat 0 0px; padding-left:18px		}
.unblock:hover, .block:hover {	text-decoration:underline	}

.inlineDiv { display:inline; 	}

.helpBody .content {  width:890px; 	}
ul.helpList, .ul.helpList li, ul.helpList ul, ul.helpList ul li, .helpBody h2 {	margin:0; padding:0; font-size:14px; font-weight:bold 	}
ul.helpList, ul.helpList ul {	list-style:none	}
ul.helpList {	margin-top:10px;	}
ul.helpList ul li {	margin:4px 0 4px 20px; font-weight:normal;  	}
.helpBody h2 {	border-top:1px solid #c4c8bd; margin-top:15px; padding-top:15px; color:#555789; background:#fff  }

 strong.dot, .content select.combo1, .content input.textfield1 {	float:left;		 }
  strong.dot {	padding-top:3px;	}
  form {	margin:0; padding:0;	}

.liginbox input.button { border:0px; background:url(../images/button-bg.png) no-repeat; font:normal 17px Arial; color:#fff; cursor:pointer; height:34px; width:97px; padding-bottom:4px\9  }
.btnWrp {	margin:0 0 0 2px; padding:0; display:inline-block; display:inline; transition-duration: 0.4s; height:31px; padding-right:2px; height:31px;	}
.btnWrp:hover {	 background-color: #c8e5fc;  color: white;}
.btnbgWrp {	margin:0 0 0 2px; padding:0; display:inline-block; display:inline; transition-duration: 0.4s; background:url(../images/button-bg.png) right -31px; height:31px; padding-right:6px; height:31px;	}
.btnWrp input {	  padding:3px 8px 4px 14px; font:bold 13px Arial; color:#000000; cursor:pointer; height:31px;  margin:0; padding:3px 2px 4px 8px;	}

.helpBody .helpTitle {  background: none; font: bold 15px arial; color: #fff; width: 572px; margin-top: 3px; margin-left: 186px; text-align: center; }.footerHelp { width: 1002px; margin: 0 auto; background: #fff; }

.mar_logo {   float: right   ; background:url(../images/ZYlogo.png) no-repeat;  height: 90px; margin-top: -90px;  }
.liginbox a:hover { text-decoration: underline; }
input{ vertical-align:middle; margin:0; padding:0}
.file-box{ position:relative;width:340px}
.txt{ height:22px; border:1px solid #cdcdcd; width:160px;}
.btn{ background-color:#FFF; border:1px solid #CDCDCD;height:24px; width:70px;}
.file{ 
    position:absolute; 
    top:0px; 
    left:0px; 
    height:24px; 
    filter:alpha(opacity=0); 
    opacity: 0; 
    width:270px; 
    font-size:18px; 
}
.disabledBtn {
    background:url(../images/disabledBtn.png) repeat scroll right -31px transparent;  
}
.disabledBtn input
{
    background:url(../images/disabledBtn.png) no-repeat scroll 0 0 transparent;    
    color:#adadad;
    cursor:default;
}

.shareFilebox   {	width:1000px;	margin:100px auto 0 auto;  color:#666666; 	}
.shareFilebox	span	{	padding-left:0px;	}

.progressbar{ 
    background-color:#eee; 
    color:#222; 
    height:16px; 
    width:150px; 
    border:1px solid #bbb; 
    text-align:center; 
    position:relative; 
} 
.progressbar .bar { 
    background-color:#6CAF00; 
    height:16px; 
    width:0; 
    position:absolute; 
    left:0; 
    top:0; 
    z-index:10; 
} 
.progressbar .progressText { 
    height:16px; 
    position:absolute; 
    left:0; 
    top:0; 
    width:100%; 
    line-height:16px; 
     
    z-index:100; 
} 
.mobileMenu{
    display: none;
}
.sidenav {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #fff;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
    box-shadow: 2px 0 10px rgba(0,0,0,0.2);
}

.sidenav a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 16px;
    color: #333;
    display: block;
    transition: 0.3s;
}

.sidenav a:hover {
    color: #105bb1;
}

.sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}

/* 侧边栏主菜单样式 */
.side-main-menu {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    border-bottom: 1px solid #eee;
}

.side-main-menu li {
    margin: 0;
    padding: 0;
}

.side-main-menu li a {
    display: block;
    padding: 12px 15px;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    border-left: 4px solid transparent;
    transition: all 0.3s;
}

.side-main-menu li a:hover {
    background-color: #f5f5f5;
    border-left: 4px solid #1890ff;
}

.side-main-menu li a.on {
    background-color: #e6f7ff;
    color: #1890ff;
    border-left: 4px solid #1890ff;
}

/* 二级菜单容器 */
.side-submenu-container {
    margin-bottom: 20px;
}

.side-submenu-container ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* 侧边栏底部链接 */
.side-footer {
    padding: 15px;
    border-top: 1px solid #eee;
    text-align: center;
}

/* 下拉菜单样式 */
.dropdown-btn {
    padding: 10px 15px;
    text-decoration: none;
    font-size: 16px;
    color: #333;
    display: block;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    outline: none;
    transition: all 0.3s;
    border-left: 4px solid transparent;
    font-weight: bold;
}

.dropdown-btn:hover {
    background-color: #f5f5f5;
    border-left: 4px solid #1890ff;
}

.dropdown-btn.active {
    background-color: #e6f7ff;
    color: #1890ff;
    border-left: 4px solid #1890ff;
}

.dropdown-container {
    display: none;
    background-color: #f9f9f9;
    padding-left: 15px;
}

.dropdown-container a.submenu-item {
    padding: 10px 15px 10px 25px;
    text-decoration: none;
    font-size: 15px;
    color: #333;
    display: block;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.dropdown-container a.submenu-item:hover {
    background-color: #f0f0f0;
    border-left: 3px solid #1890ff;
}

.dropdown-container a.submenu-item.active {
    background-color: #e6f7f3;
    border-left: 3px solid #1890ff;
    font-weight: bold;
}

.homepic {
    margin-left: 220px;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    align-items: center;
}

.info-item {
    margin-right: 30px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.battery-info {
    display: flex;
    align-items: center;
}

.battery-icon {
    width: 40px;
    height: 20px;
    border: 2px solid #333;
    border-radius: 3px;
    position: relative;
    margin-left: 10px;
    padding: 1px;
}

.battery-icon:after {
    content: '';
    position: absolute;
    right: -5px;
    top: 6px;
    height: 8px;
    width: 3px;
    background: #333;
    border-radius: 0 2px 2px 0;
}

.battery-level {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 1px;
    width: 0%;
    transition: width 0.3s ease;
}

/* 电池电量颜色 */
.battery-level.high {
    background-color: #4CAF50; /* 绿色 */
}

.battery-level.medium {
    background-color: #FFC107; /* 黄色 */
}

.battery-level.low {
    background-color: #F44336; /* 红色 */
}

.battery-level.charging {
    background-color: #2196F3; /* 蓝色 */
    animation: charging 1.5s infinite;
}

@keyframes charging {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .homepic {
        margin-left: 0;
        padding: 15px;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-item {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }
}

.mobileMenubtn{
    display: none;
}
@media only screen and (max-width: 600px) {
    .homepic {
        display: flex;
        flex-direction: column; 
        align-items: center;
        justify-content: center;
    }
    .liginbox {
        width: 90%;
        max-width: 400px;
        margin: 200px auto;
    }

    .mobileMenubtn{
        display: block;
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 999;
        font-size: 25px;
        background-color: #fff;
        color: #1890ff;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .homeBoxesContainer {
        flex-direction: column;
    }

    .homeBox {
        width: 100%;
        margin-bottom: 20px;
    }

    .leftMenu li a {
        padding: 10px;
        text-align: center;
    }

    /* 调整按钮大小 */
    .content input.button, .liginbox input.button {
        padding: 10px;
    }
    .navigation {
        display: none;
    }
    .footer ,.loginArea,.leftBar{
        display: none;
    }
    .logo{
        margin: auto;
        padding: 20px;
    }
    .mobileMenu{
        display: block;
        float: left;
        font-size:25px;
    }
    .dropdown-btn {
        padding: 6px 8px 6px 16px;
        text-decoration: none;
        font-size: 20px;
        color: #818181;
        display: block;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        cursor: pointer;
        outline: none;
      }
      .dropdown-container {
        padding-left: 8px;
      }
      .fa-caret-down {
        float: right;
        padding-right: 8px;
      }
      .dropdown-container a{
        padding: 8px 8px 8px 32px;
        text-decoration: none;
        font-size: 16px;
        
        color: #818181;
      }


}

/* Ant Design 风格布局 */
.main-layout {
    display: flex;
    min-height: 100vh;
    background-color: #f0f2f5;
    justify-content: center;
}

.ant-layout-content {
    margin: 0 auto;
    padding: 20px;
    max-width: 1200px;
    width: 100%;
    flex: 1;
    background-color: #f0f2f5;
    transition: all 0.2s;
    position: relative;
}

.ant-layout-sider {
    width: 200px;
    background-color: #ffffff;
    position: absolute;
    height: calc(100% - 40px);
    left: 0;
    top: 20px;
    transition: all 0.2s ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 999;
    overflow-y: auto;
    border-radius: 4px;
}

.homepic {
    margin-left: 220px;
    margin-bottom: 20px;
}

.mainColumn {
    margin-left: 220px;
    display: flex;
    padding: 20px;
    flex-wrap: wrap;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Logo区域 */
.logo-container {
    height: 64px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* background-color: #ffffff; */
    border-bottom: 1px solid #f0f0f0;
}

.logo-container img {
    height: 32px;
}

.logo-container h1 {
    color: #333333;
    font-size: 18px;
    margin: 0;
    margin-left: 12px;
    font-weight: 600;
}

/* Ant Design菜单样式 */
.ant-menu-wrapper {
    height: calc(100% - 64px);
    overflow-y: auto;
    position: relative;
    padding-bottom: 60px; /* 为底部快捷链接预留空间 */
}

.ant-menu {
    background-color: #ffffff;
    color: rgba(0, 0, 0, 0.65);
    border-right: none;
    padding: 0;
    margin: 0;
    padding-bottom: 60px; /* 与快捷链接高度保持一致 */
}

.ant-menu-item, .ant-menu-submenu-title {
    color: rgba(0, 0, 0, 0.65);
    height: 40px;
    line-height: 40px;
    margin: 4px 0;
    padding: 0 16px 0 24px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ant-menu-item:hover, .ant-menu-submenu-title:hover {
    color: #1890ff;
    background-color: #e6f7ff;
}

.ant-menu-item-selected {
    color: #1890ff;
    background-color: #e6f7ff;
    font-weight: bold;
}

.ant-menu-item-selected:after {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    border-right: 3px solid #1890ff;
}

.ant-menu-submenu {
    transition: all 0.3s;
}

.ant-menu-submenu-arrow {
    position: absolute;
    right: 16px;
    font-size: 12px;
    transform: scale(0.8);
    transition: transform 0.3s;
}

.ant-menu-submenu-arrow:after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #333;
    position: relative;
    top: -2px;
}

.ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow {
    transform: scale(0.8) rotate(180deg);
}

.ant-menu-sub {
    background-color: #fafafa;
    padding-left: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin: 0;
}

.ant-menu-sub .ant-menu-item {
    padding-left: 48px;
    height: 40px;
    line-height: 40px;
    margin: 0;
}

.ant-menu-hidden {
    display: none;
}

.ant-menu-submenu-inline > .ant-menu-submenu-title:after {
    content: "";
    font-family: "FontAwesome";
    position: absolute;
    right: 16px;
    color: rgba(0, 0, 0, 0.45);
    transition: transform 0.3s;
}

.ant-menu-submenu-open > .ant-menu-submenu-title:after {
    transform: rotate(180deg);
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .ant-layout-sider {
        width: 0;
        overflow: hidden;
    }
    
    .ant-layout-sider.mobile-show {
        width: 200px;
    }
    
    .homepic, .mainColumn {
        margin-left: 0;
    }
    
    #mobileMenubtn {
        display: block;
        position: absolute;
        top: 20px;
        left: 10px;
        z-index: 999;
        background-color: #fff;
        color: #1890ff;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .ant-layout-content {
        padding: 15px;
    }
}

/* 修复之前的CSS错误 */
.clearCrm {
    display: block;
    clear: both;
}



.btnbgWrp {
    display: inline-block;
    padding: 0 0 0 10px;
}

/* 移动端菜单相关 */
#mobileMenubtn {
    display: none;
}

@media screen and (max-width: 768px) {
    #mobileMenubtn {
        display: block;
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 999;
        background-color: #fff;
        color: #1890ff;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    #mySidenav {
        width: 0;
        transition: width 0.3s;
    }
}

/* 侧边栏快捷链接样式 */
.ant-menu-wrapper .sidebar-quick-links {
    padding: 15px;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    margin-top: auto;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    background-color: #ffffff;
    z-index: 10;
    border-radius: 0 0 4px 4px;
}

.ant-menu-wrapper .sidebar-quick-links span {
    display: block;
    margin-bottom: 10px;
}

.ant-menu-wrapper .sidebar-quick-links a {
    color: #1890ff;
    margin: 0 5px;
    font-size: 14px;
    text-decoration: none;
}

.ant-menu-wrapper .sidebar-quick-links a:hover {
    color: #40a9ff;
    text-decoration: underline;
}

/* 备用箭头样式 */
.menu-arrow:not(.fa) {
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid currentColor;
    margin-left: 5px;
    vertical-align: middle;
}




