<div class="content">
  <div class="form-section">
    <div class="form-group">
      <a href='#' class='help' onclick="getHelp('ConfigurationManagement')">&nbsp;</a>
      <label id="title" class="title"></label>
      <div class="formBox">
        <label id='lResotreFactSetting' ></label>
        <label id='lResotreFactSettingText' class="subttl"> </label>
        <div align='right'><span class="btnWrp"><input type='button' id='btnRestoreFactorySettings' value='Restore Factory Settings' onclick='RestoreFactoryConfiguration()'  /></span> </div>
      </div>
      <form enctype="multipart/form-data" id="uploadFileForm" method="post" name="SoftFileUpload" style="display: none;">
        <div class="formBox">
          <label id='lImportCfgFileText1' class="subttl"></label>
          <label id='lImportCfgFileText2' class="subttl"></label>
          <label id='lImportCfgFileText3' class="subttl"></label>
          <div class='file-box' style="width: 550px;">
            <input type='file' id='updateCfgFile' class='file' name='config_backup.bin' onchange="onChangeCfgFile()"/>
            <input type='text' id='txtCfgFileName' class='txt' />
            <input type='button' class='btnWrn' value='Browse' id='btnBrowserFile' />
            <span style="display: none;color:red;" id="lFileFormatError">file format errro.</span>
          </div>
          <br class="clear" />
          <div align='right'>
            <span class="btnWrp ">  
              <input type="submit" id="btnSubmitCnfFile" style="display: none">
              <input type='button' id='btnUpdate' value='Update' onclick="UpdateCfgFile()"/></span>
          </div>
        </div>
        <div class="formBox">
          <label id='lEmportCfgFileText' class="subttl">click link to export cfg file.</label> 
          <a target="_blank" id="lExportLink" style="color:blue;cursor: pointer;" onclick="saveConfiguration()">Export Cfg file.</a>
        </div>
        <div id="ACATSDlog_Setting_div"  class="formBox" style="display: none">
          <label id='lSaveAcatDumplogSetting' > </label>
          <label id="lSdFormatSupportStatus" style="display:inline;"></label><em id="txtSdFormatStatus"></em></br></br>
          <input type="checkbox" id="saveacatlogchk" style="margin-right:8px" />
          <span id="lSaveAcatDumplogIntoSDText"> </span>
          <div align='right'><span class="btnWrp"><input type='button' id='btnSaveAcatDumplogSettings' value='Save' onclick='SaveAcatDumplogSetting()'  /></span> </div>
        </div>
      </form>
    </div>
  </div>
</div>
<!-- 保留弹窗和iframe结构 -->
<div class="popUpBox" id="MBRebooting" style="display: none">
    <h1 id="h1Rebooting">Rebooting...</h1>
    <a  class="close">&nbsp;</a><br style="clear:both" />
    <div align="center">
        <label id="lRebootingText"></label>
    </div>
     
     <div align="center">
        <label id="lRebootingText1"></label>
    </div>
    
</div>


<div class="popUpBox" id="MBConfirm" style="display: none">
 <form enctype="multipart/form-data" id="uploadConfigFileForm" method="post">
    <h1 id="h1Confirm"></h1>
    <a  class="close">&nbsp;</a><br style="clear:both" />
    <div align="center">
        <label id="lConfirmText"></label>
    </div>   
  
    <div class="bottonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:right">
         <a href="#." id="btnModalCancle"  onclick="hm()" class="cancel"></a>
         <span class="btnWrp"> <button  onclick="confManagementConfirmed()" value="OK" id="btnModalOk1"></button></span> &nbsp;&nbsp;
       
     </div>
 </form>
</div>
<div class="popUpBox" id="MBConfirmFactory" style="display: none">
    <h1 id="h1Confirm"></h1>
    <a class="close" style="width: 40px;">&nbsp;</a><br style="clear:both" />
    <div align="center">
        <label id="lConfirmText1"></label>
    </div>

     <div class="bottonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:right" >
            <a href="#." id="btnModalCancle"  onclick="hm()" class="cancel"></a>
            <span class="btnWrp"> <button  onclick="confFactoryConfirmed()" value="OK" id="btnModalOk"></button></span>
            
     </div>

</div>
<iframe id="rfFrame" name="rfFrame" src="about:blank" style="display:none;"></iframe> 