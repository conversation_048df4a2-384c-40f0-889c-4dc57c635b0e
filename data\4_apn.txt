{401,	01,	"Beeline",	"internet.beeline.kz",	"0",	"PAP",	"@internet.beeline",	"beeline"}
{401,	02,	"Kcell",	"internet"}
{401,	77,	"Tele2",	"internet"}
{404,	01,	"Vodafone",	"www"}
{404,	02,	"ZY",	"ZYgprs.com"}
{404,	03,	"ZY",	"ZYgprs.com"}
{404,	04,	"IDEA",	"internet"}
{404,	05,	"Vodafone",	"www"}
{404,	07,	"IDEA",	"internet"}
{404,	09,	"Reliance",	"SMARTNET"}
{404,	10,	"ZY",	"ZYgprs.com"}
{404,	11,	"Vodafone",	"www"}
{404,	12,	"IDEA",	"internet"}
{404,	13,	"Vodafone",	"www"}
{404,	14,	"IDEA",	"internet"}
{404,	15,	"Vodafone",	"www"}
{404,	16,	"ZY",	"ZYgprs.com"}
{404,	17,	"AIRCEL",	"aircelwebpost"}
{404,	18,	"Reliance",	"SMARTNET"}
{404,	19,	"IDEA",	"internet"}
{404,	20,	"Vodafone",	"www"}
{404,	21,	"Loop Mobile",	"www"}
{404,	22,	"IDEA",	"internet"}
{404,	24,	"IDEA",	"internet"}
{404,	25,	"AIRCEL",	"aircelwebpost"}
{404,	27,	"Vodafone",	"www"}
{404,	28,	"AIRCEL",	"aircelwebpost"}
{404,	29,	"AIRCEL",	"aircelwebpost"}
{404,	30,	"Vodafone",	"www"}
{404,	31,	"ZY",	"ZYgprs.com"}
{404,	33,	"AIRCEL",	"aircelwebpost"}
{404,	34,	"CellOne",	"bsnlnet"}
{404,	35,	"AIRCEL",	"aircelwebpost"}
{404,	36,	"Reliance",	"SMARTNET"}
{404,	37,	"AIRCEL",	"aircelwebpost"}
{404,	38,	"CellOne",	"bsnlnet"}
{404,	40,	"ZY",	"ZYgprs.com"}
{404,	41,	"AIRCEL",	"aircelgprs.po"}
{404,	43,	"Vodafone",	"www"}
{404,	44,	"IDEA",	"internet"}
{404,	45,	"ZY",	"ZYgprs.com"}
{404,	46,	"Vodafone",	"www"}
{404,	49,	"ZY",	"ZYgprs.com"}
{404,	50,	"Reliance",	"SMARTNET"}
{404,	51,	"CellOne",	"bsnlnet"}
{404,	52,	"Reliance",	"SMARTNET"}
{404,	53,	"CellOne",	"bsnlnet"}
{404,	54,	"CellOne",	"bsnlnet"}
{404,	55,	"CellOne",	"bsnlnet"}
{404,	56,	"IDEA",	"internet"}
{404,	57,	"CellOne",	"bsnlnet"}
{404,	58,	"CellOne",	"bsnlnet"}
{404,	59,	"CellOne",	"bsnlnet"}
{404,	60,	"Vodafone",	"www"}
{404,	62,	"CellOne",	"bsnlnet"}
{404,	64,	"CellOne",	"bsnlnet"}
{404,	66,	"CellOne",	"bsnlnet"}
{404,	67,	"Reliance",	"SMARTNET"}
{404,	68,	"Dolphin",	"gprsmtnldel",	"0",	"PAP",	"mtnl",	"mtnl123"}
{404,	69,	"Dolphin",	"gprsmtnldel",	"0",	"PAP",	"mtnl",	"mtnl123"}
{404,	70,	"ZY",	"ZYgprs.com"}
{404,	71,	"CellOne",	"bsnlnet"}
{404,	72,	"CellOne",	"bsnlnet"}
{404,	73,	"CellOne",	"bsnlnet"}
{404,	74,	"CellOne",	"bsnlnet"}
{404,	75,	"CellOne",	"bsnlnet"}
{404,	76,	"CellOne",	"bsnlnet"}
{404,	77,	"CellOne",	"bsnlnet"}
{404,	78,	"IDEA",	"internet"}
{404,	79,	"CellOne",	"bsnlnet"}
{404,	80,	"CellOne",	"bsnlnet"}
{404,	81,	"CellOne",	"bsnlnet"}
{404,	82,	"IDEA",	"internet"}
{404,	83,	"Reliance",	"SMARTNET"}
{404,	84,	"Vodafone",	"www"}
{404,	85,	"Reliance",	"SMARTNET"}
{404,	86,	"Vodafone",	"www"} 
{404,	87,	"IDEA",	"internet"}
{404,	88,	"Vodafone",	"www"}
{404,	89,	"IDEA",	"internet"}
{404,	90,	"ZY",	"ZYgprs.com"}
{404,	91,	"AIRCEL",	"aircelwebpost"}
{404,	92,	"ZY",	"ZYgprs.com"}
{404,	93,	"ZY",	"ZYgprs.com"}
{404,	94,	"ZY",	"ZYgprs.com"}
{404,	95,	"ZY",	"ZYgprs.com"}
{404,	96,	"ZY",	"ZYgprs.com"}
{404,	97,	"ZY",	"ZYgprs.com"}
{404,	98,	"ZY",	"ZYgprs.com"}
{405,	01,	"Reliance",	"rcomnet"}
{405,	03,	"Reliance",	"rcomnet"}
{405,	04,	"Reliance",	"rcomnet"}
{405,	05,	"Reliance",	"rcomnet"}
{405,	06,	"Reliance",	"rcomnet"}
{405,	07,	"Reliance",	"rcomnet"}
{405,	08,	"Reliance",	"rcomnet"}
{405,	09,	"Reliance",	"rcomnet"}
{405,	10,	"Reliance",	"rcomnet"}
{405,	11,	"Reliance",	"rcomnet"}
{405,	12,	"Reliance",	"rcomnet"}
{405,	13,	"Reliance",	"rcomnet"}
{405,	14,	"Reliance",	"rcomnet"}
{405,	15,	"Reliance",	"rcomnet"}
{405,	17,	"Reliance",	"rcomnet"}
{405,	18,	"Reliance",	"rcomnet"}
{405,	19,	"Reliance",	"rcomnet"}
{405,	20,	"Reliance",	"rcomnet"}
{405,	21,	"Reliance",	"rcomnet"}
{405,	22,	"Reliance",	"rcomnet"}
{405,	23,	"Reliance",	"rcomnet"}
{405,	025,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	026,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	027,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	028,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	029,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	030,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	031,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	032,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	033,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	034,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405, 035,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	036,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	037,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	038,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	039,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	040,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	041,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	042,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	043,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	044,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	045,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	046,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	047,	"TataDOCOMO",	"TATA.DOCOMO.INTERNET"}
{405,	51,	"ZY",	"ZYgprs.com"}
{405,	52,	"ZY",	"ZYgprs.com"}
{405,	53,	"ZY",	"ZYgprs.com"}
{405,	54,	"ZY",	"ZYgprs.com"}
{405,	55,	"ZY",	"ZYgprs.com"}
{405,	56,	"ZY",	"ZYgprs.com"}
{405,	66,	"Vodafone",	"www"} 
{405,	67,	"Vodafone",	"www"} 
{405,	70,	"IDEA",	"internet"}
{405,	750,	"Vodafone",	"jkgprs"} 
{405,	751,	"Vodafone",	"www"} 
{405,	752,	"Vodafone",	"www"} 
{405,	753,	"Vodafone",	"www"} 
{405,	754,	"Vodafone",	"www"} 
{405,	755,	"Vodafone",	"www"} 
{405,	756,	"Vodafone",	"www"} 
{405,	799,	"IDEA",	"internet"}
{405,	800,	"AIRCEL",	"aircelgprs"}
{405,	801,	"AIRCEL",	"aircelgprs"}
{405,	802,	"AIRCEL",	"aircelgprs"}
{405,	803,	"AIRCEL",	"aircelgprs"}
{405,	804,	"AIRCEL",	"aircelgprs"}
{405,	805,	"AIRCEL",	"aircelgprs"}
{405,	806,	"AIRCEL",	"aircelgprs"}
{405,	807,	"AIRCEL",	"aircelgprs"}
{405,	808,	"AIRCEL",	"aircelgprs"}
{405,	809,	"AIRCEL",	"aircelgprs"}
{405,	810,	"AIRCEL",	"aircelgprs"}
{405,	811,	"AIRCEL",	"aircelgprs"}
{405,	812,	"AIRCEL",	"aircelgprs"}
{405,	813,	"Uninor",	"uninor"}
{405,	814,	"Uninor",	"uninor"}
{405,	815,	"Uninor",	"uninor"}
{405,	816,	"Uninor",	"uninor"}
{405,	817,	"Uninor",	"uninor"}
{405,	818,	"Uninor",	"uninor"}
{405,	819,	"Uninor",	"uninor"}
{405,	820,	"Uninor",	"uninor"}
{405,	822,	"Uninor",	"uninor"}
{405,	823,	"Videocon",	"vinternet.com"}
{405,	824,	"Videocon",	"vinternet.com"}
{405,	825,	"Videocon",	"vinternet.com"}
{405,	826,	"Videocon",	"vinternet.com"}
{405,	827,	"Videocon",	"vinternet.com"}
{405,	828,	"Videocon",	"vinternet.com"}
{405,	829,	"Videocon",	"vinternet.com"}
{405,	830,	"Videocon",	"vinternet.com"}
{405,	831,	"Videocon",	"vinternet.com"}
{405,	832,	"Videocon",	"vinternet.com"}
{405,	833,	"Videocon",	"vinternet.com"}
{405,	834,	"Videocon",	"vinternet.com"}
{405,	835,	"Videocon",	"vinternet.com"}
{405,	836,	"Videocon",	"vinternet.com"}
{405,	837,	"Videocon",	"vinternet.com"}
{405,	838,	"Videocon",	"vinternet.com"}
{405,	839,	"Videocon",	"vinternet.com"}
{405,	840,	"Videocon",	"vinternet.com"}
{405,	841,	"Videocon",	"vinternet.com"}
{405,	842,	"Videocon",	"vinternet.com"}
{405,	843,	"Videocon",	"vinternet.com"} 
{405,	843,	"Videocon",	"vinternet.com"}
{405,	844,	"Uninor",	"uninor"}
{405,	845,	"IDEA",	"internet"}
{405,	846,	"IDEA",	"internet"}
{405,	847,	"IDEA",	"internet"}
{405,	848,	"IDEA",	"internet"}
{405,	849,	"IDEA",	"internet"}
{405,	850,	"IDEA",	"internet"}
{405,	851,	"IDEA",	"internet"}
{405,	852,	"IDEA",	"internet"}
{405,	853,	"IDEA",	"internet"}
{405,	875,	"Uninor",	"uninor"}
{405,	876,	"Uninor",	"uninor"}
{405,	877,	"Uninor",	"uninor"}
{405,	878,	"Uninor",	"uninor"}
{405,	879,	"Uninor",	"uninor"}
{405,	880,	"Uninor",	"uninor"}
{405,	881,	"STEL",	"gprs.stel.in"}
{405,	882,	"STEL",	"gprs.stel.in"}
{405,	883,	"STEL",	"gprs.stel.in"}
{405,	884,	"STEL",	"gprs.stel.in"}
{405,	885,	"STEL",	"gprs.stel.in"}
{405,	886,	"STEL",	"gprs.stel.in"}
{405,	908,	"IDEA",	"internet"}
{405,	909,	"IDEA",	"internet"}
{405,	910,	"IDEA",	"internet"}
{405,	911,	"IDEA",	"internet"}
{405,	925,	"Uninor",	"uninor"}
{405,	926,	"Uninor",	"uninor"}
{405,	927,	"Uninor",	"uninor"}
{405,	928,	"Uninor",	"uninor"}
{405,	929,	"Uninor",	"uninor"}
{405,	932,	"Videocon",	"vinternet.com"} 
{410,	01,	"Mobilink",	"connect.mobilinkworld.com",	"0",	"PAP",	"Mobilink",	"Mobilink"} 
{410,	03,	"Ufone",	"Ufone.internet"} 
{410,	04,	"ZONG",	"zonginternet"}
{410,	06,	"Telenor",	"internet"}
{410,	07,	"Warid",	"Wap.warid"}
{415,	01,	"Alfa",	"internet.mic1.com.lb",	"0",	"PAP",	"mic1",	"mic1"}
{415,	03,	"MTC",	"gprs.mtctouch.com.lb"}
{416,	01,	"Zain JO",	"internet",	"0",	"PAP",	"zain",	"zain"}
{416,	03,	"umniah",	"internet"}
{416,	77,	"Orange",	"net.orange.jo",	"0",	"PAP",	"net",	"net"}
{419,	02,	"ZAIN",	"pps",	"0",	"PAP",	"pps",	"pps"}
{419,	03,	"WATANIYA",	"action.wataniya.com"}
{419,	04,	"VIVA",	"VIVA"}
{420,	01,	"JAWALNet",	"jawalnet.com.sa"}
{420,	03,	"mobily",	"web2"}
{420,	04,	"zain",	"zain"}
{422,	02,	"Oman",	"taif"}
{422,	02,	"Nawras",	"isp.nawras.com.om",	"0",	"PAP",	"taif",	"taif"}
{424,	02,	"DATA",	"etisalat.ae"}
{424,	03,	"Du",	"du"}
{425,	01,	"Orange",	"uwap.orange.co.il"}
{425,	02,	"Cellcom",	"Sphone"}
{425,	03,	"Sphone",	"sphone.pelephone.net.il",	"0",	"PAP",	"pcl@3g",	"pcl"}
{426,	01,	"Batelco",	"internet.batelco.com"}
{426,	02,	"Zain",	"connect.mobilinkworld.com",	"0",	"PAP",	"Mobilink",	"Mobilink"}
{426,	04,	"Viva",	"viva.bh"}
{427,	01,	"Qtel",	"gprs.qtel",	"0",	"PAP",	"gprs",	"gprs"}
{427,	02,	"Vodafone",	"web.vodafone.com.qa"}
{440,	10,	"NTT",	"spmode.ne.jp"}
{440,	10,	"NTT",	"mpr2.bizho.net"}
{440,	10,	"NTT",	"0120.mopera.net"}
{450,	05,	"SK Telecom",	"web.sktelecom.com"}
{450,	05,	"SK Telecom",	"lte.sktelecom.com",	"2"}
{450,	08,	"KT",	"lte.ktfwing.com","2"}
{450,	08,	"KT",	"alwayson.ktfwing.com"}
{450,	02,	"KT",	"lte.ktfwing.com",	"2"}
{450,	02,	"KT",	"alwayson.ktfwing.com"}
{450,	06,	"LG U+",	"internet.lguplus.co.kr"}
{452,	01,	"MOBIFONE",	"m-wap",	"0",	"PAP",	"mms",	"mms"}
{452,	02,	"Vinaphone",	"m3-world",	"0",	"PAP",	"mms",	"mms"}
{452,	04,	"Viettel",	"v-internet"}
{452,	05,	"Viettel",	"internet"}
{452,	07,	"BEELINE",	"internet"}
{454,	00,	"CSL Data",	"hkcsl"}
{454,	02,	"CSL Data",	"hkcsl"}
{454,	03,	"Hutchison",	"mobile.three.com.hk"}
{454,	04,	"Hutchison",	"web-g.three.com.hk"}
{454,	06,	"SmarTone",	"SmarTone"}
{454,	10,	"CSL",	"hkcsl"}
{454,	12,	"CMHK",	"peoples.net"}
{454,	14,	"Hutchison",	"web-g.three.com.hk"}
{454,	15,	"SmarTone",	"SmarTone"}
{454,	16,	"PCCW",	"pccwdata"}
{454,	17,	"SmarTone",	"SmarTone"}
{454,	18,	"CSL",	"hkcsl"}
{454,	19,	"PCCW",	"pccw"}
{455,	00,	"SmarTone",	"smartgprs"}
{455,	01,	"CTM",	"ctm-mobile"}
{455,	03,	"Hutchinson ",	"web-g.three.com.hk",	"0",	"PAP",	"hutchison",	"1234"}
{455,	04,	"CTM",	"ctm-mobile"}
{460,	00,	"CMCC",	"cmnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	01,	"CUCC",	"3gnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	02,	"CMCC",	"cmnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	03,	"CTCC",	"ctnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	04,	"CMCC",	"cmiot",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	04,	"CMCC",	"cmnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	05,	"CTCC",	"ctnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	06,	"CUCC",	"3gnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	07,	"CMCC",	"cmnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	08,	"CMCC",	"cmnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	09,	"CUCC",	"3gnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	11,	"CTCC",	"ctnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{460,	15,	"CBN",	"cbnet",	"0",	"NONE",	"any",	"any",	"IPV4V6"}
{466,	01,	"Far EasTone",	"internet"}
{466,	88,	"KGT",	"internet"}
{466,	89,	"VIBO",	"vibo"}
{466,	89,	"VIBOONE",	"viboone"}
{466,	92,	"CHT",	"internet"}
{466,	93,	"TWM",	"internet"}
{466,	97,	"TWM",	"internet"}
{466,	99,	"TWM",	"internet"}
{466,	99,	"TWM match",	"twm"}