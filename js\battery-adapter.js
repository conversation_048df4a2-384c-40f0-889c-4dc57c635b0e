/**
 * 电池信息适配器
 * 该脚本用于连接dashboard.js中的电池数据和电池显示组件
 */

(function() {
    // 原始的dashboard更新函数
    var originalDashboardUpdateFunction = null;
    
    // 保存最后一次获取的电池信息
    var lastBatteryInfo = {
        percentage: 0,
        isCharging: false,
        voltage: ''
    };
    
    /**
     * 从dashboard.js中捕获电池信息
     */
    function captureDashboardData() {
        // 监控DOM变化
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有电池信息相关元素变化
                checkBatteryElements();
            });
        });
        
        // 配置观察器
        var config = { 
            attributes: true, 
            childList: true, 
            subtree: true,
            characterData: true
        };
        
        // 开始观察文档
        observer.observe(document.body, config);
        
        // 每隔一段时间检查一次，以防观察器没有捕获到
        setInterval(checkBatteryElements, 3000);
    }
    
    /**
     * 检查页面中的电池元素
     */
    function checkBatteryElements() {
        try {
            // 检查可能包含电池信息的元素
            var batteryElements = document.querySelectorAll('[id*="battery"], [id*="Battery"], [class*="battery"], [class*="Battery"]');
            batteryElements.forEach(function(element) {
                var content = element.textContent || '';
                processBatteryData(content, element.id);
            });
            
            // 特别检查图片中显示的那些元素
            var batteryPercentageElement = document.querySelector('.' + jQuery.i18n.prop('lDeviceBatteryLevel'));
            if (batteryPercentageElement) {
                processBatteryData(batteryPercentageElement.textContent, jQuery.i18n.prop('lDeviceBatteryLevel'));
            }
            
            // 也检查特定ID
            if (document.getElementById('pDashBetteryInfo')) {
                processBatteryData(document.getElementById('pDashBetteryInfo').textContent, 'pDashBetteryInfo');
            }
            
            if (document.getElementById('pDashBetteryVol')) {
                var voltageText = document.getElementById('pDashBetteryVol').textContent;
                if (voltageText) {
                    lastBatteryInfo.voltage = voltageText;
                    updateBatteryDisplay();
                }
            }
        } catch (error) {
        }
    }
    
    /**
     * 处理电池数据
     */
    function processBatteryData(content, sourceId) {
        if (!content) return;
        
        // 查找百分比
        var percentMatch = content.match(/(\d+)%/);
        if (percentMatch && percentMatch[1]) {
            lastBatteryInfo.percentage = parseInt(percentMatch[1]);
        } else {
            // 尝试直接提取数字
            var numMatch = content.match(/(\d+)/);
            if (numMatch && numMatch[1]) {
                lastBatteryInfo.percentage = parseInt(numMatch[1]);
            }
        }
        
        // 检查充电状态
        lastBatteryInfo.isCharging = content.indexOf(jQuery.i18n.prop('lCharging')) > -1;
        
        // 更新显示
        updateBatteryDisplay();
    }
    
    /**
     * 更新电池显示
     */
    function updateBatteryDisplay() {
        // 使用adminApp.html中定义的setDashboardBatteryInfo函数
        if (typeof window.setDashboardBatteryInfo === 'function') {
            window.setDashboardBatteryInfo(
                lastBatteryInfo.percentage, 
                lastBatteryInfo.isCharging, 
                lastBatteryInfo.voltage
            );
        }
    }
    
    /**
     * 初始化
     */
    function init() {
        // 在页面加载完成后启动
        if (document.readyState === 'complete') {
            captureDashboardData();
        } else {
            window.addEventListener('load', function() {
                // 延迟启动，确保dashboard.js已经运行
                setTimeout(captureDashboardData, 2000);
            });
        }
        
        // 重写dashboard.js中可能包含电池信息更新的函数
        if (typeof window.updateDashboard === 'function') {
            originalDashboardUpdateFunction = window.updateDashboard;
            window.updateDashboard = function() {
                // 调用原始函数
                var result = originalDashboardUpdateFunction.apply(this, arguments);
                
                // 检查更新后的电池信息
                setTimeout(checkBatteryElements, 100);
                
                return result;
            };
        }
        
        // 特别处理路由器页面上最后一行显示的电池电量
        setInterval(function() {
            var routerDivs = document.querySelectorAll('div');
            for (var i = 0; i < routerDivs.length; i++) {
                var text = routerDivs[i].textContent || '';
                if (text.indexOf(jQuery.i18n.prop('lDeviceBatteryLevel')) > -1 || text.indexOf(jQuery.i18n.prop('lBatteryLevel')) > -1) {
                    processBatteryData(text, 'routerDiv');
                    break;
                }
            }
        }, 4000);
    }
    
    // 启动适配器
    init();
})(); 