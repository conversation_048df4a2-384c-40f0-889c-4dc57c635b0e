var timezoneStringArrayQS =  new Array(2);
var indexString;
var g_router_username="";
var g_router_password="";
var g_multi_account="";
var selectedInternetConn = "disabled";
var g_QuickSetup;
var flagIpBoxAdded = false;
var g_InternetConnectionObj = null;
var g_PrimaryNetworkObj = null;
var username1,passwd1;

(function($) {
    // changed
    $.fn.quick_setup = function(oInit) {
        this.loadHtml = function() {
            // Check if homepic exists before setting its display
            var homepic = document.getElementById("homepic");
            if (homepic) {
                homepic.style.display = "none";
            }
            
            // Check if navigation exists before setting its innerHTML
            var navigation = document.getElementById("navigation");
            if (navigation) {
                navigation.innerHTML = "<ul id ='menu' ><li ><a id='menuQuickSetup' class='on'>Quick Setup </a> </li> </ul>";
                
                // Check if menuQuickSetup exists before setting its innerHTML
                var menuQuickSetup = document.getElementById("menuQuickSetup");
                if (menuQuickSetup) {
                    menuQuickSetup.innerHTML = jQuery.i18n.prop('quickSetupName');
                }
            } else {
                //console.error("Element with ID 'navigation' not found");
                // Create navigation if it doesn't exist
                var navigationDiv = document.createElement("div");
                navigationDiv.id = "navigation";
                document.body.appendChild(navigationDiv);
                navigationDiv.innerHTML = "<ul id ='menu' ><li ><a id='menuQuickSetup' class='on'>Quick Setup </a> </li> </ul>";
                
                var menuQuickSetup = document.getElementById("menuQuickSetup");
                if (menuQuickSetup) {
                    menuQuickSetup.innerHTML = jQuery.i18n.prop('quickSetupName');
                }
            }
            
            // Check if mainColumn exists before setting its innerHTML
            var mainColumn = document.getElementById("mainColumn");
            if (mainColumn) {
                mainColumn.innerHTML = "";
                mainColumn.innerHTML = callProductHTML("html/quick_setup.html");
            } else {
                //console.error("Element with ID 'mainColumn' not found");
                // Create mainColumn if it doesn't exist
                var mainColumnDiv = document.createElement("div");
                mainColumnDiv.id = "mainColumn";
                document.body.appendChild(mainColumnDiv);
                mainColumnDiv.innerHTML = callProductHTML("html/quick_setup.html");
            }
            
            if ("dongle" == g_platformName) {
                var h1WirelessSeetings = document.getElementById("h1WirelessSeetings");
                if (h1WirelessSeetings) {
                    h1WirelessSeetings.style.display = "none";
                }
            }
            
            // Safely set innerHTML for header elements
            function safeSetElementInnerHTML(id, text) {
                var element = document.getElementById(id);
                if (element) {
                    element.innerHTML = jQuery.i18n.prop(text);
                }
            }
            
            safeSetElementInnerHTML("h1UserSettings", 'h1UserSettings');
            safeSetElementInnerHTML("h1InternetConnection", 'h1InternetConnection');
            safeSetElementInnerHTML("h1WirelessSeetings", 'h1WirelessSeetings');
            safeSetElementInnerHTML("h1DevicePlaceGuid", 'h1DevicePlaceGuid');
            
            // Setup form if it exists
            if ($('#uploadISPFileForm').length > 0) {
                $('#uploadISPFileForm').ajaxForm({
                    success: function() {
                        hm();
                    },
                    error: function() {
                        hm();
                    },
                    beforeSend: function() {
                        var fileName = document.getElementById("fileName");
                        if (fileName && fileName.value.toString().lastIndexOf(".xml") == -1) {
                            showAlert(jQuery.i18n.prop("XMLExtError"));
                            return false;
                        }
                        return true;
                    }
                });
            }
            
            // Localize labels and headings
            var h1Elements = document.getElementsByTagName("h1");
            if (h1Elements.length > 0) {
                lableLocaliztion(h1Elements);
            }
            
            var arrayLabels = document.getElementsByTagName("label");
            if (arrayLabels.length > 0) {
                lableLocaliztion(arrayLabels);
            }
            
            // Make sure MBQuickSetupMainPage exists before showing it
            var mainPage = document.getElementById("MBQuickSetupMainPage");
            if (mainPage) {
                showDiv("MBQuickSetupMainPage", 400, 220);
                
                // Make sure h1UserSettings exists before adding class
                var h1UserSettings = document.getElementById("h1UserSettings");
                if (h1UserSettings) {
                    addClassQSon("h1UserSettings");
                }
                
                MBQuickSetupMainPageLoadData();
            } else {
                //console.error("Element with ID 'MBQuickSetupMainPage' not found");
            }
        }
        this.onPostSuccess = function() {
            username = username1;
            passwd = passwd1; 
            hideDiv();
        }
        return this.each(function() {
        });
    }
})(jQuery);

function quickSetup() {
    // Check if mobileMenubtn exists before trying to hide it
    var mobileMenubtn = document.getElementById("mobileMenubtn");
    if (mobileMenubtn) {
        mobileMenubtn.style.display = 'none';
    }
    
    // Check if lableWelcome exists before setting its innerHTML
    var lableWelcome = document.getElementById("lableWelcome");
    if (lableWelcome) {
        lableWelcome.innerHTML = jQuery.i18n.prop("lableWelcome");
    }
    
    // Check if quickSetupSpan exists before setting its innerHTML
    var quickSetupSpan = document.getElementById("quickSetupSpan");
    if (quickSetupSpan) {
        quickSetupSpan.innerHTML = "<a href='#.' id='quickSetupspanlink' onclick=getHelp('QuickSetup')>Help</a>";
        
        // Check if quickSetupspanlink exists before setting its innerHTML
        var quickSetupspanlink = document.getElementById("quickSetupspanlink");
        if (quickSetupspanlink) {
            quickSetupspanlink.innerHTML = jQuery.i18n.prop("helpName");
        }
    }
    
    // Initialize g_QuickSetup if mainColumn exists
    var mainColumn = document.getElementById("mainColumn");
    if (mainColumn) {
        g_QuickSetup = $("#mainColumn").quick_setup();
        clearRefreshTimers();
        g_QuickSetup.loadHtml();
    } else {
        //console.error("Element with ID 'mainColumn' not found");
        // Create mainColumn if it doesn't exist
        var mainColumnDiv = document.createElement("div");
        mainColumnDiv.id = "mainColumn";
        mainColumnDiv.className = "mainColumn";
        document.body.appendChild(mainColumnDiv);
        g_QuickSetup = $("#mainColumn").quick_setup();
        clearRefreshTimers();
        g_QuickSetup.loadHtml();
    }
    
    // Check if platform-specific elements exist before setting their properties
    if ("dongle" == g_platformName) {
        var btnNext1 = document.getElementById("btnNext1");
        var btnBack4 = document.getElementById("btnBack4");
        if (btnNext1) {
            btnNext1.onclick = btnQSNextClicked1;
        }
        if (btnBack4) {
            btnBack4.onclick = btnBackClicked2;
        }
    }
    
    // Check if btnExit exists before setting its innerHTML
    var btnExit = document.getElementById("btnExit");
    if (btnExit) {
        btnExit.innerHTML = jQuery.i18n.prop("btnExit");
    }
    
    // Check if btnNext exists before localizing it
    var btnNext = document.getElementById("btnNext");
    if (btnNext) {
        buttonLocaliztion(btnNext.id);
    }
}

function btnQSNextClicked() {
    username1 =  encodeURIComponent(document.getElementById("tbrouter_username").value);
    passwd1 =   encodeURIComponent(document.getElementById("tbrouter_password").value);
    //Post MBQuickSetupMainPage data [Admin, locale]

    if(validatePasswordQS()) {
        if(isValidAdminPage()) {

            SetUserNameAndPasswd();

            document.getElementById("MBQuickSetupMainPage").style.display = "none";
            document.getElementById("btnExit1").innerHTML = jQuery.i18n.prop("btnExit1");
            buttonLocaliztion(document.getElementById("btnBack1").id);
            buttonLocaliztion(document.getElementById("btnNext1").id);
            $("#h1UserSettings").removeClass("on");
            SetInternetConnection();

        }
    }
}


function SetUserNameAndPasswd() {
    var mapData = new Array();
    var index = 0;
    if(g_multi_account == "1") {
        if (passwd1 != g_router_password) {
            mapData = putMapElement(mapData,"RGW/management/account_management/account_action", 1, index++);//edit or add
            mapData = putMapElement(mapData,"RGW/management/account_management/account_username", username1, index++);
            mapData = putMapElement(mapData,"RGW/management/account_management/account_password", passwd1, index++);
            mapData = putMapElement(mapData,"RGW/management/router_user_list/Item#index", index, index++);
            mapData = putMapElement(mapData,"RGW/management/router_user_list/Item/username", username1, index++);
            mapData = putMapElement(mapData,"RGW/management/router_user_list/Item/password", passwd1, index++);
            mapData = putMapElement(mapData,"RGW/management/router_user_list/Item/authority", 1, index++);
        }
    } else {
        if (username1 != g_router_username)
            mapData = putMapElement(mapData, "RGW/management/router_username", username1, index++);
        if (passwd1 != g_router_password)
            mapData = putMapElement(mapData, "RGW/management/router_password", passwd1, index++);
    }

    if(mapData.length > 0) {
        PostSyncXMLEx("admin", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)),function(){username = username1; passwd = passwd1; });
    }

}
function btnExitClicked(id) {
    // document.getElementById("mobileMenubtn").style.display='block'   
    if (window.matchMedia("(max-width: 600px)").matches) {
        document.getElementById("mobileMenubtn").style.display = 'block';
    }else{
        document.getElementById("mobileMenubtn").style.display = 'none';
    }
    hideDiv();
    
}

function btnFinishClicked() {
    // document.getElementById("mobileMenubtn").style.display='block'   
    if (window.matchMedia("(max-width: 600px)").matches) {
        document.getElementById("mobileMenubtn").style.display = 'block';
    }else{
        document.getElementById("mobileMenubtn").style.display = 'none';
    }
    hideDiv();
}
function btnQSNextClicked1() {   

     if (document.getElementById("micdropdown").value == 'cellular' && !g_InternetConnectionObj.MtuValid()) {
     	$("#lMtuInvalidTip").show();
        return;
     }
				
    if ("dongle" == g_platformName) {
        document.getElementById("MBQuickSetupPage1").style.display = "none";
        showDiv("MBQuickSetupPage3", 400, 220);
        buttonLocaliztion(document.getElementById("btnBack4").id);
        buttonLocaliztion(document.getElementById("btnFinish").id);
        MBQuickSetupPage3Localization();
        addClassQSon("h1DevicePlaceGuid");
    } else {
        document.getElementById("MBQuickSetupPage1").style.display = "none";
        $("#h1InternetConnection").removeClass("on");
        SetPrimaryNetwork();
    }

	g_InternetConnectionObj.onPost(false);

}
function SetPrimaryNetwork() {

    document.getElementById("MBQuickSetupPage2").style.display = "block";
    document.getElementById("divPrimaryNetworkSet").innerHTML = callProductHTML("html/wireless/primary_network.html");

    g_objContent = $("#MBQuickSetupPage2").objWire_Sec();

    g_objContent.setXMLName("uapxb_wlan_security_settings");
    g_objContent.onLoad(false);
    g_PrimaryNetworkObj = g_objContent;


    addClassQSon("h1WirelessSeetings");
    $("#primaryNetworkHelp").hide();
    $("#divSavePrimaryNetworkData").hide();
    $("#lPassErrorMesPN").hide();

    document.getElementById("btnExit3").innerHTML = jQuery.i18n.prop("btnExit3");
    buttonLocaliztion(document.getElementById("btnBack3").id);
    buttonLocaliztion(document.getElementById("btnNext3").id);
}

function btnQSNextClicked2() {   
	g_PrimaryNetworkObj.onPost(true);
    if (g_objContent.isValid(false)) {
        if ("dongle" == g_platformName) {
            document.getElementById("MBQuickSetupPage1").style.display = "none";
        } else {
            document.getElementById("MBQuickSetupPage2").style.display = "none";
        }
        showDiv("MBQuickSetupPage3",400,220);

        buttonLocaliztion(document.getElementById("btnBack4").id);
        buttonLocaliztion(document.getElementById("btnFinish").id);

        MBQuickSetupPage3Localization();
        addClassQSon("h1DevicePlaceGuid");
    }

	 

}
function MBQuickSetupPage3Localization() {
    // Helper function to safely set innerHTML of an element
    function safeSetInnerHTML(id, text) {
        var element = getID(id);
        if (element) {
            element.innerHTML = jQuery.i18n.prop(text);
        }
    }
    
    // Safely set innerHTML for each element
    safeSetInnerHTML("Microwave", 'Microwave');
    safeSetInnerHTML("Bluetooth_Devices", 'Bluetooth_Devices');
    safeSetInnerHTML("Cordless_Phone", 'Cordless_Phone');
    safeSetInnerHTML("ownDevices", 'ownDevices');
    safeSetInnerHTML("Baby_Monitor", 'Baby_Monitor');
}
function btnBackClicked1() {
    document.getElementById("MBQuickSetupPage1").style.display = "none";
    showDiv("MBQuickSetupMainPage",400,220);
    document.getElementById("btnExit").innerHTML = jQuery.i18n.prop("btnExit");
    buttonLocaliztion(document.getElementById("btnNext").id);
    g_objContent = g_InternetConnectionObj;
    addClassQSon("h1UserSettings");
    MBQuickSetupMainPageLoadData();
}
function btnBackClicked2() {
    if ("dongle" == g_platformName) {
        document.getElementById("MBQuickSetupPage3").style.display = "none";
    } else {
        document.getElementById("MBQuickSetupPage2").style.display = "none";
    }
    SetInternetConnection();
}

function SetInternetConnection() {
    document.getElementById("MBQuickSetupPage1").style.display = "block";
    document.getElementById("divInternetConnectSet").innerHTML = callProductHTML("html/internet/internet_connection.html");
    $("#inter_help").hide();
    $("#divFormBox").hide();
    $("#divSaveButton").hide();
	$("#title").hide();
	
    $("#h1WirelessSeetings").removeClass("on");
    document.getElementById("btnExit1").innerHTML = jQuery.i18n.prop("btnExit1");
    buttonLocaliztion(document.getElementById("btnBack1").id);
    buttonLocaliztion(document.getElementById("btnNext1").id);
    g_objContent = null;
    g_objContent =  $("#MBQuickSetupPage1").objInternetConn();
    g_objContent.setXMLName("wan");
    g_objContent.onLoad(false);
    g_InternetConnectionObj = g_objContent;
    addClassQSon("h1InternetConnection");

}
function btnBackClicked3() {
    document.getElementById("MBQuickSetupPage3").style.display = "none";
    $("#h1DevicePlaceGuid").removeClass("on");

    if("mifi" == g_platformName) {
        SetPrimaryNetwork();
    } else {
        SetInternetConnection();
    }
}

function showDiv(divid,width,height) {
    removeClassQS();
    document.getElementById(divid).style.display = "block";
    g_InternetConnectionObj = null;
    g_PrimaryNetworkObj = null;

}
function hideDiv() {
    // Safely set innerHTML for elements
    function safeSetElementInnerHTML(id, text) {
        var element = document.getElementById(id);
        if (element) {
            element.innerHTML = text;
        }
    }
    
    safeSetElementInnerHTML("lableWelcome", jQuery.i18n.prop("lableWelcome"));
    safeSetElementInnerHTML("quickSetupSpan", '<a href="#."  onclick="quickSetup()" id="quickSetup" >Quick Setup</a>  |  <a href="#." id="HelpName" onclick="getMainHelp()">Help</a>  |  <a href="#."  id="LogOutName"  onclick="logOut()">Log Out</a>');
    
    // Update text for newly created elements
    safeSetElementInnerHTML("quickSetup", jQuery.i18n.prop("quickSetupName"));
    safeSetElementInnerHTML("HelpName", jQuery.i18n.prop("helpName"));
    safeSetElementInnerHTML("LogOutName", jQuery.i18n.prop("LogOutName"));

    // Check if navigation exists before setting its innerHTML
    var navigation = document.getElementById("navigation");
    if (navigation) {
        navigation.innerHTML = " <ul id ='menu'></ul>";
    }
    
    // Reset global objects
    g_InternetConnectionObj = null;
    g_PrimaryNetworkObj = null;
    
    // Call menu creation functions if they exist
    if (typeof createMenuFromXML === 'function') {
        createMenuFromXML();
    }
    
    if (typeof createMenu === 'function') {
        createMenu(1);
    }
}
function MBQuickSetupMainPageLoadData() {
    var xmlAdmin = getData("admin");
    var xmlLocale = getData("locale");
    // var xmlTimeZone = getTimeZoneData("tzdatabase.xml");
    var index=0;
    var router_username_;
    var router_password_;
    var authority;
    var _arrayTableDataAccount = new Array(0);
    var indexAccount = 0;
    var login_account_index;

    timezoneStringArrayQS[0] = new Array();
    timezoneStringArrayQS[1] = new Array();
    
    if (xmlAdmin) {
        $(xmlAdmin).find("management").each(function() {
            g_multi_account = $(this).find("multi_account").text();
        });
        
        if(g_multi_account == "1") {
            $(xmlAdmin).find("router_user_list").each(function() {
                $(this).find("Item").each(function() {
                    router_username_ = decodeURIComponent($(this).find("username").text());
                    router_password_ = decodeURIComponent($(this).find("password").text());
                    authority = $(this).find("authority").text();
                    if(router_username_ == username) {
                        login_account_index = indexAccount;
                    }
                    _arrayTableDataAccount[indexAccount] = new Array(3);
                    _arrayTableDataAccount[indexAccount][0] = router_username_;
                    _arrayTableDataAccount[indexAccount][1] = router_password_;
                    _arrayTableDataAccount[indexAccount][2] = authority;
                    indexAccount++;
                });
            });
            
            if (typeof login_account_index !== 'undefined' && _arrayTableDataAccount.length > 0) {
                g_router_username = _arrayTableDataAccount[login_account_index][0];
                g_router_password = _arrayTableDataAccount[login_account_index][1];
                
                // Safely set values for form elements
                var usernameField = document.getElementById("tbrouter_username");
                var passwordField = document.getElementById("tbrouter_password");
                var reenterPasswordField = document.getElementById("tbreenter_password");
                
                if (usernameField) {
                    usernameField.value = g_router_username;
                    usernameField.readOnly = true;
                }
                
                if (passwordField) {
                    passwordField.value = g_router_password;
                }
                
                if (reenterPasswordField) {
                    reenterPasswordField.value = g_router_password;
                }
            }
        } else {
            $(xmlAdmin).find("management").each(function() {
                g_router_username = decodeURIComponent($(this).find("router_username").text());
                g_router_password = decodeURIComponent($(this).find("router_password").text());
                
                // Safely set values for form elements
                var usernameField = document.getElementById("tbrouter_username");
                var passwordField = document.getElementById("tbrouter_password");
                var reenterPasswordField = document.getElementById("tbreenter_password");
                
                if (usernameField) {
                    usernameField.value = g_router_username;
                    usernameField.readOnly = false;
                }
                
                if (passwordField) {
                    passwordField.value = g_router_password;
                }
                
                if (reenterPasswordField) {
                    reenterPasswordField.value = g_router_password;
                }
            });
        }
    }
}
function rbTimeZoneCickedQS() {
    if(document.getElementById("getConnDevTimeZone").checked==true)
        setMachineTimezoneQS();
}

function setMachineTimezoneQS() {
    var i = GetMachineTimezoneGmtOffset();
    var gmtOffset = GetMachineTimezoneGmtOffsetStr(i);
    var dstStr = GetMachineTimezoneDstStartStr(i);
    indexString =  setConnectedDeviceTimezoneStr(gmtOffset,dstStr,timezoneStringArrayQS);
    if(indexString!=-1)
        document.getElementById("lConnectedDeviceTimeZone").innerHTML=timezoneStringArrayQS[0][indexString];
    else
        document.getElementById("lConnectedDeviceTimeZone").innerHTML=jQuery.i18n.prop("ErrTimeZoneNotFound");
}
function validatePasswordQS() {
    var passwordField = document.getElementById('tbrouter_password');
    var reenterPasswordField = document.getElementById('tbreenter_password');
    var errorMessageField = document.getElementById('lPassErrorMes');
    
    if (!passwordField || !reenterPasswordField) {
        //console.error("Password fields not found");
        return false;
    }
    
    if (passwordField.value != reenterPasswordField.value) {
        if (errorMessageField) {
            errorMessageField.style.display = 'block';
            errorMessageField.innerHTML = jQuery.i18n.prop('lPassErrorMes');
        }
        
        if (reenterPasswordField) {
            reenterPasswordField.value = '';
        }
        
        return false;
    } else {
        if (errorMessageField) {
            errorMessageField.style.display = 'none';
        }
        
        return true;
    }
}

function removeClassQS() {
    document.getElementById("h1UserSettings").className="";
    document.getElementById("h1InternetConnection").className="";
    document.getElementById("h1WirelessSeetings").className="";
    document.getElementById("h1DevicePlaceGuid").className="";

}
function addClassQSon(id) {
    document.getElementById(id).className="on";
}
