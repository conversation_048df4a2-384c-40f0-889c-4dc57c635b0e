(function ($) {
    $.fn.objAcsManage = function (InIt) {
	var xmlName = '';
	var controlMapExisting=new Array(0);
	var controlMapCurrent=new Array(0);
	var g_bInformEnabled;
	var _enable;
	var _url;
	var _userName;
	var _password;
	var _connName;
	var _connPsw ;
	var _informInterval;
	var _informEnable;
	var _secretMode;
	var oldMap=new Array(0);
	var newMap=new Array(0);
	var index = 0;
	
	this.onLoad = function () {

		this.loadHTML();
		document.getElementById("acstitle").innerHTML = jQuery.i18n.prop(InIt);
		
		// 本地化所有静态文本
		this.localizeStaticTexts();
		
		var arrayLabels = document.getElementsByTagName("label");
		lableLocaliztion(arrayLabels);
		var xml = getData("acs");
		$(xml).find("tr069").each(function() {
			_enable = $(this).find("enable").text();
			_url = $(this).find("acs_url").text();
			_userName = $(this).find("acs_username").text();
			_password = $(this).find("acs_password").text();
			_connName = $(this).find("conn_name").text();
			_connPsw = $(this).find("conn_psw").text();
			_informInterval = $(this).find("inform_interval").text();
			_informEnable = $(this).find("inform_enable").text();
			_secretMode = $(this).find("acs_secretmode").text();

		});
		$('#sAcsEnable').val(_enable);
		
		if (_enable == "1") {
			$('#Acs_Management_div').show();
			document.getElementById("tr069AcsUrl").value = _url; 
			document.getElementById("tr069AcsUsername").value = _userName;
			document.getElementById("tr069AcsPassword").value = _password;
			document.getElementById("tr069SecretMode").value = _secretMode;
			
			document.getElementById("tr069ConnName").value = _connName;
			document.getElementById("tr069ConnPassword").value = _connPsw;
			document.getElementById("tr069InformInterval").value = _informInterval;
			document.getElementById("lTr069InformEnable").value = _informEnable;
			document.getElementById("tbacsreenter_password").value = _password;
			var buttonID = document.getElementById("btUpdate").id;
	              buttonLocaliztion(buttonID); 
	              
	              // 重新本地化静态文本
	              this.localizeStaticTexts();

			index = 0;
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/enable", _enable);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_url", _url);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_username", _userName);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_password", _password);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/conn_name", _connName);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/conn_psw", _connPsw);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/inform_interval", _informInterval);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/inform_enable", _informEnable);
			controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_secretmode", _secretMode);
			controlMapCurrent = g_objXML.copyArray(controlMapExisting,controlMapCurrent);
			oldMap = g_objXML.copyArray(controlMapExisting,oldMap);	
		} else {
			$('#Acs_Management_div').hide();
		}

		$('#sAcsEnable').change(function(){
			if ("1" == $(this).val()) {
				$('#Acs_Management_div').show();
				document.getElementById("tr069AcsUrl").value = _url; 
				document.getElementById("tr069AcsUsername").value = _userName;
				document.getElementById("tr069AcsPassword").value = _password;
				document.getElementById("tr069SecretMode").value = _secretMode;
						
				document.getElementById("tr069ConnName").value = _connName;
				document.getElementById("tr069ConnPassword").value = _connPsw;
				document.getElementById("tr069InformInterval").value = _informInterval;
				document.getElementById("lTr069InformEnable").value = _informEnable;
				document.getElementById("Acs_Management_div").style.display = "block";
				document.getElementById("tbacsreenter_password").value = _password;
				var buttonID = document.getElementById("btUpdate").id;
		              buttonLocaliztion(buttonID); 
		              
		              // 重新本地化静态文本
		              var self = this;
		              setTimeout(function() { self.localizeStaticTexts(); }, 100);

				index = 0;
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/enable", _enable);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_url", _url);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_username", _userName);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_password", _password);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/conn_name", _connName);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/conn_psw", _connPsw);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/inform_interval", _informInterval);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/inform_enable", _informEnable);
				controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/tr069/acs_secretmode", _secretMode);
				controlMapCurrent = g_objXML.copyArray(controlMapExisting,controlMapCurrent);
				oldMap = g_objXML.copyArray(controlMapExisting,oldMap);	
			} else {
				$('#Acs_Management_div').hide();
			}
		});
	}
	
	this.getPostData = function(){
		var index = 0;
		var mapData = new Array(0);
		controlMapCurrent[index++][1] = document.getElementById("sAcsEnable").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069AcsUrl").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069AcsUsername").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069AcsPassword").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069ConnName").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069ConnPassword").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069InformInterval").value;
		controlMapCurrent[index++][1] = document.getElementById("lTr069InformEnable").value;
		controlMapCurrent[index++][1] = document.getElementById("tr069SecretMode").value;

		if(controlMapCurrent[3][1] != controlMapExisting[3][1]){
	                   document.getElementById("tr069SecretMode").value = 0;
		}
		mapData = g_objXML.copyArray(controlMapCurrent,mapData);
		newMap = mapData;
		return newMap;
	}
	
       this.putMapElement = function(xpath, value, index) {
            mapData[index] = new Array(2);
            mapData[index][0] = xpath;
            mapData[index][1] = value;
        }

	this.acsSave =function() {

		if(oldMap.sort().toString() != newMap.sort().toString()) {
			
			/**Need reboot after set ACS?**/
			/*
			if(confirm("Are you sure you want to Reboot the Router?")) {
				setData();
				hm();
				callProductXML("reset");
				hm();
				sm('rebootRouterModalBox',319,170);
				document.getElementById("h1RebootRouter").innerHTML = jQuery.i18n.prop("h1RebootRouter");
				document.getElementById("lRebootedRouter").innerHTML = jQuery.i18n.prop("lRebootedRouter");
				afterRebootID =  setInterval("afterReboot()", 45000);
			}
			*/
			setData();
		}
	}


       this.afterReboot = function () {
                hm();
                clearInterval(afterRebootID);
                clearAuthheader();
       }
			
	this.onPost = function(){

		if(this.isValid()) {
			var _controlMap ;
			_controlMap = this.getPostData();
			if(_controlMap.length>0) {
				postXML("acs", g_objXML.getXMLDocToString(g_objXML.createXML(_controlMap)));
			}

		}
	}
	
	this. isValid = function(){
		if (!Password_Validation($("#tr069AcsUsername").val())||!Password_Validation($("#tr069AcsPassword").val())||!Password_Validation($("#tr069ConnName").val())||!Password_Validation($("#tr069ConnPassword").val())) 
		{
	            document.getElementById('lPassErrorMesPN').style.display = 'block';
	            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('ErrInvalidUserPwd_ACS');
	            return false;
	        }
		else if (isChineseChar($("#tr069AcsUsername").val())||isChineseChar($("#tr069AcsPassword").val())||isChineseChar($("#tr069ConnName").val())||isChineseChar($("#tr069ConnPassword").val())) 
		{
	            document.getElementById('lPassErrorMesPN').style.display = 'block';
	            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lChineseCharError_ACS');
	            return false;
	        }
		else if(isChineseChar($("#tr069AcsUrl").val()))
		{            
			document.getElementById('lPassErrorMesPN').style.display = 'block';
			document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lChineseCharError_ACS_URL_zh');
			return false;
		}
		else if(!deviceNameValidation_ACS_URL($("#tr069AcsUrl").val()))
		{            
			document.getElementById('lPassErrorMesPN').style.display = 'block';
			document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lChineseCharError_ACS_URL_en');
			return false;
		}
		else if(!isNumber($("#tr069InformInterval").val()))
		{
			document.getElementById('lPassErrorMesPN').style.display = 'block';
			document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lNumberCharError_ACS');
			return false;

		}
		else
		     return true;
	}
	
	this.onPostSuccess = function() {
	    this.onLoad();
	}
	 
	this.setXMLName = function (_xmlname) {
		xmlName = _xmlname;
	}

	this.loadHTML = function() {
		document.getElementById('Content').innerHTML = "";
		document.getElementById('Content').innerHTML = callProductHTML("html/router/acs_management.html");
	}
	
	// 本地化静态文本的函数
	this.localizeStaticTexts = function() {
		try {
			// 本地化下拉选项
			var elements = {
				// ACS Enable/Disable options
				'oAcsDisable': 'lAcsDisabled',
				'oAcsEnable': 'lAcsEnabled',
				
				// Periodic Inform options
				'lInformEnabled': 'lInformEnabled',
				'lInformDisabled': 'lInformDisabled',
				
				// Labels
				'lAcsEnable': 'lAcsEnable',
				'lTr069AcsUrl': 'lTr069AcsUrl',
				'lTr069AcsUsername': 'lTr069AcsUsername',
				'lTr069AcsPassword': 'lTr069AcsPassword',
				'lReAcsPassword': 'lReAcsPassword',
				'lTr069Inform': 'lTr069Inform',
				'lTr069InformInterval': 'lTr069InformInterval',
				'lTr069ConnName': 'lTr069ConnName',
				'lTr069ConnPassword': 'lTr069ConnPassword',
				
				// Error messages
				'lPassErrorMes': 'lPassErrorMes',
				'lPassErrorMesPN': 'lPassErrorMesPN',
				
				// Button
				'btUpdate': 'btUpdate',
				
				// Alert dialog
				'lAlert': 'lAlert',
				'lAlertMessage': 'lAlertMessage',
				'btnModalOk': 'btnModalOk'
			};
			
			// 遍历并设置文本
			for (var elementId in elements) {
				var element = document.getElementById(elementId);
				if (element) {
					var translatedText = jQuery.i18n.prop(elements[elementId]);
					if (translatedText && translatedText !== elements[elementId]) {
						if (element.tagName === 'INPUT' && (element.type === 'button' || element.type === 'submit')) {
							element.value = translatedText;
						} else {
							element.innerHTML = translatedText;
						}
					}
				}
			}
			
			// 特殊处理：错误消息的默认文本
			var passErrorElement = document.getElementById('lPassErrorMes');
			if (passErrorElement && !passErrorElement.innerHTML.trim()) {
				passErrorElement.innerHTML = jQuery.i18n.prop('lPassErrorMes') || 'Password do not match';
			}
			
		} catch (e) {
			//console.error('ACS管理页面本地化失败:', e);
		}
	}
	return this.each(function () {

	});
}
})(jQuery);

function InformStatusChanged() {
    var linkObj = document.getElementById("lTr069InformEnable");
    var value = linkObj.options[linkObj.selectedIndex].value;
}

function setDataReboot() {	
        g_objContent.getPostData();
	if(document.getElementById('tr069AcsPassword').value!=document.getElementById('tbacsreenter_password').value) {
	    document.getElementById('lPassErrorMes').style.display = 'block';
	    document.getElementById('lPassErrorMes').innerHTML=jQuery.i18n.prop('lPassErrorMes');
	    document.getElementById("tbacsreenter_password").value = '';
	} else {
	    document.getElementById('lPassErrorMes').style.display = 'none';
	    g_objContent.acsSave();
	}		
}

function pswChanged() {
    document.getElementById("tbacsreenter_password").value = '';
    document.getElementById('lReAcsPassword').style.display = 'block';
    document.getElementById('tbacsreenter_password').style.display = 'block';

}
  
