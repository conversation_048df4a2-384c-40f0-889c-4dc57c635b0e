var xml;
var wan_conn_status;
var lastSelPdpName = "";
var g_bSimCardExist = false;
var g_masterSim = "0"; // Global variable for at_sim
var g_bNetworkConnected = false;
var lastPrintSysInfoTime = 0;
var hasCalledPrintSysInfo = false; // 全局变量，记录是否已调用过
var isPrintSysInfoRunning = false; // 添加全局变量跟踪执行状态
var cachedXmlData = null; // 缓存的XML数据
var isFirstLoad = true; // 标记是否首次加载
var lastUpdateTime = 0; // 上次更新时间
var UPDATE_INTERVAL = 5000; // 最小更新间隔5秒
(function($) {
    $.fn.objdashboard = function(oInit) {
        var c_xmlName = '';
        this.onLoad = function(flag) {
            var connDeviceValue;
            var wlan_enable;
            var defaultGateway;
            var conn_type;
            var proto_type="";
            var wan_link_status;
            var rssis;
            var roaming;
            var sim_status;
            var pin_status;
            var strURL;
            var regURL;
            var lanip;
            var wssid;
            var enc;
            var cipher;
            var sys_mode;
            var Battery_charging;
            var Battery_charge;
            var Battery_voltage;
            var Battery_charge_string;
            var Battery_voltage_percent;
            var Battery_connect;
            var data_conn_mode;
            var data_conn_mode_string;
            var channel;
            var pdpruleTxt = '';
            var pdpruleName = '';
            var NewSMSArrivedNum = 0;
            var IsLWGFlag = 0;
            var ISPName = '';
            var roamingNetworkName;
            var auto_apn;
            var connect_disconnect="";
            var ICCID='';
            var IMEI='';
            var now = Date.now();
            // 首次加载或缓存过期时才主动获取数据
            if (isFirstLoad || !cachedXmlData || (now - lastUpdateTime > UPDATE_INTERVAL)) {
                try {
                    xml = getData(c_xmlName);
                    if (xml) {
                        cachedXmlData = xml; // 缓存数据
                        lastUpdateTime = now;
                        isFirstLoad = false;
                    } else {
                        xml = cachedXmlData; // 获取失败时使用缓存
                    }
                } catch (e) {
                    xml = cachedXmlData; // 出错时使用缓存
                }
            } else {
                xml = cachedXmlData; // 使用缓存数据
            }
			// getData("Engineer_parameter");
            if (flag) {
                this.loadHTML();


                if ("dongle" == g_platformName) {
                    document.getElementById("wlan_settings").style.display = "none";
                    document.getElementById("wlan_enble").style.display = "none";

                }
                this.Localize();
            }


            $(xml).find("wan").each(function() {
                ICCID = $(this).find("ICCID").text();
                setLabelValue("ICCIDdiv", ICCID);
                SN = $(this).find("SN").text();
                var at_sim = $(this).find("at_sim").text();
                g_masterSim = at_sim || "0";  // Store in global variable
                setLabelValue("SNdiv", SN);
                $("#pdpRuleNameDropdown").empty();
                IsLWGFlag = $(this).find("LWG_flag").text();
                var bConnectStatus = false;
                var bLastSelPdpExist = false;

                $(this).find("pdp_context_list").each(function() {
                    $(this).find("Item").each(function() {
                        if ("1" == $(this).find("success").text())
                            bConnectStatus = true;

                        pdpruleTxt = $(this).find("rulename").text();
                        pdpruleName = "selectValue" + $(this).find("rulename").text();
                        
                        var pdpDropdown = document.getElementById("pdpRuleNameDropdown");
                        if (pdpDropdown) {
                        var opt = document.createElement("option");
                            pdpDropdown.options.add(opt);
                        opt.text = pdpruleTxt;
                        opt.value = pdpruleName;
                        } else {
                        }

                        if (lastSelPdpName == pdpruleName)
                            bLastSelPdpExist = true;
                    })
                })

                var pdpDropdownSetValue = document.getElementById("pdpRuleNameDropdown");
                if (pdpDropdownSetValue) {
                if (!bLastSelPdpExist)
                        pdpDropdownSetValue.value = pdpruleName;
                    else
                        pdpDropdownSetValue.value = lastSelPdpName;
                } else {
                }
                setDetailPDPInfo();

                wan_link_status = $(this).find("wan_link_status").text();
                wan_conn_status = $(this).find("wan_conn_status").text();
                var rsrp = $(this).find("rssi").text();
                // 将RSRP转换为RSSI
                rssis = rsrp-140;
                
                // RSSI信号分级函数
                function getRSSIGrade(rssi) {
                    var rssiValue = parseFloat(rssi);
                    if (rssiValue > -85) return {grade: '极好', percent: '80%+'};
                    else if (rssiValue > -95) return {grade: '好', percent: '60%-80%'};
                    else if (rssiValue > -105) return {grade: '中', percent: '40%-60%'};
                    else if (rssiValue > -115) return {grade: '差', percent: '20%-40%'};
                    else return {grade: '极差', percent: '<20%'};
                }
                
                var rssiGrade = getRSSIGrade(rssis);
                sim_status = $(this).find("sim_status").text();
                if(0 == sim_status) {
                    g_bSimCardExist = true;
                } else {
                    g_bSimCardExist = false;
                }
                auto_apn = $(this).find("auto_apn").text();
                pin_status = $(this).find("pin_status").text();
                roaming = $(this).find("roaming").text();
                wssid = UniDecode($(this).find("ssid").text());
                enc = $(this).find("enc").text();
                cipher = $(this).find("cipher").text();
                sys_mode = $(this).find("sys_mode").text();
                Battery_charging = $(this).find("Battery_charging").text();
                Battery_charge = $(this).find("Battery_charge").text();
                Battery_voltage = $(this).find("Battery_voltage").text();
                Battery_connect = $(this).find("Battery_connect").text();
                data_conn_mode = $(this).find("sys_submode").text();
                
                window.g_Battery_charging = Battery_charging;
                window.g_Battery_charge = Battery_charge;
                window.g_Battery_voltage = Battery_voltage;
                window.g_Battery_connect = Battery_connect;
                ISPName = $(this).find("network_name").text();
                roamingNetworkName = $(this).find("roaming_network_name").text();
                IMEI=$(this).find("IMEI").text();
                
                setLabelValue("IMEIdiv", IMEI);
                
                var networkTypeText = "";
                if (data_conn_mode == '17' || sys_mode == 17) {
                    networkTypeText = "LTE";
                } else if (sys_mode == 3) {
                    networkTypeText = "GSM";
                } else if (sys_mode == 5) {
                    networkTypeText = "WCDMA";
                } else if (sys_mode == 15) {
                    networkTypeText = "TD-SCDMA";
                } else if (data_conn_mode == '3') {
                    networkTypeText = 'EDGE';
                } else if (data_conn_mode == '2') {
                    networkTypeText = 'GPRS';
                } else if (data_conn_mode == '5') {
                    networkTypeText = 'HSDPA';
                } else if (data_conn_mode == '6') {
                    networkTypeText = 'HSUPA';
                } else if (data_conn_mode == '7') {
                    networkTypeText = 'HSDPA+HSUPA';
                } else {
                    networkTypeText = jQuery.i18n.prop("lNoService") || "No Service";
                }

                var curconntime = $(this).find("curconntime").text();
                var deviceStatus = {
                    signal:  rssiGrade.grade,
                    signalWithType: rssiGrade.percent + ' ' + rssiGrade.grade + ' ' + networkTypeText,
                    ipv4: $(this).find("ipv4").text() || "unknown",
                    ipv6: $(this).find("ipv6").text() || "unknown",
                    dns: $(this).find("dns1").text() + ($(this).find("dns2").text() ? "," + $(this).find("dns2").text() : ""),
                    connTime: curconntime ? dateFormat(curconntime) : "未连接"
                };

                setLabelValue("SignalStrength", deviceStatus.signal);
                setLabelValue("lSignalInfoValue", deviceStatus.signalWithType);
                setLabelValue("IPv4div", deviceStatus.ipv4);
                setLabelValue("lIPv6Value", deviceStatus.ipv6);
                setLabelValue("lDNSValue", deviceStatus.dns);
                setLabelValue("lConnTimeValue", deviceStatus.connTime);

                if (wan_conn_status.indexOf("ppp", 0) != -1) {
                    wan_conn_status = wan_conn_status.substr(4);
                }

                if (wan_conn_status.indexOf("wifi", 0) != -1) {
                    wan_conn_status = wan_conn_status.substr(5);
                }

                conn_type = $(this).find("ConnType").text();
                proto_type = $(this).find("proto").text();
                connect_disconnect = $(this).find("connect_disconnect").text();
                if ("cellular" == connect_disconnect) {
                    g_bNetworkConnected = true;
                } else {
                    g_bNetworkConnected = false;
                }

                var connectionStatusText = "未知";
                if (sim_status != 0) {
                    connectionStatusText = jQuery.i18n.prop("lSIMAbsent");
                    if (sim_status == 1) connectionStatusText = jQuery.i18n.prop("lSIMAbsent");
                    else if (pin_status == 3) connectionStatusText = jQuery.i18n.prop("lSIMLocked");
                    else if (pin_status == 4) connectionStatusText = jQuery.i18n.prop("lSIMerror");

                } else if (pin_status == 1 || pin_status == 2) {
                    connectionStatusText = jQuery.i18n.prop("lPINrequired");
                } else if (g_bNetworkConnected) {
                    connectionStatusText = jQuery.i18n.prop("lEnabled");
                                if (roaming == 1) {
                        connectionStatusText += " (" + jQuery.i18n.prop("pRoaming") + ")";
                    }
                                } else {
                     connectionStatusText = jQuery.i18n.prop("lDisabled");
                }
                setLabelValue("NetworkType", networkTypeText);
                setLabelValue("BandInfo", networkTypeText);
                setLabelValue("lcconnectivitymode", networkTypeText);

                var operatorName = roamingNetworkName;
                if (operatorName === "中国移动") operatorName = "CMCC";
                if (operatorName === "中国联通") operatorName = "CUCC";
                if (operatorName === "中国电信") operatorName = "CTCC";
                setLabelValue("lISPName", operatorName);
                if (roaming == 1 && roamingNetworkName) {
                    if (roamingNetworkName === "中国移动") roamingNetworkName = "CMCC";
                    if (roamingNetworkName === "中国联通") roamingNetworkName = "CUCC";
                    if (roamingNetworkName === "中国电信") roamingNetworkName = "CTCC";
                    operatorName = roamingNetworkName;
                }
                if (!operatorName || operatorName.trim() === "") {
                    operatorName = "--";
                }
                setLabelValue("Operator", operatorName);
            });

            $(xml).find("lan").each(function() {
                lanip = $(this).find("ip").text();
                var mac_address = $(this).find("mac").text();
                setLabelValue("IPdiv", lanip);
                
                if (!mac_address || mac_address.trim() === "" || mac_address === "00:00:00:00:00:00") {
                    mac_address = "--";
                }
                setLabelValue("MACdiv", mac_address);
                
                var run_days, run_hours, run_minutes, run_seconds;
                var onlineDurationText = "";
                try {
                    run_days = parseInt($(this).find("run_days").text()) || 0;
                    run_hours = parseInt($(this).find("run_hours").text()) || 0;
                    run_minutes = parseInt($(this).find("run_minutes").text()) || 0;
                    run_seconds = parseInt($(this).find("run_seconds").text()) || 0;
                    
                    var run_days_text = run_days + " " + (run_days > 1 ? jQuery.i18n.prop("ldDays") : jQuery.i18n.prop("ldDay"));
                    var run_hours_text = run_hours + " " + (run_hours > 1 ? jQuery.i18n.prop("ldHours") : jQuery.i18n.prop("ldHour"));
                    var run_minutes_text = run_minutes + " " + (run_minutes > 1 ? jQuery.i18n.prop("ldMinutes") : jQuery.i18n.prop("ldMinute"));
                    var run_seconds_text = run_seconds + " " + (run_seconds > 1 ? jQuery.i18n.prop("ldSeconds") : jQuery.i18n.prop("ldSecond"));
                
                    onlineDurationText = run_days_text + " " + run_hours_text + " " + run_minutes_text + " " + run_seconds_text;
                } catch (e) {
                    onlineDurationText = "计算错误";
                } finally {
                    setLabelValue("OnlineTime", onlineDurationText.trim());
                }
            });
            if (Battery_connect == 0) {
                setLabelValueProp("lDashChargeStatus", 'lNoBattery');
                setLabelValueProp("lDashBatteryQuantity", 'lNoBattery');
                updateHeaderBatteryInfo(0, false, "未知");
                window.g_batteryInfo = { percentage: 0, isCharging: false, voltage: "未知" };
            } else if (Battery_connect == 1) {
                var isCharging = false;
                if (Battery_charging == "0")
                    setLabelValueProp("lDashChargeStatus", "lUncharged");
                else {
                    if (Battery_charge == 0)
                        setLabelValueProp("lDashChargeStatus", "lUncharged");
                    else if (Battery_charge == 1) {
                        setLabelValueProp("lDashChargeStatus", "lCharging");
                        isCharging = true;
                    } else if (Battery_charge == 2) {
                        setLabelValueProp("lDashChargeStatus", "lFullycharged");
                        isCharging = true;
                    }
                }

                var Battery_charge_percent = Battery_voltage + '%';
                setLabelValue("lDashBatteryQuantity", Battery_charge_percent);
                
                updateHeaderBatteryInfo(Battery_voltage, isCharging, Battery_voltage + "V");
                window.g_batteryInfo = { 
                    percentage: parseInt(Battery_voltage) || 0, 
                    isCharging: isCharging, 
                    voltage: Battery_voltage + "V" 
                };
            }
            $(xml).find("wlan_security").each(function() {
                var mode = $(this).find("mode").text();
                if (mode == "Mixed") {
                    mode = "dropdownWPAWPA2";
                    setLabelValueProp("lSecurityModeValue", mode);
                } else
                    setLabelValue("lSecurityModeValue", mode);
                setLabelValue("lWirelessNwValue", UniDecode($(this).find("ssid").text()));

            });
            $(xml).find("device_management").each(function() {
                var connected_number = $(this).find("nr_connected_dev").text();
                setLabelValue("lConnDeviceValue", connected_number);

            });

            $(xml).find("sysinfo").each(function() {
                var sw_version = $(this).find("version_num").text();
                var hw_version = $(this).find("hardware_version").text();
                setLabelValue("SoftwareVersion", sw_version);
                setLabelValue("SerialNumber", hw_version);

            });

            if ("mifi" == g_platformName) {
                $(xml).find("wlan_settings").each(function() {
                    setLabelValueEnabledDisabled("lWLANStatus", $(this).find("wlan_enable").text(), "imgWN");
                    wlan_enable = $(this).find("wlan_enable").text();
                    channel = $(this).find("channel").text();
                    if (channel != "0")
                        setLabelValue("lChannelNumber", $(this).find("channel").text());
                    else if (channel == "0")
                        setLabelValueProp("lChannelNumber", "dropdownWirelessAuto");
                });
                if (wlan_enable == 0) {
                    document.getElementById("wlan_settings").style.display = 'none';

                } else {
                    document.getElementById("wlan_settings").style.display = 'block';
                }

            }

            $(xml).find("dhcp").each(function() {
                setLabelValueEnabledDisabled("lDhcpServerValue", $(this).find("status").text(), "imgDhcpServerValue");
            });

            $(xml).find("message").each(function() {
                NewSMSArrivedNum = $(this).find("new_sms_num").text();
            });

            if (NewSMSArrivedNum >= 1) {
                var MessAgeNotification = "";
                if(1 == NewSMSArrivedNum)
                    MessAgeNotification = NewSMSArrivedNum + " " + jQuery.i18n.prop("lsmsOneNewArrivedSMS");
                else
                    MessAgeNotification = NewSMSArrivedNum + " " + jQuery.i18n.prop("lsmsMoreNewArrivedSMS");
                showMsgBox(jQuery.i18n.prop("lsmsNotification"), MessAgeNotification);
            }


            try {
                var deviceStatus = {
                    ipv4: $(xml).find("wan").find("ipv4").text() || 'unknown',
                    ipv6: $(xml).find("wan").find("ipv6").text() || 'unknown',
                    dns: $(xml).find("wan").find("dns1").text() + "," + $(xml).find("wan").find("dns2").text(),
                    signalStrength: rssis + 'dbm'
                };
                

                var run_days = parseInt($(xml).find("lan").find("run_days").text()) || 0;
                var run_hours = parseInt($(xml).find("lan").find("run_hours").text()) || 0;
                var run_minutes = parseInt($(xml).find("lan").find("run_minutes").text()) || 0;
                var run_seconds = parseInt($(xml).find("lan").find("run_seconds").text()) || 0;
                

                var run_days_text = run_days + " " + (run_days > 1 ? jQuery.i18n.prop("ldDays") : jQuery.i18n.prop("ldDay"));
                var run_hours_text = run_hours + " " + (run_hours > 1 ? jQuery.i18n.prop("ldHours") : jQuery.i18n.prop("ldHour"));
                var run_minutes_text = run_minutes + " " + (run_minutes > 1 ? jQuery.i18n.prop("ldMinutes") : jQuery.i18n.prop("ldMinute"));
                var run_seconds_text = run_seconds + " " + (run_seconds > 1 ? jQuery.i18n.prop("ldSeconds") : jQuery.i18n.prop("ldSecond"));
                
                deviceStatus.connTime = run_days_text + " " + run_hours_text + " " + run_minutes_text + " " + run_seconds_text;
                

                setLabelValue("IPv4div", deviceStatus.ipv4);

                setLabelValue("lIPv6Value", deviceStatus.ipv6);
                setLabelValue("lDNSValue", deviceStatus.dns.replace(/,+$/, ''));
                setLabelValue("lConnTimeValue", deviceStatus.connTime);
                setLabelValue("lSignalInfoValue", deviceStatus.signalStrength);
                

                setLabelValue("IPv4div", deviceStatus.ipv4);
            } catch(e) {
            }

            if (this.localizeBatteryLabels) {
                this.localizeBatteryLabels();
            }
        }
        this.onPostSuccess = function() {
            var now = Date.now();
            // 限制更新频率
            if (now - lastUpdateTime < UPDATE_INTERVAL) {
                return;
            }
            
            try {
                var newXml = getData(c_xmlName);
                if (newXml) {
                    cachedXmlData = newXml;
                    lastUpdateTime = now;
                }
                this.onLoad(false);
            } catch (e) {
                console.error('更新数据时出错:', e);
            }
        }
        this.Localize = function() {
            $("h2,strong,a,span").each(function() {
                var currentId = $(this).attr("id");
                if (currentId) { // Process only if ID exists
                    var originalText = $(this).text();
                    var localizedText = jQuery.i18n.prop(currentId);
                    
                   
                    if (localizedText && localizedText !== currentId) {
                        $(this).text(localizedText);
                    } else {
                    }
                }
            });
            

            this.localizeBatteryLabels();
            

            this.localizePingInfoLabels();

            this.localizeDeviceInfoTitle();
        }
        

        this.localizeBatteryLabels = function() {
            try {

                var batteryLabels = [
                    'batteryLevelLabel', 
                    'chargingStatusLabel',
                    'batteryVoltageLabel'
                ];
                

                for (var i = 0; i < batteryLabels.length; i++) {
                    var labelId = batteryLabels[i];
                    var labelElement = document.getElementById(labelId);
                    
                    if (labelElement) {

                        var translationKey = labelId.replace('Label', '');
                        var translatedText = jQuery.i18n.prop(translationKey);
                        

                        if (translatedText && translatedText !== translationKey) {
                            labelElement.textContent = translatedText;
                        }
                    }
                }
                

                var mobileLabels = document.querySelectorAll('.battery-info strong, .charging-info strong');
                for (var j = 0; j < mobileLabels.length; j++) {
                    var label = mobileLabels[j];
                    var id = label.id;
                    
                    if (id) {
                        var key = id.replace('Label', '');
                        var text = jQuery.i18n.prop(key);
                        
                        if (text && text !== key) {
                            label.textContent = text;
                        }
                    }
                }
                

                var deviceInfoTitleElement = document.getElementById('deviceInfoTitle');
                if (deviceInfoTitleElement) {
                    var deviceInfoText = jQuery.i18n.prop('deviceInfo');
                    if (deviceInfoText && deviceInfoText !== 'deviceInfo') {
                        deviceInfoTitleElement.textContent = deviceInfoText;
                    }
                }
            } catch (error) {
            }
        }
        

        this.localizeDeviceInfoTitle = function() {
            try {
                var deviceInfoTitleElement = document.getElementById('deviceInfoTitle');
                if (deviceInfoTitleElement) {
                    var translatedText = jQuery.i18n.prop('deviceInfo');
                    if (translatedText && translatedText !== 'deviceInfo') {
                        deviceInfoTitleElement.textContent = translatedText;
                    }
                }
            } catch (error) {
            }
        }
        
        this.localizePingInfoLabels = function() {
            try {
                var pingLabels = [
                    'pingResultsTitle',
                    'pingWaitingDataLabel',
                    'signalStrengthLabel',
                    'connectionStatusLabel',
                    'networkTypeLabel',
                    'operatorLabel',
                    'onlineTimeLabel',
                    'softwareVersionLabel',
                    'phyCellIdLabel',
                    'bandLabel',
                    'pIPv4',
                    'pIPv6',
                    'pDNS',
                    'pConnTime',
                    'pSignalInfo'
                ];
                
                for (var i = 0; i < pingLabels.length; i++) {
                    var labelId = pingLabels[i];
                    var labelElement = document.getElementById(labelId);
                    
                    if (labelElement) {
                        var translatedText = jQuery.i18n.prop(labelId);
                        
                        if (translatedText && translatedText !== labelId) {
                            labelElement.textContent = translatedText;
                        }
                    }
                }
            } catch (error) {
            }
        }

        this.loadHTML = function() {
            document.getElementById('mainColumn').innerHTML = "";
            document.getElementById('mainColumn').innerHTML = callProductHTML("html/dashboard.html");
            document.getElementById('homepic').style.display="flex";
        }
        this.setXMLName = function(_xmlname) {
            c_xmlName = _xmlname;
        }

        return this.each(function() {
            // 清理旧的定时器
            if (_dashboardIntervalID) {
                clearInterval(_dashboardIntervalID);
            }
            // 创建新的定时器，增加错误处理
            _dashboardIntervalID = setInterval(function() {
                try {
                    if (g_objContent && typeof g_objContent.onPostSuccess === 'function') {
                        g_objContent.onPostSuccess();
                    }
                } catch (e) {
                    console.error('定时更新出错:', e);
                }
            }, _dashboardInterval);
        });
    }
})(jQuery);
function setLabelValue(id, value) {
    var element = document.getElementById(id);
    if (element) {
        element.innerHTML = value;
    } else {
    }
}
function setLabelValueProp(id, prop) {
    var element = document.getElementById(id);
    if (element) {
        element.innerHTML = jQuery.i18n.prop(prop);
    } else {
    }
}
function setLabelValueEnabledDisabled(id, flag, imgID) {
    var labelElement = document.getElementById(id);
    var imgElement = document.getElementById(imgID);

    if (imgElement) {
        imgElement.style.display = "block";
    } else {
    }

    if (labelElement) {
    if (flag == 1) {
            labelElement.innerHTML = jQuery.i18n.prop("lEnabled");

        } else {
            labelElement.innerHTML = jQuery.i18n.prop("lDisabled");

        }
    } else {
    }
}
function setDetailPDPInfo() {

    var connectionNum = "";
    var sucess = "";
    var defaultPDP = "";
    var secondaryPdp = "";
    var typeValue = "";
    var IPV4dnsServer1;
    var IPV4dnsServer2;
    var IPV6dnsServer1;
    var IPV6dnsServer2;
    var curconntime;
    var totalconntime;

    var linkObj = document.getElementById("pdpRuleNameDropdown");

    if (!linkObj) {
        var detailPDPDiv = document.getElementById("DetailPDP_DIV");
        if (detailPDPDiv) {
            detailPDPDiv.style.display = "none";
        }
        return; 
    }


    if (-1 == linkObj.selectedIndex) {
        document.getElementById("DetailPDP_DIV").style.display = "none";
        return;
    }

    var value = linkObj.options[linkObj.selectedIndex].value;
    lastSelPdpName = value;


    document.getElementById("DetailPDP_DIV").style.display = "block";

    $(xml).find("wan").each(function() {

        $(this).find("pdp_context_list").each(function() {
            $(this).find("Item").each(function() {
                pdpruleName = "selectValue" + $(this).find("rulename").text();
                if (pdpruleName == value) {

                    defaultPDP = $(this).find("default").text();
                    secondaryPdp = $(this).find("secondary").text();
                    if (defaultPDP == "1") {
                        if (secondaryPdp == "0")
                            typeValue = jQuery.i18n.prop("SPDPType_Default") + ' ' + "Secondary PDP";
                        else
                            typeValue = jQuery.i18n.prop("SPDPType_Default") + ' ' + "Primary PDP";
                    } else if (defaultPDP == "0") {
                        if (secondaryPdp == "0")
                            typeValue = jQuery.i18n.prop("SPDPType_Custom") + ' ' + "Secondary PDP";
                        else
                            typeValue = jQuery.i18n.prop("SPDPType_Custom") + ' ' + "Primary PDP";
                    }

                    sucess = $(this).find("success").text();
                    if (sucess == "1")
                        setLabelValue("LpdpSuccess", jQuery.i18n.prop("lconnected"));
                    else if (sucess == "0")
                        setLabelValue("LpdpSuccess", jQuery.i18n.prop("ldisconnected"));
                    else if (sucess == "2")
                        setLabelValue("LpdpSuccess", jQuery.i18n.prop("lconnecting"));


                    setLabelValue("IpdpTypeValue", typeValue);
                    setLabelValue("LIPV4AddValue", $(this).find("ipv4").text());

                    IPV4dnsServer1 = $(this).find("v4dns1").text();
                    IPV4dnsServer2 = $(this).find("v4dns2").text();
                    if (IPV4dnsServer2 != "")
                        IPV4dnsServer1 = IPV4dnsServer1 + "," + IPV4dnsServer2;
                    setLabelValue("LDNSIPV4Value", IPV4dnsServer1);
                    setLabelValue("LIPV4GatewayValue", $(this).find("v4gateway").text());
                    setLabelValue("LIPV4NetmaskValue", $(this).find("v4netmask").text());

                    setLabelValue("LIPV6AddValue", $(this).find("ipv6").text());    // local ipv6 addr
                    setLabelValue("LGIPV6AddValue", $(this).find("g_ipv6").text()); //global ipv6 addr

                    IPV6dnsServer1 = $(this).find("v6dns1").text();
                    IPV6dnsServer2 = $(this).find("v6dns2").text();
                    var Ipv6DnsServerHtmlStr = "";
                    if (IPV6dnsServer2 != "" && IPV6dnsServer2 != "NA") {
                        Ipv6DnsServerHtmlStr = "<p>" + IPV6dnsServer1 + "</p><p>" + IPV6dnsServer2 + "</p>";
                        $("#LDNSIPV6Value").html(Ipv6DnsServerHtmlStr);
                    } else {
                        setLabelValue("LDNSIPV6Value", IPV6dnsServer1+","+IPV6dnsServer2);
                    }

                    setLabelValue("LIPV6GatewayValue", $(this).find("v6gateway").text());
                    setLabelValue("LIPV6NetmaskValue", $(this).find("v6netmask").text());

                    curconntime = $(this).find("curconntime").text();
                    totalconntime = $(this).find("totalconntime").text();

                    setLabelValue("lDashCurConnValue", dateFormat(curconntime));
                    setLabelValue("lDashTotalConnValue", dateFormat(totalconntime));

                    return false;
                }

            })
        })
    })
}

function CellularED() {
    var ConnType;
    var ProtoType;
    var mapData = new Array(0);

    $(xml).find("wan").each(function() {
        ConnType = $(this).find("ConnType").text();
        ProtoType = $(this).find("proto").text();
        var mapData = new Array(0);
        if (ConnType == "disabled")
            mapData = putMapElement(mapData, "RGW/wan/connect_disconnect", "cellular", 0);
        else
            mapData = putMapElement(mapData, "RGW/wan/connect_disconnect", "disabled", 0);

        postXML("wan", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
    });
}

function WiFiED() {
    if (wan_conn_status == "connected") {
        //alert("Implement disconnect");
        var mapData = new Array(0);

        //mapData = putMapElement(mapData,"RGW/wan/proto","wifi",0);
        mapData = putMapElement(mapData, "RGW/wan/wifi/ssid", "", 0);
        mapData = putMapElement(mapData, "RGW/wan/wifi/enc", "", 1);
        postXML("wan", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
    } else {
        dashboardOnClick(2, 'mInternetConn');
    }
}

function ResetTraffic() {
    //alert("Unimplement traffic reset");
    var mapData = new Array(0);
    mapData = putMapElement(mapData, "RGW/statistics/WanStatistics/reset", "1", 0);
    postXML("statistics", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
}


function dateFormat(second) {
    var dd, hh, mm, ss;
    var dayUnit = "ldDay";
    var hourUnit = "ldHour";
    var minuteUnit = "ldMinute";
    var secondUnit = "ldSecond";
    second = typeof second === 'string' ? parseInt(second) : second;

    if (second < 0) {
        return "NA";
    }

    dd = second / (24 * 3600) | 0;
    second = Math.round(second) - dd * 24 * 3600;

    hh = second / 3600 | 0;
    second = Math.round(second) - hh * 3600;

    mm = second / 60 | 0;
    ss = Math.round(second) - mm * 60;

    if (dd > 1) {
        dayUnit = "ldDays";
    }
    if (hh > 1) {
        hourUnit = "ldHours";
    }
    if (mm > 1) {
        minuteUnit = "ldMinutes";
    }
    if (ss > 1) {
        secondUnit = "ldSeconds";
    }
    

    /* if (Math.round(dd) < 10) {
        dd = dd > 0 ? '0' + dd : '';
    }
    
    if (Math.round(hh) < 10) {
        hh = '0' + hh;
    }
    
    if (Math.round(mm) < 10) {
        mm = '0' + mm;
    }

    if (Math.round(ss) < 10) {
        ss = '0' + ss;
    }*/

    //if (dd.length > 0)
    return dd + ' ' + jQuery.i18n.prop(dayUnit) + ' ' + hh + ' ' + jQuery.i18n.prop(hourUnit) + ' ' + mm + ' ' + jQuery.i18n.prop(minuteUnit) + ' ' + ss + ' ' + jQuery.i18n.prop(secondUnit) + ' ';
    //else
    //return hh + ' ' + jQuery.i18n.prop(hourUnit) + ' ' + mm + ' ' + jQuery.i18n.prop(minuteUnit) + ' ' + ss + ' ' + jQuery.i18n.prop(secondUnit) + ' ';
}

/**
 * 更新头部电池信息
 * @param {number} percentage - 电池电量百分比
 * @param {boolean} isCharging - 是否正在充电
 * @param {string} voltage - 电池电压
 */
function updateHeaderBatteryInfo(percentage, isCharging, voltage) {
    window.updateHeaderBatteryInfo = updateHeaderBatteryInfo;
    try {
        percentage = typeof percentage === 'number' ? percentage : (typeof percentage === 'string' ? parseInt(percentage, 10) || 0 : 0);
        isCharging = !!isCharging; // 转为布尔值
        voltage = voltage || '未知';
        
        var batteryLevelElement = document.getElementById('batteryLevel');
        var chargingStatusElement = document.getElementById('chargingStatus');
        var batteryVoltageElement = document.getElementById('batteryVoltage');
        var batteryLevelVisualElement = document.getElementById('batteryLevelVisual');
        
        if (batteryLevelElement) {
            batteryLevelElement.textContent = percentage + '%';
        }
        
        if (chargingStatusElement) {
            chargingStatusElement.textContent = isCharging ? jQuery.i18n.prop('lCharging') : jQuery.i18n.prop('lUncharged');
        }
        
        if (batteryVoltageElement) {
            batteryVoltageElement.textContent = voltage;
        }
        
        if (batteryLevelVisualElement) {
            batteryLevelVisualElement.style.width = percentage + '%';
            batteryLevelVisualElement.className = 'battery-level';
            
            if (isCharging) {
                batteryLevelVisualElement.classList.add('charging');
            } else if (percentage >= 60) {
                batteryLevelVisualElement.classList.add('high');
            } else if (percentage >= 30) {
                batteryLevelVisualElement.classList.add('medium');
            } else {
                batteryLevelVisualElement.classList.add('low');
            }
        }
        
        if (typeof translateBatteryLabels === 'function') {
            translateBatteryLabels();
        }
        
    } catch (error) {
    }
}

/**
 * 简化版系统诊断信息获取函数
 * @returns {Promise} 返回一个Promise，成功时传递诊断结果
 */
function PrintSysInfo() {
    return new Promise((resolve, reject) => {
        
        // 第一阶段：设置诊断参数
        setDiagnosticArg(function() {
            getFirewallData();
        });
        
        function setDiagnosticArg(callback) {
            var host = window.location.protocol + "//" + window.location.host + "/";
            var url = host + 'xml_action.cgi?method=set&module=duster&file=diagnostic';
            var xhr = new XMLHttpRequest();
            xhr.open('POST', url, true);
            xhr.timeout = 30000;
            
            try {
                xhr.setRequestHeader("Authorization", getAuthHeader("POST"));
            } catch (e) {
            }
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        callback();
                    } else {
                        reject("diagnostic设置失败: " + xhr.status);
                    }
                }
            };
            
            var diagnosticData = '<?xml version="1.0" encoding="US-ASCII"?><RGW><diagnostic><arg>1</arg></diagnostic></RGW>';
            xhr.send(diagnosticData);
        }
        
        function getFirewallData() {
            var host = window.location.protocol + "//" + window.location.host + "/";
            var url = host + 'xml_action.cgi?method=get&module=duster&file=firewall';
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.timeout = 60000;
        
            try {
                xhr.setRequestHeader("Authorization", getAuthHeader("GET"));
            } catch (e) {
            }
        
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        processFirewallResponse(xhr.responseXML);
                    } else {
                        reject("firewall请求失败: " + xhr.status);
                    }
                }
            };
        
            xhr.send();
        }
        
        function processFirewallResponse(firewallContent) {
            if(firewallContent) {
                try {
                    var arg = $(firewallContent).find("arg").text();
                    if(arg === "0") {
                        var command = $(firewallContent).find("command").text();
                        const sysInfo = parseCommand(command);
                        updateSysInfoDisplay(sysInfo);
                        resolve(sysInfo);
                    } else {
                        reject("自检失败, arg=" + arg);
                    }
                } catch (e) {
                    reject("处理数据失败: " + e.message);
                }
            } else {
                reject("获取防火墙配置失败");
            }
        }
    });
}



/**
 * 创建加载进度模拟
 * @param {boolean} showLoadingIndicator - 是否显示加载指示器
 */
function createLoadingProgressSimulation(showLoadingIndicator) {
    if (!showLoadingIndicator) return;
    
    var startTime = new Date().getTime();
    var progressInterval = setInterval(function() {
        var elapsed = new Date().getTime() - startTime;
        
        if (!isPrintSysInfoRunning) {
            clearInterval(progressInterval);
            return;
        }
        
        if (elapsed < 20000) { // 20秒内
            showSysInfoLoadingStatus(true, "正在加载系统信息...(约需1-2分钟)");
        } else if (elapsed < 40000) { // 20-40秒
            showSysInfoLoadingStatus(true, "正在处理系统诊断数据...");
        } else if (elapsed < 60000) { // 40-60秒
            showSysInfoLoadingStatus(true, "正在解析防火墙配置...");
        } else { // 60秒以上
            showSysInfoLoadingStatus(true, "加载时间较长，请耐心等待...");
            if (elapsed > 90000) {
                clearInterval(progressInterval);
            }
        }
    }, 10000); // 每10秒更新一次提示
}

/**
 * 显示或隐藏系统信息加载状态
 * @param {boolean} isLoading - 是否正在加载
 * @param {string} message - 显示的消息
 * @param {string} type - 消息类型 (success/error/warning)
 */
function showSysInfoLoadingStatus(isLoading, message = '正在加载系统信息...', type = '') {
    // 检查状态显示元素是否存在，不存在则创建
    var statusElement = document.getElementById('sysInfoLoadingStatus');
    if (!statusElement) {
        // 创建状态显示元素
        statusElement = document.createElement('div');
        statusElement.id = 'sysInfoLoadingStatus';
        statusElement.style.position = 'fixed';
        statusElement.style.bottom = '20px';
        statusElement.style.right = '20px';
        statusElement.style.padding = '10px 20px';
        statusElement.style.borderRadius = '6px';
        statusElement.style.zIndex = '9999';
        statusElement.style.fontSize = '14px';
        statusElement.style.boxShadow = '0 3px 10px rgba(0,0,0,0.2)';
        statusElement.style.transition = 'opacity 0.3s';
        statusElement.style.maxWidth = '300px';
        statusElement.style.wordWrap = 'break-word';
        document.body.appendChild(statusElement);
    }
    
    if (isLoading) {
        // 显示加载状态
        statusElement.style.backgroundColor = '#f8f9fa';
        statusElement.style.color = '#1a1a1a';
        statusElement.style.border = '1px solid #dee2e6';
        
        // 创建并更新进度条
        var progressElement = document.getElementById('sysInfoLoadingProgress');
        if (!progressElement) {
            progressElement = document.createElement('div');
            progressElement.id = 'sysInfoLoadingProgress';
            progressElement.style.width = '100%';
            progressElement.style.height = '4px';
            progressElement.style.backgroundColor = '#e9ecef';
            progressElement.style.marginTop = '8px';
            progressElement.style.borderRadius = '2px';
            progressElement.style.overflow = 'hidden';
            
            var progressBarElement = document.createElement('div');
            progressBarElement.id = 'sysInfoLoadingProgressBar';
            progressBarElement.style.height = '100%';
            progressBarElement.style.width = '5%';
            progressBarElement.style.backgroundColor = '#007bff';
            progressBarElement.style.transition = 'width 0.5s';
            progressBarElement.style.animation = 'pulse 2s infinite';
            
            progressElement.appendChild(progressBarElement);
            
            // 创建动画样式
            var styleElement = document.getElementById('loadingAnimationStyle');
            if (!styleElement) {
                styleElement = document.createElement('style');
                styleElement.id = 'loadingAnimationStyle';
                styleElement.innerHTML = '@keyframes pulse { 0% { opacity: 0.6; } 50% { opacity: 1; } 100% { opacity: 0.6; } }';
                document.head.appendChild(styleElement);
            }
        } else {
            // 更新进度条位置
            var progressBar = document.getElementById('sysInfoLoadingProgressBar');
            if (progressBar) {
                var currentWidth = parseInt(progressBar.style.width) || 5;
                // 缓慢增加进度，但永远不会达到100%
                var newWidth = Math.min(currentWidth + 5, 90);
                progressBar.style.width = newWidth + '%';
            }
        }
        
        statusElement.innerHTML = message;
        if (progressElement && !progressElement.parentNode) {
            statusElement.appendChild(progressElement);
        }
        statusElement.style.opacity = '1';
    } else {
        // 显示完成或错误状态
        if (type === 'success') {
            statusElement.style.backgroundColor = '#d4edda';
            statusElement.style.color = '#155724';
            statusElement.style.border = '1px solid #c3e6cb';
        } else if (type === 'error') {
            statusElement.style.backgroundColor = '#f8d7da';
            statusElement.style.color = '#721c24';
            statusElement.style.border = '1px solid #f5c6cb';
        } else if (type === 'warning') {
            statusElement.style.backgroundColor = '#fff3cd';
            statusElement.style.color = '#856404';
            statusElement.style.border = '1px solid #ffeeba';
        }
        
        statusElement.innerHTML = message;
        
        // 删除进度条
        var progressElement = document.getElementById('sysInfoLoadingProgress');
        if (progressElement && progressElement.parentNode) {
            progressElement.parentNode.removeChild(progressElement);
        }
        
        // 短暂显示后淡出
        setTimeout(function() {
            statusElement.style.opacity = '0';
            // 完全淡出后移除元素
            setTimeout(function() {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 300);
        }, 3000);
    }
}

/**
 * 解析系统诊断命令的输出
 * @param {string} command - 系统命令的输出文本
 * @returns {Object} 解析后的结构化数据
 */
function parseCommand(command) {
    
    // 创建结果对象
    const result = {};

    // 添加master_sim解析
    try {
        const masterSimMatch = command.match(/master_sim(?:\s+)?:(?:\s+)?(\d+)/);
        if (masterSimMatch && masterSimMatch[1]) {
            result.masterSim = masterSimMatch[1];
        }
    } catch (e) {
    }
    
    try {
        // 按行或^分割命令输出
        let lines = [];
        if (command.indexOf('^') > -1) {
            lines = command.split('^').filter(line => line.trim() !== "");
        } else {
            lines = command.split("\n").filter(line => line.trim() !== "");
        }
        
        // 处理常见的信息项
        lines.forEach(line => {
            line = line.trim();
            
            // 提取时间信息
            if (line.indexOf('time:') === 0) {
                const timeValue = line.substring(5);
                if (timeValue.length >= 14) {
                    // 格式化时间：20131231240146 -> 2013-12-31 24:01:46
                    const year = timeValue.substring(0, 4);
                    const month = timeValue.substring(4, 6);
                    const day = timeValue.substring(6, 8);
                    const hour = timeValue.substring(8, 10);
                    const minute = timeValue.substring(10, 12);
                    const second = timeValue.substring(12, 14);
                    result.time = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
                } else {
                    result.time = timeValue;
                }
            }
            
            // 提取IMEI
            else if (line.indexOf('imei:') === 0) {
                result.imei = line.substring(5);
            }
            
            // 提取SN (序列号)
            else if (line.indexOf('sn:') === 0) {
                result.sn = line.substring(3);
            }
            
            // 提取IMSI
            else if (line.indexOf('imsi:') === 0) {
                result.imsi = line.substring(5);
            }
            
            // 提取ICCID
            else if (line.indexOf('iccid:') === 0) {
                result.iccid = line.substring(6);
            }
            
            // 提取WiFi状态
            else if (line.indexOf('at+wifi=uapst') === 0) {
                const matches = line.match(/st:(\d+)\s+init:(\d+)/);
                if (matches) {
                    result.wifiStatus = matches[1] === '1' ? '开启' : '关闭';
                    result.wifiInit = matches[2] === '1' ? '已初始化' : '未初始化';
                }
            }
            
            // 提取MAC地址
            else if (line.indexOf('at+wifi=mac') === 0) {
                const macMatch = line.match(/f:([0-9a-f:]+)/i);
                if (macMatch && macMatch[1]) {
                    result.macAddress = macMatch[1];
                }
            }
            
            // 提取WiFi连接数
            else if (line.indexOf('at+wifi=stanum') === 0) {
                const matches = line.match(/n:(\d+)/);
                if (matches) {
                    result.wifiConnections = matches[1];
                }
            }
            
            // 提取网络注册信息
            else if (line.indexOf('at+creg?') === 0) {
                const matches = line.match(/\+CREG:\s+(\d+),(\d+)/);
                if (matches) {
                    const regStatus = matches[2];
                    // 网络注册状态
                    switch(regStatus) {
                        case '0': result.networkReg = '未注册'; break;
                        case '1': result.networkReg = '已注册，本地网络'; break;
                        case '2': result.networkReg = '正在搜索网络'; break;
                        case '3': result.networkReg = '注册被拒绝'; break;
                        case '4': result.networkReg = '未知状态'; break;
                        case '5': result.networkReg = '已注册，漫游状态'; break;
                        default: result.networkReg = '未知(' + regStatus + ')';
                    }
                }
            }
            
            // 提取信号强度
            else if (line.indexOf('at+csq') === 0) {
                const matches = line.match(/\+CSQ:\s+(\d+),(\d+)/);
                if (matches) {
                    const csq = parseInt(matches[1]);
                    // 信号强度等级
                    if (csq >= 0 && csq <= 31) {
                        // result.signalStrength = csq;
                    } else {
                        // result.signalStrength = 'unknown';
                   }
                }
            }
            
            // 提取扩展信号质量
            else if (line.indexOf('cesq') === 0) {
                const matches = line.match(/\*CESQ:\s*(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+)/);
                if (matches) {
                    result.rxlev = matches[1];
                    result.ber = matches[2];
                    result.rscp = matches[3];
                    result.ecno = matches[4];
                    result.rsrq = matches[5];
                    result.rsrp = matches[6];
                    result.sinr = matches[7];
                }
            }

            // 提取LTE服务信息
            else if (line.indexOf('at+eemginfo=1') === 0) {
                const eemltesvcMarker = "+EEMLTESVC:";
                const indexOfEemltesvcDataStart = line.indexOf(eemltesvcMarker);

                if (indexOfEemltesvcDataStart !== -1) {
                    const contentAfterMarker = line.substring(indexOfEemltesvcDataStart + eemltesvcMarker.length).trim();
                    
                    let eemltesvcParamsString;
                    let pingSegmentForParsing = "";

                    // Try to find where ping data might start
                    let pingKeywordStartIndex = contentAfterMarker.indexOf("Ping Statistics for");
                    if (pingKeywordStartIndex === -1) {
                        let tempIndex = contentAfterMarker.toLowerCase().indexOf("ping ");
                        // Ensure "ping " is a whole word or at the start, not part of another word like "mapping"
                        if (tempIndex !== -1 && (tempIndex === 0 || !/\\w/.test(contentAfterMarker[tempIndex-1]))) {
                            pingKeywordStartIndex = tempIndex;
                        }
                    }
                     // Check for "ping" preceded by typical separators like !, ,, ^ or at the very beginning of the segment
                    if (pingKeywordStartIndex === -1) {
                        let tempIndex = contentAfterMarker.toLowerCase().indexOf("ping");
                        if (tempIndex === 0 || (tempIndex > 0 && ['!', ',', '^', ' '].includes(contentAfterMarker[tempIndex-1]))) {
                           pingKeywordStartIndex = tempIndex;
                        }
                    }

                    if (pingKeywordStartIndex !== -1) {
                        eemltesvcParamsString = contentAfterMarker.substring(0, pingKeywordStartIndex).trim();
                        pingSegmentForParsing = contentAfterMarker.substring(pingKeywordStartIndex).trim();
                        // Clean trailing comma from params if ping was immediately after a comma
                        if (eemltesvcParamsString.endsWith(',')) {
                            eemltesvcParamsString = eemltesvcParamsString.slice(0, -1).trim();
                        }
                    } else {
                        eemltesvcParamsString = contentAfterMarker; // Assume no ping data follows, or it's not clearly identifiable here
                    }

                    if (eemltesvcParamsString) {
                        const values = eemltesvcParamsString.split(',').map(v => v.trim());

                        try {
                            if (values.length >= 5) {
                                result.phyCellId = values[4]; 
                            }
                            
                            if (values.length >= 1) {
                                result.dlEuArfcn = values[0]; 
                            }

                            if (values.length >= 7) {
                                result.ulEuArfcn = values[6]; 
                            } else if (values.length >= 6) {
                                result.ulEuArfcn = values[5]; 
                            }
                            
                            // 频段通常是第8个参数或单独计算
                            if (values.length >= 8) {
                                result.band = values[7]; // 第8个参数是频段 (索引7)
                            } else if (result.dlEuArfcn) {
                                // 如果没有直接提供频段，可以根据下行频点估算频段
                                const dlEuArfcn = parseInt(result.dlEuArfcn);
                                if (!isNaN(dlEuArfcn)) {
                                    // 根据3GPP规范估算频段
                                    if (dlEuArfcn >= 0 && dlEuArfcn <= 599) { result.band = '1'; }
                                    else if (dlEuArfcn >= 600 && dlEuArfcn <= 1199) { result.band = '2'; }
                                    else if (dlEuArfcn >= 1200 && dlEuArfcn <= 1949) { result.band = '3'; }
                                    else if (dlEuArfcn >= 1950 && dlEuArfcn <= 2399) { result.band = '4'; }
                                    else if (dlEuArfcn >= 2400 && dlEuArfcn <= 2649) { result.band = '5'; }
                                    else if (dlEuArfcn >= 2650 && dlEuArfcn <= 2749) { result.band = '6'; }
                                    else if (dlEuArfcn >= 2750 && dlEuArfcn <= 3449) { result.band = '7'; }
                                    else if (dlEuArfcn >= 3450 && dlEuArfcn <= 3799) { result.band = '8'; }
                                    else if (dlEuArfcn >= 3800 && dlEuArfcn <= 4149) { result.band = '9'; }
                                    else if (dlEuArfcn >= 4150 && dlEuArfcn <= 4749) { result.band = '10'; }
                                    else if (dlEuArfcn >= 4750 && dlEuArfcn <= 4999) { result.band = '11'; }
                                    else if (dlEuArfcn >= 5000 && dlEuArfcn <= 5179) { result.band = '12'; }
                                    else if (dlEuArfcn >= 5180 && dlEuArfcn <= 5279) { result.band = '13'; }
                                    else if (dlEuArfcn >= 5280 && dlEuArfcn <= 5379) { result.band = '14'; }
                                    else if (dlEuArfcn >= 5730 && dlEuArfcn <= 5849) { result.band = '17'; }
                                    else if (dlEuArfcn >= 5850 && dlEuArfcn <= 5999) { result.band = '18'; }
                                    else if (dlEuArfcn >= 6000 && dlEuArfcn <= 6149) { result.band = '19'; }
                                    else if (dlEuArfcn >= 6150 && dlEuArfcn <= 6449) { result.band = '20'; }
                                    else if (dlEuArfcn >= 6450 && dlEuArfcn <= 6599) { result.band = '21'; }
                                    else if (dlEuArfcn >= 6600 && dlEuArfcn <= 7399) { result.band = '22'; }
                                    else if (dlEuArfcn >= 7500 && dlEuArfcn <= 7699) { result.band = '23'; }
                                    else if (dlEuArfcn >= 7700 && dlEuArfcn <= 8039) { result.band = '24'; }
                                    else if (dlEuArfcn >= 8040 && dlEuArfcn <= 8689) { result.band = '25'; }
                                    else if (dlEuArfcn >= 8690 && dlEuArfcn <= 9039) { result.band = '26'; }
                                    else if (dlEuArfcn >= 9040 && dlEuArfcn <= 9209) { result.band = '27'; }
                                    else if (dlEuArfcn >= 9210 && dlEuArfcn <= 9659) { result.band = '28'; }
                                    else if (dlEuArfcn >= 9660 && dlEuArfcn <= 9769) { result.band = '29'; }
                                    else if (dlEuArfcn >= 9770 && dlEuArfcn <= 9869) { result.band = '30'; }
                                    else if (dlEuArfcn >= 9870 && dlEuArfcn <= 9919) { result.band = '31'; }
                                    else if (dlEuArfcn >= 9920 && dlEuArfcn <= 10359) { result.band = '32'; }
                                    else if (dlEuArfcn >= 36000 && dlEuArfcn <= 36199) { result.band = '33'; }
                                    else if (dlEuArfcn >= 36200 && dlEuArfcn <= 36349) { result.band = '34'; }
                                    else if (dlEuArfcn >= 36350 && dlEuArfcn <= 36949) { result.band = '35'; }
                                    else if (dlEuArfcn >= 36950 && dlEuArfcn <= 37549) { result.band = '36'; }
                                    else if (dlEuArfcn >= 37550 && dlEuArfcn <= 37749) { result.band = '37'; }
                                    else if (dlEuArfcn >= 37750 && dlEuArfcn <= 38249) { result.band = '38'; }
                                    else if (dlEuArfcn >= 38250 && dlEuArfcn <= 38649) { result.band = '39'; }
                                    else if (dlEuArfcn >= 38650 && dlEuArfcn <= 39649) { result.band = '40'; }
                                    else if (dlEuArfcn >= 39650 && dlEuArfcn <= 41589) { result.band = '41'; }
                                    else if (dlEuArfcn >= 41590 && dlEuArfcn <= 43589) { result.band = '42'; }
                                    else if (dlEuArfcn >= 43590 && dlEuArfcn <= 45589) { result.band = '43'; }
                                    else if (dlEuArfcn >= 45590 && dlEuArfcn <= 46589) { result.band = '44'; }
                                    else if (dlEuArfcn >= 46590 && dlEuArfcn <= 46789) { result.band = '45'; }
                                    else if (dlEuArfcn >= 46790 && dlEuArfcn <= 54539) { result.band = '46'; }
                                    else if (dlEuArfcn >= 54540 && dlEuArfcn <= 55239) { result.band = '47'; }
                                    else if (dlEuArfcn >= 55240 && dlEuArfcn <= 56739) { result.band = '48'; }
                                    else if (dlEuArfcn >= 56740 && dlEuArfcn <= 58239) { result.band = '49'; }
                                    else if (dlEuArfcn >= 58240 && dlEuArfcn <= 59089) { result.band = '50'; }
                                    else if (dlEuArfcn >= 59090 && dlEuArfcn <= 59139) { result.band = '51'; }
                                    else if (dlEuArfcn >= 59140 && dlEuArfcn <= 60139) { result.band = '52'; }
                                    else if (dlEuArfcn >= 60140 && dlEuArfcn <= 60254) { result.band = '53'; }
                                    else if (dlEuArfcn >= 65536 && dlEuArfcn <= 66435) { result.band = '65'; }
                                    else if (dlEuArfcn >= 66436 && dlEuArfcn <= 67335) { result.band = '66'; }
                                    else if (dlEuArfcn >= 67336 && dlEuArfcn <= 67535) { result.band = '67'; }
                                    else if (dlEuArfcn >= 67536 && dlEuArfcn <= 67835) { result.band = '68'; }
                                    else if (dlEuArfcn >= 67836 && dlEuArfcn <= 68335) { result.band = '69'; }
                                    else if (dlEuArfcn >= 68336 && dlEuArfcn <= 68585) { result.band = '70'; }
                                    else if (dlEuArfcn >= 68586 && dlEuArfcn <= 68935) { result.band = '71'; }
                                    else if (dlEuArfcn >= 68936 && dlEuArfcn <= 68985) { result.band = '72'; }
                                    else if (dlEuArfcn >= 68986 && dlEuArfcn <= 69035) { result.band = '73'; }
                                    else if (dlEuArfcn >= 69036 && dlEuArfcn <= 69465) { result.band = '74'; }
                                    else if (dlEuArfcn >= 69466 && dlEuArfcn <= 70315) { result.band = '75'; }
                                    else if (dlEuArfcn >= 70316 && dlEuArfcn <= 70365) { result.band = '76'; }
                                    else if (dlEuArfcn >= 70366 && dlEuArfcn <= 70545) { result.band = '85'; }
                                    else if (dlEuArfcn >= 70546 && dlEuArfcn <= 70595) { result.band = '87'; }
                                    else if (dlEuArfcn >= 70596 && dlEuArfcn <= 70645) { result.band = '88'; }
                                    else { result.band = "未知(" + dlEuArfcn + ")"; }
                                }
                            }

                        } catch (e) {
                        }
                        // End of EEMLTESVC parameter parsing

                        // Parse ping data if it was found and separated
                        if (pingSegmentForParsing) {
                             // Clean up leading non-alphanumeric characters that might be separators, like '!', '^', or ','
                            let cleanedPingSegment = pingSegmentForParsing;
                            const firstAlphaNumeric = cleanedPingSegment.search(/[a-zA-Z0-9]/);
                            if (firstAlphaNumeric > 0) {
                                cleanedPingSegment = cleanedPingSegment.substring(firstAlphaNumeric);
                            }
                            
                            if (cleanedPingSegment.length > 0 && 
                                (cleanedPingSegment.toLowerCase().startsWith("ping statistics for") || cleanedPingSegment.toLowerCase().startsWith("ping "))) {
                                parsePingLine(cleanedPingSegment, result);
                            } else if (cleanedPingSegment.length > 0 && pingSegmentForParsing.toLowerCase().includes("ping")) {
                                // Fallback for less standard ping prefixes if the segment still contains "ping"
                                parsePingLine(cleanedPingSegment, result);
                            }
                        }
                    }
                }
            }
            // 提取ping测试结果 - 包括fail开头的ping结果
            else if (line.indexOf('ping') === 0 || line.indexOf('Ping') === 0 || line.indexOf('Ping Statistics for') === 0 || line.indexOf('fail,') === 0) {
                parsePingLine(line, result); // 直接调用新的解析函数
            }

        });
        
        console.log("命令解析完成，找到", Object.keys(result).length, "项信息");
        console.log("最终result对象:", result);
        if (result.pingTests) {
            console.log("Ping测试结果数量:", result.pingTests.length);
        } else {
        }
    } catch (error) {
        result.error = error.message;
    }
    
    return result;
}

/**
 * 将解析后的系统信息显示到页面上
 * @param {Object} info - 解析后的系统信息
 */
function updateSysInfoDisplay(info) {
    
    try {
        if (info.imei) setLabelValue("IMEIdiv", info.imei);
        // if (info.iccid) setLabelValue("ICCIDdiv", info.iccid);
        if (info.macAddress) setLabelValue("MACdiv", info.macAddress);
        if (info.signalStrength) setLabelValue("SignalStrength", info.signalStrength );
        // if (info.networkReg) setLabelValue("ConnectionStatus", info.networkReg);
        if (info.imsi) setLabelValue("IMSIdiv", info.imsi);
        
        // 更新CESQ信号质量参数（根据AT+CESQ定义转换）
        setLabelValue("RxlevDiv", info.rxlev && info.rxlev != '99' ? (-110 + parseInt(info.rxlev)) + 'dBm' : '--');
        setLabelValue("BerDiv", info.ber || '--');
        setLabelValue("RscpDiv", info.rscp || '--');
        setLabelValue("EcnoDiv", info.ecno || '--');
        setLabelValue("RsrqDiv", info.rsrq && info.rsrq != '255' ? (-19.5 + parseInt(info.rsrq) * 0.5) + 'dB' : '--');
        setLabelValue("RsrpDiv", info.rsrp && info.rsrp != '255' ? (-140 + parseInt(info.rsrp)) + 'dBm' : '--');
        setLabelValue("SinrDiv", info.sinr && info.sinr != '255' ? (-23 + parseInt(info.sinr) * 0.5) + 'dB' : '--');
        
        // 更新物理小区信息到homepic区域的元素中
        setLabelValue("PhyCellIdDiv", info.phyCellId || '--');
        setLabelValue("DlEuArfcnDiv", info.dlEuArfcn || '--');
        setLabelValue("UlEuArfcnDiv", info.ulEuArfcn || '--');
        setLabelValue("BandDiv", info.band || '--');
        
        // 更新Ping测试结果到 .ping-results-row #pingResultsContainer
        var pingResultsContainer = document.querySelector('.ping-results-row #pingResultsContainer');
        if (pingResultsContainer) {
            pingResultsContainer.innerHTML = ""; // 清空现有内容

            if (info.pingTests && info.pingTests.length > 0) {
                info.pingTests.forEach(function(pingTest, index) {
                    var pingResultItem = document.createElement("div");
                    pingResultItem.className = "ping-result-item";
                    // 移除内联样式，使用CSS控制
                    // pingResultItem.style.cursor = "pointer";
                    pingResultItem.title = "点击查看详情";

                    var urlSpan = document.createElement("div");
                    urlSpan.className = "ping-result-url";
                    urlSpan.textContent = pingTest.url || jQuery.i18n.prop("lUnknownTarget");
                    pingResultItem.appendChild(urlSpan);

                    var statusSpan = document.createElement("div");
                    statusSpan.className = "ping-result-status " + (pingTest.status === jQuery.i18n.prop("lPingSuccess") ? "ping-success" : "ping-fail");
                    
                    if (pingTest.status === jQuery.i18n.prop("lPingSuccess") && pingTest.time) {
                        statusSpan.textContent = pingTest.status + " (" + pingTest.time + ")";
                    } else {
                        statusSpan.textContent = pingTest.status || jQuery.i18n.prop("lStatusUnknown");
                    }
                    pingResultItem.appendChild(statusSpan);

                    // 点击事件显示详情
                    pingResultItem.onclick = function() {
                        alert("Ping " + jQuery.i18n.prop("lDetails") + " (" + (pingTest.url || jQuery.i18n.prop("lUnknownTarget")) + "):\\n" + (pingTest.details || jQuery.i18n.prop("lNoDetailInfo")));
                    };
                    
                    pingResultsContainer.appendChild(pingResultItem);
                });
            } else {
                var noPingDataPlaceholder = document.createElement("div");
                noPingDataPlaceholder.className = "ping-result-placeholder";
                // 移除内联样式，使用CSS控制
                // noPingDataPlaceholder.style.textAlign = "center";
                // noPingDataPlaceholder.style.color = "#999";
                // noPingDataPlaceholder.style.padding = "10px";
                noPingDataPlaceholder.textContent = (info && info.pingTests && info.pingTests.length === 0) ? 
                    jQuery.i18n.prop("lNoPingTestData") : jQuery.i18n.prop("lPingDataLoadingOrUnavailable");
                pingResultsContainer.appendChild(noPingDataPlaceholder);
            }
        } else {
        }

        // 更新头部网络状态指示器
        var headerBatteryInfo = document.querySelector('.header-battery-info');
        if (headerBatteryInfo) {
            var networkStatusInfoDiv = headerBatteryInfo.querySelector('.network-status-info');
            if (!networkStatusInfoDiv) {
                networkStatusInfoDiv = document.createElement('div');
                networkStatusInfoDiv.className = 'header-info-item network-status-info';
                
                var strongEl = document.createElement('strong');
                strongEl.textContent = jQuery.i18n.prop("lNetworkStatus");
                networkStatusInfoDiv.appendChild(strongEl);
                
                var infoValueSpan = document.createElement('span');
                infoValueSpan.className = 'info-value';
                
                var dotSpan = document.createElement('span');
                dotSpan.id = 'networkStatusDot';
                dotSpan.className = 'status-dot'; // 用于基础样式
                dotSpan.style.display = 'inline-block';
                dotSpan.style.width = '10px';
                dotSpan.style.height = '10px';
                dotSpan.style.borderRadius = '50%';
                dotSpan.style.marginRight = '5px';
                dotSpan.style.verticalAlign = 'middle';

                var textSpan = document.createElement('span');
                textSpan.id = 'networkStatusText';
                
                infoValueSpan.appendChild(dotSpan);
                infoValueSpan.appendChild(textSpan);
                networkStatusInfoDiv.appendChild(infoValueSpan);
                headerBatteryInfo.appendChild(networkStatusInfoDiv);
            }

            var statusDot = networkStatusInfoDiv.querySelector('#networkStatusDot');
            var statusText = networkStatusInfoDiv.querySelector('#networkStatusText');

            if (statusDot && statusText) {
                if (info.pingTests && info.pingTests.length > 0) {
                    var allFailed = info.pingTests.every(pt => pt.status !== jQuery.i18n.prop("lPingSuccess"));
                    if (allFailed) {
                        statusDot.style.backgroundColor = "red";
                        statusText.textContent = jQuery.i18n.prop("lNetworkNotAccessible");
                        statusText.style.color = "red";
                    } else {
                        statusDot.style.backgroundColor = "green";
                        statusText.textContent = jQuery.i18n.prop("lNetworkAccessible");
                        statusText.style.color = "green";
                    }
                } else {
                    // 没有ping测试数据，显示未知或默认状态
                    statusDot.style.backgroundColor = "gray";
                    statusText.textContent = jQuery.i18n.prop("lNetworkStatusUnknown");
                    statusText.style.color = "gray";
                }
            }
        } else {
        }

        // 处理网络类型和频段信息 (原有逻辑保留)
        var networkType = "";
        if (info.earfcn) {
            // 如果有LTE信息，说明是4G网络
            networkType = "LTE";
            if (info.band) {
                setLabelValue("BandInfo", "频段 " + info.band);
            }
        } else {
            // 否则根据信号质量判断可能是2G/3G
            networkType = "unknown";
        }
        if (networkType) setLabelValue("NetworkType", networkType);
        
        // 添加PhyCell信息到序列号下面 (原有逻辑保留)
        var serialNumberElement = document.getElementById("SerialNumber");
        if (serialNumberElement && serialNumberElement.parentNode) {
            var phyCellInfoContainer = document.getElementById("PhyCellInfoContainer");
            
            // 如果容器不存在，创建新的
            if (!phyCellInfoContainer) {
                phyCellInfoContainer = document.createElement("div");
                phyCellInfoContainer.id = "PhyCellInfoContainer";
                phyCellInfoContainer.style.marginTop = "8px";
                serialNumberElement.parentNode.appendChild(phyCellInfoContainer);
            }
        }
        
        // 移除之前动态添加的简化版Ping测试结果到info-row底部的逻辑，因为现在使用 .ping-results-row
        var oldSimplePingContainer = document.querySelector('.info-row #simplePingContainer');
        if (oldSimplePingContainer && oldSimplePingContainer.parentNode) {
            oldSimplePingContainer.parentNode.removeChild(oldSimplePingContainer);
        }
        
        // 获取systemInfoContent元素
        var systemInfoContent = document.getElementById("systemInfoContent");
        if (!systemInfoContent) {
            return;
        }
        
        // 生成HTML内容
        var html = '<div style="font-family: Arial, sans-serif;">';
        
        // 添加标题
        html += '<h3 style="color: #1976d2; margin: 15px 0; font-size: 16px; border-bottom: 1px solid #dee2e6; padding-bottom: 10px;">详细系统诊断信息</h3>';
        
        // 添加设备信息区
        html += '<div style="margin-bottom: 20px;">';
        html += '<h4 style="color: #495057; margin: 0 0 10px 0; font-size: 14px;">设备信息</h4>';
        html += '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px; margin-bottom: 15px;">';
        
        // 添加设备信息项
        const deviceInfoItems = [
            { label: "时间", value: info.time || '未知' },
            { label: "IMEI", value: info.imei || '未知' },
            { label: "SN", value: info.sn || '未知' },
            { label: "IMSI", value: info.imsi || '未知' },
            { label: "ICCID", value: info.iccid || '未知' },
            { label: "MAC地址", value: info.macAddress || '未知' }
        ];
        
        deviceInfoItems.forEach(item => {
            html += '<div style="padding: 8px 10px; background-color: #ffffff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">';
            html += '<span style="color: #6c757d; font-size: 12px; display: block; margin-bottom: 3px;">' + item.label + '</span>';
            html += '<span style="color: #212529; font-size: 14px; word-break: break-all;">' + item.value + '</span>';
            html += '</div>';
        });
        
        html += '</div>';
        html += '</div>';
        
        // 添加网络状态区，包括5-8项重要参数
        html += '<div style="margin-bottom: 20px;">';
        html += '<h4 style="color: #495057; margin: 0 0 10px 0; font-size: 14px;">网络状态</h4>';
        html += '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px; margin-bottom: 15px;">';
        
        // 添加网络状态项
        const networkInfoItems = [
            { label: "网络注册", value: info.networkReg || 'unknown', color: info.networkReg === '已注册，本地网络' ? '#28a745' : (info.networkReg === '已注册，漫游状态' ? '#ffc107' : '#6c757d') },
            { label: "信号强度", value: info.signalStrength || 'unknown', color: info.signalQuality === '优' ? '#28a745' : (info.signalQuality === '良' ? '#17a2b8' : (info.signalQuality === '中' ? '#ffc107' : '#dc3545')) },
            { label: "网络类型", value: networkType || 'unknown' },
            { label: "物理小区ID (p5)", value: info.phyCellId || 'unknown' },
            { label: "下行频点 (p6)", value: info.dlEuArfcn || 'unknown' },
            { label: "上行频点 (p7)", value: info.ulEuArfcn || 'unknown' },
            { label: "频段 (p8)", value: info.band || 'unknown' },
            { label: "RXLEV", value: info.rxlev || 'unknown' },
            { label: "BER", value: info.ber || 'unknown' },
            { label: "RSCP", value: info.rscp || 'unknown' },
            { label: "ECNO", value: info.ecno || 'unknown' },
            { label: "RSRQ", value: info.rsrq || 'unknown' },
            { label: "RSRP", value: info.rsrp || 'unknown' },
            { label: "SINR", value: info.sinr || 'unknown' }
        ];
        
        networkInfoItems.forEach(item => {
            html += '<div style="padding: 8px 10px; background-color: #ffffff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">';
            html += '<span style="color: #6c757d; font-size: 12px; display: block; margin-bottom: 3px;">' + item.label + '</span>';
            html += '<span style="color: ' + (item.color || '#212529') + '; font-size: 14px;">' + item.value + '</span>';
            html += '</div>';
        });
        
        html += '</div>';
        html += '</div>';
        
        // 添加WiFi状态区
        html += '<div style="margin-bottom: 20px;">';
        html += '<h4 style="color: #495057; margin: 0 0 10px 0; font-size: 14px;">WiFi状态</h4>';
        html += '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px; margin-bottom: 15px;">';
        
        // 添加WiFi状态项
        const wifiInfoItems = [
            { label: "WiFi状态", value: info.wifiStatus || '未知', color: info.wifiStatus === '开启' ? '#28a745' : '#dc3545' },
            { label: "WiFi初始化", value: info.wifiInit || '未知' },
            { label: "连接设备数", value: info.wifiConnections || '0' }
        ];
        
        wifiInfoItems.forEach(item => {
            html += '<div style="padding: 8px 10px; background-color: #ffffff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">';
            html += '<span style="color: #6c757d; font-size: 12px; display: block; margin-bottom: 3px;">' + item.label + '</span>';
            html += '<span style="color: ' + (item.color || '#212529') + '; font-size: 14px;">' + item.value + '</span>';
            html += '</div>';
        });
        
        html += '</div>';
        html += '</div>';
        
        // 添加网络诊断区
        if (info.pingTests && info.pingTests.length > 0) {
            html += '<div style="margin-bottom: 10px;">';
            html += '<h4 style="color: #495057; margin: 0 0 10px 0; font-size: 14px;">网络诊断</h4>';
            html += '<div style="background-color: #ffffff; border-radius: 4px; padding: 10px; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">';
            
            // 表格样式
            html += '<table style="width: 100%; border-collapse: collapse;">';
            html += '<thead><tr>';
            html += '<th style="text-align: left; padding: 6px 10px; border-bottom: 1px solid #dee2e6; color: #495057; font-weight: 500; font-size: 14px;">目标地址</th>';
            html += '<th style="text-align: left; padding: 6px 10px; border-bottom: 1px solid #dee2e6; color: #495057; font-weight: 500; font-size: 14px;">状态</th>';
            html += '</tr></thead>';
            
            html += '<tbody>';
            info.pingTests.forEach(test => {
                html += '<tr>';
                html += '<td style="padding: 6px 10px; border-bottom: 1px solid #f0f0f0; font-size: 14px;">' + test.url + '</td>';
                html += '<td style="padding: 6px 10px; border-bottom: 1px solid #f0f0f0; font-size: 14px; color: ' + (test.status === '成功' ? '#28a745' : '#dc3545') + ';">' + test.status + '</td>';
                html += '</tr>';
            });
            html += '</tbody>';
            
            html += '</table>';
            html += '</div>';
            html += '</div>';
        }
        
        // 添加刷新按钮
        html += '<div style="text-align: center; margin: 15px 0;">';
        html += '<button id="refreshSysInfoBtn" style="padding: 8px 16px; background-color: #f8f9fa; color: #212529; border: 1px solid #dee2e6; border-radius: 4px; cursor: pointer; font-size: 14px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">手动刷新系统信息</button>';
        html += '<div style="margin-top: 5px; font-size: 12px; color: #6c757d;">系统信息加载时间较长，更新期间不会影响其他操作</div>';
        html += '</div>';
        
        // 关闭主div
        html += '</div>';
        
        // 更新HTML内容
        systemInfoContent.innerHTML = html;
        
        // 添加刷新按钮点击事件
        var refreshBtn = document.getElementById("refreshSysInfoBtn");
        if (refreshBtn) {
            refreshBtn.onclick = function() {
                // 显示加载提示
                systemInfoContent.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin" style="font-size: 24px; color: #1976d2;"></i><p style="margin-top: 10px; color: #666;">正在加载系统信息...</p></div>';
                
                // 不再自动调用PrintSysInfo
            };
        }
        
    } catch (error) {
    }
}

/**
 * 添加系统信息刷新按钮
 */
function addSysInfoRefreshButton() {
    // 此函数保留但不再使用，刷新按钮功能已移至updateSysInfoDisplay
    return;
}

/**
 * 模拟系统信息数据，用于测试显示效果
 * @param {string} testData - 可选的测试数据字符串
 * @returns {Object} 解析后的结构化数据
 */
function simulateSystemInfo(testData) {
    
    // 如果提供了测试数据，尝试解析它
    if (testData) {
        return parseCommand(testData);
    }
    
    // 没有提供测试数据，使用默认测试数据
    
    return parseCommand(defaultTestData);
}

/**
 * 在页面加载完成后添加系统信息测试按钮（仅在开发环境使用）
 */
function addSystemInfoTestControls() {
    
    // 找到systemInfoContent元素
    var systemInfoContent = document.getElementById('systemInfoContent');
    if (!systemInfoContent) return;
    
    // 如果systemInfoContent已经有内容，不添加测试控件
    if (systemInfoContent.innerHTML.trim() !== '') return;
    
    // 创建测试控件
    var testControls = document.createElement('div');
    testControls.style.padding = '15px';
    testControls.style.marginBottom = '15px';
    testControls.style.borderBottom = '1px solid #dee2e6';
    
    // 添加标题
    var title = document.createElement('h3');
    title.textContent = '系统信息测试控件';
    title.style.fontSize = '16px';
    title.style.marginBottom = '10px';
    title.style.color = '#1976d2';
    testControls.appendChild(title);
    
    // 添加说明
    var description = document.createElement('p');
    description.textContent = '以下按钮用于测试系统信息显示，仅在开发环境使用。';
    description.style.fontSize = '14px';
    description.style.marginBottom = '15px';
    description.style.color = '#666';
    testControls.appendChild(description);
    
    // 添加测试按钮
    var buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '10px';
    buttonContainer.style.flexWrap = 'wrap';
    
    // 测试按钮1：使用默认测试数据
    var defaultTestButton = document.createElement('button');
    defaultTestButton.textContent = '使用默认测试数据';
    defaultTestButton.style.padding = '8px 12px';
    defaultTestButton.style.backgroundColor = '#0066cc';
    defaultTestButton.style.color = '#fff';
    defaultTestButton.style.border = 'none';
    defaultTestButton.style.borderRadius = '4px';
    defaultTestButton.style.cursor = 'pointer';
    defaultTestButton.onclick = function() {
        var testData = simulateSystemInfo();
        updateSysInfoDisplay(testData);
    };
    buttonContainer.appendChild(defaultTestButton);
    
    // 测试按钮2：使用用户输入的测试数据
    var customTestButton = document.createElement('button');
    customTestButton.textContent = '使用自定义测试数据';
    customTestButton.style.padding = '8px 12px';
    customTestButton.style.backgroundColor = '#28a745';
    customTestButton.style.color = '#fff';
    customTestButton.style.border = 'none';
    customTestButton.style.borderRadius = '4px';
    customTestButton.style.cursor = 'pointer';
    customTestButton.onclick = function() {
        var customData = prompt('请输入测试数据（格式如：time:20131231240146^imei:123456789...）：');
        if (customData) {
            var testData = simulateSystemInfo(customData);
            updateSysInfoDisplay(testData);
        }
    };
    buttonContainer.appendChild(customTestButton);
    
    // 测试按钮3：尝试从服务器获取
    var serverTestButton = document.createElement('button');
    serverTestButton.textContent = '从服务器获取数据';
    serverTestButton.style.padding = '8px 12px';
    serverTestButton.style.backgroundColor = '#6c757d';
    serverTestButton.style.color = '#fff';
    serverTestButton.style.border = 'none';
    serverTestButton.style.borderRadius = '4px';
    serverTestButton.style.cursor = 'pointer';
    serverTestButton.onclick = function() {
        if (typeof PrintSysInfo === 'function') {
            // 显示加载中
            systemInfoContent.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fa fa-spinner fa-spin" style="font-size: 24px; color: #1976d2;"></i><p style="margin-top: 10px; color: #666;">正在加载系统信息...</p></div>';
            
            // 调用PrintSysInfo获取数据
            PrintSysInfo().catch(function(error) {
                systemInfoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #dc3545;"><i class="fa fa-exclamation-circle" style="font-size: 24px;"></i><p style="margin-top: 10px;">加载系统信息失败: ' + error + '</p></div>';
            });
        } else {
            alert('PrintSysInfo函数未定义，无法从服务器获取数据。');
        }
    };
    buttonContainer.appendChild(serverTestButton);
    
    testControls.appendChild(buttonContainer);
    
    // 在systemInfoContent的开头插入测试控件
    systemInfoContent.innerHTML = '';
    systemInfoContent.appendChild(testControls);
    
}

// 在页面加载完成后添加测试控件（根据需要注释掉或移除此行）
document.addEventListener('DOMContentLoaded', function() {
    // 延迟2秒后添加测试控件
    setTimeout(addSystemInfoTestControls, 2000);
});

/**
 * 仅刷新Ping测试结果的显示，不更新其他系统信息
 * @param {Object} info - 包含pingTests数组的对象
 */
function refreshPingResultsDisplay(info) {
    if (!info || !info.pingTests) { // 检查 info 和 info.pingTests 是否存在
        // 考虑在这里也更新头部网络状态为未知或错误
        var headerBatteryInfo = document.querySelector('.header-battery-info');
        if (headerBatteryInfo) {
            var networkStatusInfoDiv = headerBatteryInfo.querySelector('.network-status-info');
            if (!networkStatusInfoDiv) {
                networkStatusInfoDiv = document.createElement('div');
                networkStatusInfoDiv.className = 'header-info-item network-status-info';
                var strongEl = document.createElement('strong');
                strongEl.textContent = jQuery.i18n.prop("lNetworkStatus");
                networkStatusInfoDiv.appendChild(strongEl);
                var infoValueSpan = document.createElement('span');
                infoValueSpan.className = 'info-value';
                var dotSpan = document.createElement('span');
                dotSpan.id = 'networkStatusDot';
                dotSpan.className = 'status-dot';
                dotSpan.style.display = 'inline-block';
                dotSpan.style.width = '10px';
                dotSpan.style.height = '10px';
                dotSpan.style.borderRadius = '50%';
                dotSpan.style.marginRight = '5px';
                dotSpan.style.verticalAlign = 'middle';
                var textSpan = document.createElement('span');
                textSpan.id = 'networkStatusText';
                infoValueSpan.appendChild(dotSpan);
                infoValueSpan.appendChild(textSpan);
                networkStatusInfoDiv.appendChild(infoValueSpan);
                headerBatteryInfo.appendChild(networkStatusInfoDiv);
            }
            var statusDot = networkStatusInfoDiv.querySelector('#networkStatusDot');
            var statusText = networkStatusInfoDiv.querySelector('#networkStatusText');
            if(statusDot && statusText){
                statusDot.style.backgroundColor = "gray";
                statusText.textContent = jQuery.i18n.prop("lNetworkStatusUnknown");
                statusText.style.color = "gray";
            }
        }
        // 清空ping结果显示区
        var mainPingContainer = document.querySelector('.ping-results-row #pingResultsContainer');
        if (mainPingContainer) {
            mainPingContainer.innerHTML = '';
            var noPingDataPlaceholder = document.createElement("div");
            noPingDataPlaceholder.className = "ping-result-placeholder";
            // 移除内联样式，使用CSS控制
            // noPingDataPlaceholder.style.textAlign = "center";
            // noPingDataPlaceholder.style.color = "#999";
            // noPingDataPlaceholder.style.padding = "10px";
            noPingDataPlaceholder.textContent = (info && info.pingTests && info.pingTests.length === 0) ? 
                jQuery.i18n.prop("lNoPingTestData") : jQuery.i18n.prop("lPingDataLoadingOrUnavailable");
            mainPingContainer.appendChild(noPingDataPlaceholder);
        }
        return;
    }
    
    
    try {
        var mainPingContainer = document.querySelector('.ping-results-row #pingResultsContainer');
        
        if (mainPingContainer) {
            mainPingContainer.innerHTML = ""; // 清空容器
            
            if (info.pingTests.length > 0) {
                info.pingTests.forEach(function(pingTest, index) {
                    var pingResultItem = document.createElement("div");
                    pingResultItem.className = "ping-result-item";
                    pingResultItem.style.cursor = "pointer";
                    pingResultItem.title = "点击查看详情";

                    var urlSpan = document.createElement("div");
                    urlSpan.className = "ping-result-url";
                    urlSpan.textContent = pingTest.url || '未知目标';
                    pingResultItem.appendChild(urlSpan);

                    var statusSpan = document.createElement("div");
                    statusSpan.className = "ping-result-status " + (pingTest.status === "成功" ? "ping-success" : "ping-fail");
                    
                    if (pingTest.status === "成功" && pingTest.time) {
                        statusSpan.textContent = pingTest.status + " (" + pingTest.time + ")";
                    } else {
                        statusSpan.textContent = pingTest.status || '状态未知';
                    }
                    pingResultItem.appendChild(statusSpan);

                    pingResultItem.onclick = function() {
                        alert("Ping " + jQuery.i18n.prop("lDetails") + " (" + (pingTest.url || jQuery.i18n.prop("lUnknownTarget")) + "):\\n" + (pingTest.details || jQuery.i18n.prop("lNoDetailInfo")));
                    };
                    
                    mainPingContainer.appendChild(pingResultItem);
                });
            } else {
                var noPingDataPlaceholder = document.createElement("div");
                noPingDataPlaceholder.className = "ping-result-placeholder";
                noPingDataPlaceholder.style.textAlign = "center";
                noPingDataPlaceholder.style.color = "#999";
                noPingDataPlaceholder.style.padding = "10px";
                noPingDataPlaceholder.textContent = (info && info.pingTests && info.pingTests.length === 0) ? 
                    jQuery.i18n.prop("lNoPingTestData") : jQuery.i18n.prop("lPingDataLoadingOrUnavailable");
                mainPingContainer.appendChild(noPingDataPlaceholder);
            }
        } else {
            //console.warn("Ping结果容器 '.ping-results-row #pingResultsContainer' 在 refreshPingResultsDisplay 中未找到。");
        }

        // 在 refreshPingResultsDisplay 中也更新头部网络状态指示器
        var headerBatteryInfo = document.querySelector('.header-battery-info');
        if (headerBatteryInfo) {
            var networkStatusInfoDiv = headerBatteryInfo.querySelector('.network-status-info');
            if (!networkStatusInfoDiv) {
                networkStatusInfoDiv = document.createElement('div');
                networkStatusInfoDiv.className = 'header-info-item network-status-info';
                var strongEl = document.createElement('strong');
                strongEl.textContent = jQuery.i18n.prop("lNetworkStatus");
                networkStatusInfoDiv.appendChild(strongEl);
                var infoValueSpan = document.createElement('span');
                infoValueSpan.className = 'info-value';
                var dotSpan = document.createElement('span');
                dotSpan.id = 'networkStatusDotRefresh'; // 使用不同ID以避免冲突，如果需要严格分离
                dotSpan.className = 'status-dot';
                dotSpan.style.display = 'inline-block';
                dotSpan.style.width = '10px';
                dotSpan.style.height = '10px';
                dotSpan.style.borderRadius = '50%';
                dotSpan.style.marginRight = '5px';
                dotSpan.style.verticalAlign = 'middle';
                var textSpan = document.createElement('span');
                textSpan.id = 'networkStatusTextRefresh'; // 使用不同ID
                infoValueSpan.appendChild(dotSpan);
                infoValueSpan.appendChild(textSpan);
                networkStatusInfoDiv.appendChild(infoValueSpan);
                // 确保只添加一次
                if (!headerBatteryInfo.querySelector('.network-status-info')) {
                    headerBatteryInfo.appendChild(networkStatusInfoDiv);
                }
            } else {
                 // 如果元素已存在，确保获取正确的dot和text span (可能ID不同或不需要ID)
                statusDot = networkStatusInfoDiv.querySelector('.status-dot'); // 通过class获取
                statusText = networkStatusInfoDiv.querySelector('span:not(.status-dot)'); // 获取非dot的span
            }

            var statusDot = networkStatusInfoDiv.querySelector('.status-dot'); // 重新获取，确保拿到最新的
            var statusText = networkStatusInfoDiv.querySelector('#networkStatusTextRefresh') || networkStatusInfoDiv.querySelector('span:not(.status-dot)');
            // 如果通过ID拿不到，尝试通过非.status-dot的span获取
             if (!statusText && networkStatusInfoDiv.lastChild && networkStatusInfoDiv.lastChild.lastChild && networkStatusInfoDiv.lastChild.lastChild.nodeName === 'SPAN' && !networkStatusInfoDiv.lastChild.lastChild.classList.contains('status-dot')) {
                statusText = networkStatusInfoDiv.lastChild.lastChild;
            }

            if (statusDot && statusText) {
                if (info.pingTests && info.pingTests.length > 0) {
                    var allFailed = info.pingTests.every(pt => pt.status !== jQuery.i18n.prop("lPingSuccess"));
                    if (allFailed) {
                        statusDot.style.backgroundColor = "red";
                        statusText.textContent = jQuery.i18n.prop("lNetworkNotAccessible");
                        statusText.style.color = "red";
                    } else {
                        statusDot.style.backgroundColor = "green";
                        statusText.textContent = jQuery.i18n.prop("lNetworkAccessible");
                        statusText.style.color = "green";
                    }
                } else {
                    statusDot.style.backgroundColor = "gray";
                    statusText.textContent = jQuery.i18n.prop("lNetworkStatusUnknown");
                    statusText.style.color = "gray";
                }
            }
        }

    } catch (error) {
        //console.error("刷新Ping测试结果显示时出错 (refreshPingResultsDisplay):", error);
    }
}

// 新增的 Ping 解析函数
function parsePingLine(pingLine, result) {
    //console.log("进入 Ping 解析函数。当前 pingLine: " + pingLine);
    if (!result.pingTests) {
        result.pingTests = [];
    }

    // 标记是否找到匹配
    let foundMatch = false;

    // 0. 首先检查是否是简单的失败格式："fail, ping http://www.baidu.com.fail, ping device.10086group.com."
    if (!foundMatch && pingLine.indexOf('fail,') === 0) {
        //console.log("检测到简单失败格式的ping结果");
        // 使用正则表达式匹配所有的ping部分，避免被fail干扰
        const pingPattern = /ping\s+(https?:\/\/)?([^\s,]+)/g;
        let match;
        while ((match = pingPattern.exec(pingLine)) !== null) {
            let url = match[2]; // 获取域名部分
            // 清理URL，移除末尾的点号
            url = url.replace(/\.$/, '');
            if (url && url.indexOf('.') > -1) { // 确保是有效域名
                result.pingTests.push({
                    url: url,
                    status: jQuery.i18n.prop("lPingFailure") || '失败',
                    details: 'ping ' + url + ' 失败'
                });
                //console.log("解析到失败的ping目标:", url);
            }
        }
        foundMatch = true;
    }

    // 1. 先检查是否是多个Ping结果组合的情况（用!分隔）
    if (!foundMatch && pingLine.indexOf('Ping Statistics for') > -1 && pingLine.indexOf('!') > -1) {
        //console.log("检测到多个Ping结果组合");
        // 按!拆分多个Ping结果
        const pingBlocks = pingLine.split('!').filter(block => block.trim().length > 0);

        pingBlocks.forEach(block => {
            // 提取URL
            const urlMatch = block.match(/Ping Statistics for ([^\s:]+):/);
            if (urlMatch && urlMatch[1]) {
                const url = urlMatch[1];

                // 提取关键数据 - 改进正则表达式以匹配更多格式
                const lostMatch = block.match(/Lost\s*=\s*\d+\s*\((\d+\.?\d*)%\s*lost\)/i);
                const timeMatch = block.match(/Average\s*=\s*(\d+)ms/i);

                // 判断是否成功（50%丢包视为成功）
                const lostPercentage = lostMatch ? parseFloat(lostMatch[1]) : 100;
                const isSuccess = lostPercentage <50;

                result.pingTests.push({
                    url: url,
                    status: isSuccess ? jQuery.i18n.prop("lPingSuccess") : jQuery.i18n.prop("lPingFailure"),
                    time: timeMatch ? timeMatch[1] + 'ms' : '',
                    details: block.trim()
                });
                //console.log("解析Ping块结果:", url, isSuccess ? '成功' : '失败', timeMatch ? timeMatch[1] + 'ms' : '');
            }
        });
        foundMatch = true;
    }

    // 尝试解析只有一个结果但格式类似的情况
    if (!foundMatch && pingLine.indexOf('Ping Statistics for') > -1) {
        //console.log("检测到单个Ping结果统计");
        // 提取URL
        const urlMatch = pingLine.match(/Ping Statistics for ([^\s:]+):/);
        if (urlMatch && urlMatch[1]) {
            const url = urlMatch[1];
            // 提取关键数据
            const lostMatch = pingLine.match(/Lost\s*=\s*\d+\s*\((\d+\.?\d*)%\s*lost\)/i);
            const timeMatch = pingLine.match(/Average\s*=\s*(\d+)ms/i);
            // 判断是否成功（0%丢包视为成功）
            const lostPercentage = lostMatch ? parseFloat(lostMatch[1]) : 100;
            const isSuccess = lostPercentage<50;
            result.pingTests.push({
                url: url,
                status: isSuccess ? jQuery.i18n.prop("lPingSuccess") : jQuery.i18n.prop("lPingFailure"),
                time: timeMatch ? timeMatch[1] + 'ms' : '',
                details: pingLine.trim()
            });
            //console.log("解析单个Ping统计结果:", url, isSuccess ? '成功' : '失败');
            foundMatch = true;
        }
    }

    // 2. 如果不是多结果组合，检查是否匹配失败模式
    if (!foundMatch) {
        const failPatterns = [
            /ping\s+([^\s.]+\.[^\s]+).*\bfail\b/i,
            /ping\s+([^\s.]+\.[^\s]+).*\bfailed\b/i,
            /ping\s+([^\s.]+\.[^\s]+).*\btimeout\b/i
        ];
        for (const pattern of failPatterns) {
            const match = pingLine.match(pattern);
            if (match && match[1]) {
                result.pingTests.push({
                    url: match[1],
                    status: jQuery.i18n.prop("lPingFailure"),
                    details: pingLine.replace(/ping\s+([^\s.]+\.[^\s]+)/, '').trim()
                });
                foundMatch = true;
                break;
            }
        }
    }

    // 3. 如果不是失败模式，检查是否匹配成功模式
    if (!foundMatch) {
        const successPatterns = [
            /ping\s+([^\s.]+\.[^\s]+).*成功/i,
            /ping\s+([^\s.]+\.[^\s]+).*\bsuccess\b/i,
            /ping\s+([^\s.]+\.[^\s]+).*\bms\b/i,
            /ping\s+([^\s.]+\.[^\s]+).*\bStatistics\b.*Lost\s*=\s*(\d+).*\b0%\b.*\blost\b/i,
            /ping\s+([^\s.]+\.[^\s]+).*\bStatistics\b.*Lost\s*=\s*(\d+).*\b0\.0%\b.*\blost\b/i,
            /ping\s+([^\s.]+\.[^\s]+).*\bSend\s*=\s*\d+.*\bReceived\s*=\s*\d+.*\bLost\s*=\s*0\b/i
        ];
        for (const pattern of successPatterns) {
            const match = pingLine.match(pattern);
            if (match && match[1]) {
                const timeMatch = pingLine.match(/([0-9]+)\s*ms/i) || pingLine.match(/Average\s*=\s*(\d+)ms/i);
                const pingTime = timeMatch ? (timeMatch[1] ? timeMatch[1] + 'ms' : '') : '';
                let lossDetail = '';
                const lossMatch = pingLine.match(/Lost\s*=\s*\d+.*?\((\d+\.?\d*)%\s*lost\)/i);
                if (lossMatch) {
                    lossDetail = lossMatch[1] + '%丢包';
                }
                result.pingTests.push({
                    url: match[1],
                    status: jQuery.i18n.prop("lPingSuccess"),
                    time: pingTime,
                    details: lossDetail || pingLine.replace(/ping\s+([^\s.]+\.[^\s]+)/, '').trim()
                });
                foundMatch = true;
                break;
            }
        }
    }

    // 4. 如果没有匹配任何模式，但确定是ping命令，使用基本提取
    if (!foundMatch) {
        const urlMatch = pingLine.match(/ping\s+([^\s.]+\.[^\s]+)/);
        if (urlMatch && urlMatch[1]) {
            let isSuccess = pingLine.indexOf('成功') > -1 ||
                            pingLine.indexOf('success') > -1 ||
                            (pingLine.indexOf('ms') > -1 && pingLine.indexOf('fail') === -1 && pingLine.indexOf('failed') === -1) ||
                            (pingLine.indexOf('Statistics') > -1 && (pingLine.indexOf('0%') > -1 || pingLine.indexOf('0.0%') > -1) && pingLine.indexOf('lost') > -1) ||
                            (pingLine.indexOf('Send = ') > -1 && pingLine.indexOf('Lost = 0') > -1);
            let details = pingLine.replace(/ping\s+([^\s.]+\.[^\s]+)/, '').trim();
            const statsMatch = pingLine.match(/Send\s*=\s*\d+.*?Received\s*=\s*\d+.*?Lost\s*=\s*\d+.*?\((\d+\.?\d*)%.*?lost\)/i);
            if (statsMatch) {
                details = statsMatch[0];
                const lossPercentage = parseFloat(statsMatch[1]);
                if (lossPercentage<50) {
                    isSuccess = true;
                }
            }
            result.pingTests.push({
                url: urlMatch[1],
                status: isSuccess ? jQuery.i18n.prop("lPingSuccess") : jQuery.i18n.prop("lPingFailure"),
                details: details
            });
             foundMatch = true; // 标记已处理
        }
    }

    // 5. 增加特殊处理：直接检查是否包含成功的ping结果特征 (确保这是最后尝试的，并且之前未匹配)
    if (!foundMatch && !result.pingTests.length && pingLine.indexOf('Ping Statistics for') > -1) { // 检查是否是统计行且未被处理
        if ((pingLine.indexOf('Lost = 0') > -1 ||
             (pingLine.indexOf('0%') > -1 && pingLine.indexOf('lost') > -1) ||
             (pingLine.indexOf('0.0%') > -1 && pingLine.indexOf('lost') > -1)) &&
            pingLine.indexOf('Send = ') > -1) {
            const hostMatch = pingLine.match(/for\s+([^\s:]+):/);
            const host = hostMatch ? hostMatch[1] : 'unknown_host_in_stats';
            const avgTimeMatch = pingLine.match(/Average\s*=\s*(\d+)ms/i);
            const avgTime = avgTimeMatch ? avgTimeMatch[1] + 'ms' : '';
            result.pingTests.push({
                url: host,
                status: jQuery.i18n.prop("lPingSuccess"),
                time: avgTime,
                details: pingLine.trim()
            });
            //console.log("通过特征识别成功的ping测试 (来自统计行):", host);
        }
    }
}

/**
 * 触发系统信息获取
 * 这是一个简单的包装函数，用于处理按钮点击事件
 */
function triggerSysInfo() {
    sm("PleaseWait", 150, 100);
    $("#lPleaseWait").text("正在进行系统诊断，请稍候...");
    
    if (typeof PrintSysInfo === 'function') {
        PrintSysInfo().then(function() {
            hm();
        }).catch(function(error) {
            hm();
            showAlert("系统诊断失败: " + error);
        });
    } else {
        hm();
        showAlert("PrintSysInfo函数未定义");
    }
}

// 在文件的末尾添加全局函数
/**
 * 翻译电池相关标签
 * 这个函数可以在任何需要刷新电池标签翻译的地方调用
 */
function translateBatteryLabels() {
    try {
        // 定义需要翻译的电池相关标签
        var batteryLabels = {
            'batteryLevelLabel': 'batteryLevel',
            'chargingStatusLabel': 'chargingStatus',
            'batteryVoltageLabel': 'batteryVoltage'
        };
        
        // 遍历标签ID，应用翻译
        for (var labelId in batteryLabels) {
            var element = document.getElementById(labelId);
            if (element) {
                var translationKey = batteryLabels[labelId];
                var translatedText = jQuery.i18n.prop(translationKey);
                
                // 如果获取到了有效的翻译，则应用
                if (translatedText && translatedText !== translationKey) {
                    element.textContent = translatedText;
                }
            }
        }
        
        // 同时处理可能的移动端电池相关标签
        var mobileLabels = document.querySelectorAll('.battery-info strong, .charging-info strong');
        for (var i = 0; i < mobileLabels.length; i++) {
            var label = mobileLabels[i];
            var id = label.id;
            
            if (id && batteryLabels[id]) {
                var key = batteryLabels[id];
                var text = jQuery.i18n.prop(key);
                
                if (text && text !== key) {
                    label.textContent = text;
                }
            }
        }
        
        // 更新充电状态文本
        var chargingStatus = document.getElementById('chargingStatus');
        if (chargingStatus) {
            var isCharging = chargingStatus.textContent.indexOf('充电中') > -1;
            chargingStatus.textContent = isCharging ? jQuery.i18n.prop('lCharging') : jQuery.i18n.prop('lUncharged');
        }
    } catch (error) {
        //console.error("翻译电池标签时出错:", error);
    }
}

// 在DOMContentLoaded事件中调用
document.addEventListener('DOMContentLoaded', function() {
    // 延迟调用，确保DOM和i18n已加载
    setTimeout(translateBatteryLabels, 1000);
    
    // 同时翻译Ping测试相关标签
    setTimeout(localizePingResultsLabels, 1000);
    
    // 设置定时器定期刷新翻译，以防动态更新丢失翻译
    setInterval(translateBatteryLabels, 5000);
    setInterval(localizePingResultsLabels, 5000);
});

/**
 * 新增函数，专门处理Ping测试相关标签的翻译
 * @param {Object} info - 解析后的系统信息
 */
function localizePingResultsLabels(info) {
    try {
        // 通过全局对象g_objContent调用对象方法
        if (typeof g_objContent !== 'undefined' && g_objContent && typeof g_objContent.localizePingInfoLabels === 'function') {
            g_objContent.localizePingInfoLabels();
            return;
        }
        
        // 如果全局对象不可用，直接执行标签翻译
        // 定义需要翻译的Ping测试相关标签
        var pingLabels = [
            'pingResultsTitle',
            'pingWaitingDataLabel',
            'signalStrengthLabel',
            'connectionStatusLabel',
            'networkTypeLabel',
            'operatorLabel',
            'onlineTimeLabel',
            'softwareVersionLabel',
            'phyCellIdLabel',
            'bandLabel',
            // device-info部分的标签
            'pIPv4',
            'pIPv6',
            'pDNS',
            'pConnTime',
            'pSignalInfo'
        ];
        
        // 遍历标签ID，应用翻译
        for (var i = 0; i < pingLabels.length; i++) {
            var labelId = pingLabels[i];
            var labelElement = document.getElementById(labelId);
            
            if (labelElement) {
                var translatedText = jQuery.i18n.prop(labelId);
                
                // 如果获取到了有效的翻译，则应用
                if (translatedText && translatedText !== labelId) {
                    labelElement.textContent = translatedText;
                }
            }
        }
    } catch (error) {
        //console.error("Ping测试标签本地化出错:", error);
    }
}
