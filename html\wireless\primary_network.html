<div class="content">
  <div class="form-section">
    <div class="form-group">
      <a href='#' class='help' onclick="getHelp('WirelessSecuritySettings')" id="primaryNetworkHelp">&nbsp;</a>
      <label id="title" class="title"></label>
      <label id="lNetwork" style="display: none">Network:</label>
      <div id="nwRadio" style="display: none" ></div>
      <label id="lSSID"> </label>
      <input name="" type="text" maxlength="32" id="tbSSID" class="textfield" onkeyup="checkSsidLength(32)" />
      <label id="lssidError" class="lable13" style="color:red;display:none"></label>
      <label id="lNwVisiStatus" ></label>
      <div id="nwRadiovisi" class="inlineDiv"></div>
      <label id="lWireSecurity"></label>
      <select id="drpdwnSecurityType" onchange="changedSecuritySetting()">
        <option id='dropdownNone' value="None"> Disabled </option>
        <option id='dropdownWPA2' value="WPA2-PSK"> WPA2-PSK  </option>
        <option id='dropdownWPAWPA2' value="Mixed"> WPA-WPA2 Mixed </option>
        <!--<option id='dropdownWPA' value="WPA-PSK">WPA-PSK  </option>-->
        <option id='dropdownWEP' value="WEP"> WEP  </option>
        <option id='dropdownWAPI' value="WAPI-PSK">WAPI-PSK  </option>
      </select>
      <div id="divWpsCfgDlg">
        <input type="button" value="wps" id="lWpsCfgBtn" class="button" onclick="ShowWPSCfgDlg()"/>
      </div>
      <label id="lAuth" style="display: none">Authentication:</label>
      <select id="drpdwnAuthType" style="display: none">
        <option id='dropdownShare' value="Shared">Shared</option>
        <option id='dropdownOpen' value="Open">Open</option>
      </select>
      <label id="lEncryption" style="display: none">Encryption:</label>
      <select id="drpdwnEncryType" style="display: none">
        <option id='dropdown64bASCII' value="0">64 bit - 5 ASCII Characters</option>
        <option id='dropdown64bHEX' value="1">64 bit - 10 Hex Characters</option>
        <option id='dropdown128bASCII' value="2">128 bit - 13 ASCII Hex Characters</option>
        <option id='dropdown128bHEX' value="3">128 bit - 26 Hex Characters</option>
      </select>
      <div  id='div_wapi'>
        <label id="lKeyType" ></label>
        <div id="RadioAH" class="inlineDiv"></div>
      </div>
      <label id="lpass"></label>
      <input  type="password" class="textfield" id="tbpass" onchange='securityPasswordChanged()' onkeypress='securityPasswordChanged()'/>
      <input  type="text" class="textfield" id="tbpassText" style="display: none" onchange='securityPasswordChanged()' onkeypress='securityPasswordChanged()'/>
      <br class="clear" />
      <label id='lRetypePassword'  style='display: none'>Re-enter Password :</label>
      <input type='password' id='tbre_password' maxlength="26" style='display: none'  class='textfield' />
      <input type='text' id='tbre_passwordtext' maxlength="26" style='display: none'  class='textfield' />
      <input  type="checkbox" id="chkUnmask" class="chk11" onchange='unmaskPassword()'  onclick='unmaskPassword()'/>
      <label id="lunmaskpass" class="lable11 subttl">Unmask Password</label>
      <br class="clear" />
      <br/>
      <label id="lwpa" style="text-align: left !important;"></label>
      <div id="divCipher">
        <select id="drpdwnCipher" onchange="changedCiperSetting()">
        </select>
      </div>     
      <div align="center" class="formBox" id="divPrimaryNetworkFormBox">
        <label class='error' id='lPassErrorMesPN'  style='display: none'></label>
      </div>
      <div align='right' id='divSavePrimaryNetworkData'><span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='validateSecurityPassword()'  /></span> </div>
      <div id="MBAddWPSClient" style="display: none" >
        <div class="popUpBox popUpBox2"  >
          <h1 id="h1AddWPSClient">Add WPS Client</h1>
          <a href="#" class="close" onclick="WPSClientClose()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />
          <div style="padding:5px 15px;">
            <strong> <label id="lWPStext">Choose a method to associate WPS certified Client</label> </strong>
            <input id="btnPush" type="button" class="button" value="Push" onclick="btnPushClicked()" style="float:right" /> <span  id="spanWPSPushButton" >WPS Push Button</span>
            <br /><br /><br />
            <input id="btnRegister" type="button" class="button" value="Register" onclick="btnRegisterClicked()" style="float:right; margin-top:5px;" /> <span id="spanEnterPin"> Enter PIN</span><br />
            <input type="text" maxlength="10" id='txtRouterPinMB' onclick="RouterPinMBChange()" onchange="RouterPinMBChange()" style="height:16px; margin-top:3px; width:120px;" />
            <br />
            <br />
            <div id="divcancel_session" style="display: none; float:right;">
              <a href="#" id="cancel_session" onclick="cancelSessionClicked()"><strong id="lCancelWpsSession">cancel session</strong></a>
            </div>
            <label id="lWPSStatus" style="color:#f00;"></label>
            <!-- <img src="images/ajax-loader.gif" alt="" style="margin:5px 0 -4px 5px;" id="imageAjaxLoader"/>--><br /><br />
            <label id="lWPSError" class="lable13" style="display: none"></label>
            <div align="right">
              <input id="btnClose" type="button" class="button" value="Close" onclick="WPSClientClose()" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


