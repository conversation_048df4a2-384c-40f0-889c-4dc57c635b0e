(function ($) {
    $.fn.objCardManagement = function (InIt) {
        function updateCardDisplay(isAutoMode) {
            var currentCard = document.getElementById('currentCard');
            var autoSelectStatus = document.getElementById('autoSelectStatus');
            var cardSelect = document.getElementById('cardSelect');
            
            if (currentCard && autoSelectStatus) {
                if (isAutoMode) {
                    currentCard.textContent = getLocalizedText('autoSelectMode');
                    autoSelectStatus.textContent = getLocalizedText('currentStatus') + getLocalizedText('statusEnabled');
                } else if (cardSelect) {
                    currentCard.textContent = getLocalizedText('cardNumber') + (parseInt(cardSelect.value) + 1);
                    autoSelectStatus.textContent = getLocalizedText('currentStatus') + getLocalizedText('statusDisabled');
                }
            }
        }
        
        // 初始化主函数
        window.initCardManagement = function() {
            // 国际化已在onLoad中执行，此处无需重复执行
            // localizeCardManagement();
            
            var autoSelectCard = document.getElementById('autoSelectCard');
            if (autoSelectCard) {
                autoSelectCard.addEventListener('change', function() {
                    var autoSelectStatus = document.getElementById('autoSelectStatus');
                    if (autoSelectStatus) {
                        autoSelectStatus.textContent = getLocalizedText('currentStatus') + (this.checked ? getLocalizedText('statusEnabled') : getLocalizedText('statusDisabled'));
                    }
                    updateCardDisplay(this.checked);
                    saveAutoSwitchStatus(this.checked);
                });
            }
            
            var cardSelect = document.getElementById('cardSelect');
            if (cardSelect) {
                cardSelect.addEventListener('change', function() {
                    updateCardDisplay(false);
                    saveCardSelection(this.value);
                });
            }
            
            initializeCardSelection();
            
            // 添加调试信息
            //console.log("i18n资源检查:");
            //console.log("cardManagementTitle = " + getLocalizedText('cardManagementTitle'));
            //console.log("autoSelectCardLabel = " + getLocalizedText('autoSelectCardLabel'));
            //console.log("selectCardLabel = " + getLocalizedText('selectCardLabel'));
            //console.log("currentStatus = " + getLocalizedText('currentStatus'));
            //console.log("statusEnabled = " + getLocalizedText('statusEnabled'));
            //console.log("cmccCardOption = " + getLocalizedText('cmccCardOption'));
        };
        
        // 本地化页面文本
        function localizeCardManagement() {
            try {
                //console.log("开始本地化卡片管理页面...");
                
                // 获取当前语言
                var locale = getCookie("locale") || "cn";
                var isEnglish = locale === "en";
                
                // 辅助函数：获取本地化文本，优先从HTML元素获取，备用使用i18n
                window.getLocalizedText = function(key) {
                    // 先尝试从HTML元素获取
                    var element = document.getElementById(key + 'Text');
                    if (element && element.textContent) {
                        return element.textContent;
                    }
                    
                    // 尝试从i18n获取
                    var text = jQuery.i18n.prop(key);
                    if (text && text !== key) {
                        return text;
                    }
                    
                    // 英文模式下使用内置的英文文本
                    if (isEnglish) {
                        var englishDefaults = {
                            'cardManagementTitle': 'Card Management',
                            'autoSelectCardLabel': 'Auto Switch Card When No Network',
                            'selectCardLabel': 'Select Card',
                            'currentStatus': 'Current Status: ',
                            'statusEnabled': 'Enabled',
                            'statusDisabled': 'Disabled',
                            'cmccCardOption': 'China Mobile-----CMCC',
                            'cuccCardOption': 'China Unicom-----CUCC',
                            'ctccCardOption': 'China Telecom-----CTCC',
                            'autoSelectMode': 'Auto Select',
                            'cardNumber': 'Card ',
                            'currentCardLabel': 'Current Card:'
                        };
                        return englishDefaults[key] || key;
                    }
                    
                    // 中文模式下使用元素的默认内容
                    return getDefaultChineseText(key) || key;
                }
                
                // 获取元素默认的中文文本
                function getDefaultChineseText(key) {
                    var defaultTexts = {
                        'cardManagementTitle': '卡片管理',
                        'autoSelectCardLabel': '无网络时自动切卡',
                        'selectCardLabel': '选择卡片',
                        'currentStatus': '当前状态：',
                        'statusEnabled': '开启',
                        'statusDisabled': '关闭',
                        'cmccCardOption': '中国移动通信-----CMCC',
                        'cuccCardOption': '中国联通通讯-----CUCC',
                        'ctccCardOption': '中国电信-----CTCC',
                        'autoSelectMode': '自动选择',
                        'cardNumber': '卡片',
                        'currentCardLabel': '当前使用卡片：'
                    };
                    
                    // 优先尝试从元素获取默认内容
                    var element = document.getElementById(key);
                    if (element && element.textContent && !isEnglish) {
                        return element.textContent;
                    }
                    
                    return defaultTexts[key];
                }
                
                // 设置页面标题
                document.title = getLocalizedText('cardManagementTitle');
                
                // 本地化标题
                var titleElement = document.getElementById('title');
                if (titleElement) {
                    titleElement.textContent = getLocalizedText('cardManagementTitle');
                    //console.log("标题已本地化");
                } else {
                    //console.warn("未找到title元素");
                }
                
                // 本地化标签
                var autoSelectLabel = document.getElementById('autoSelectCardLabel');
                if (autoSelectLabel) {
                    autoSelectLabel.textContent = getLocalizedText('autoSelectCardLabel');
                    //console.log("自动切卡标签已本地化");
                } else {
                    //console.warn("未找到autoSelectCardLabel元素");
                }
                
                var selectCardLabel = document.getElementById('selectCardLabel');
                if (selectCardLabel) {
                    selectCardLabel.textContent = getLocalizedText('selectCardLabel');
                    //console.log("选择卡片标签已本地化");
                } else {
                    //console.warn("未找到selectCardLabel元素");
                }
                
                // 本地化状态文本
                var autoSelectStatus = document.getElementById('autoSelectStatus');
                if (autoSelectStatus) {
                    var isChecked = document.getElementById('autoSelectCard') && document.getElementById('autoSelectCard').checked;
                    autoSelectStatus.textContent = getLocalizedText('currentStatus') + (isChecked ? getLocalizedText('statusEnabled') : getLocalizedText('statusDisabled'));
                    //console.log("状态文本已本地化");
                } else {
                    //console.warn("未找到autoSelectStatus元素");
                }
                
                // 本地化卡片选项
                var cmccOption = document.getElementById('cmccCardOption');
                var cuccOption = document.getElementById('cuccCardOption');
                var ctccOption = document.getElementById('ctccCardOption');
                
                if (cmccOption) cmccOption.textContent = getLocalizedText('cmccCardOption');
                if (cuccOption) cuccOption.textContent = getLocalizedText('cuccCardOption');
                if (ctccOption) ctccOption.textContent = getLocalizedText('ctccCardOption');
                //console.log("卡片选项已本地化");
                
                // 本地化当前卡片标签
                var currentCardLabel = document.getElementById('currentCardLabel');
                if (currentCardLabel) {
                    currentCardLabel.textContent = getLocalizedText('currentCardLabel');
                }
                
                //console.log("卡片管理页面本地化完成");
            } catch (e) {
                //console.error("本地化过程中出错:", e);
            }
        }
        
        // 初始化卡片选择
        window.initializeCardSelection = function() {
            var xml = getData("status1");
            var checkSim = "1";
            var atSim = "0";
            
            if(xml) {
                $(xml).find("wan").each(function() {
                    checkSim = $(this).find("check_sim").text() || "1";
                    atSim = $(this).find("at_sim").text() || "0";
                });
            }
            
            var autoSelectCard = document.getElementById('autoSelectCard');
            var autoSelectStatus = document.getElementById('autoSelectStatus');
            if (autoSelectCard) {
                autoSelectCard.checked = checkSim === "1";
                if (autoSelectStatus) {
                    autoSelectStatus.textContent = getLocalizedText('currentStatus') + (checkSim === "1" ? getLocalizedText('statusEnabled') : getLocalizedText('statusDisabled'));
                }
            }
            
            var cardSelect = document.getElementById('cardSelect');
            if (cardSelect) {
                cardSelect.value = atSim;
                cardSelect.style.display = 'block';  // 始终显示下拉框
                updateCardDisplay(checkSim === "1");
            }
        };
        
        // 保存卡片选择
        window.saveCardSelection = function(cardValue) {
            var mapData = new Array(0);
            mapData = putMapElement(mapData, "RGW/wan/master_sim", cardValue, 0);
            postXML("status1", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
        };
        
        // 保存自动选择状态
        window.saveAutoSwitchStatus = function(isAutoEnabled) {
            var xml = getData("status1");
            var checkSim = "1";
            var atSim = "0";
            
            if(xml) {
                $(xml).find("wan").each(function() {
                    checkSim = $(this).find("check_sim").text() || "1";
                    atSim = $(this).find("at_sim").text() || "0";
                });
            }
            
            var mapData = new Array(0);
            putMapElement(mapData, "RGW/wan/check_sim", isAutoEnabled ? "1" : "0", 0);
            putMapElement(mapData, "RGW/wan/master_sim", atSim, 1);
            postXML("status1", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
        };
        
        this.onLoad = function (isReload) {
            try {
                //console.log("开始加载卡片管理页面...");
                this.loadHTML();
                
                // 简化加载流程，不再依赖i18n资源加载
                setTimeout(function() {
                    //console.log("执行卡片管理页面初始化...");
                    
                    // 先执行本地化
                    try {
                        if (typeof localizeCardManagement === 'function') {
                            localizeCardManagement();
                        } else {
                            //console.error("localizeCardManagement 函数不可用");
                        }
                    } catch (e) {
                        //console.error("本地化执行出错:", e);
                    }
                    
                    // 然后初始化卡片管理
                    if (typeof window.initCardManagement === 'function') {
                        window.initCardManagement();
                    } else {
                        //console.error("initCardManagement 函数不可用");
                    }
                }, 300);
            } catch (e) {
                //console.error("卡片管理页面加载出错:", e);
            }
        };
        
        this.setXMLName = function () {};

        this.loadHTML = function() {
            var contentElement = document.getElementById('Content');
            if (contentElement) {
                contentElement.innerHTML = "";
                contentElement.innerHTML = callProductHTML("html/wireless/card_management.html");
            }
        };
        
        this.onPostSuccess = function() {
            this.onLoad(true);
        };
        
        return this.each(function () {});
    };
})(jQuery);
