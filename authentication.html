<!DOCTYPE HTML>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ZY 设备认证</title>
        <link href="css/stylesheet.css" rel="stylesheet" type="text/css" />
        <link href="css/blue-white-theme.css" rel="stylesheet" type="text/css" />
        <script type="text/javascript" src="js/jquery/jquery.js" language="javascript"></script>
        <script type="text/javascript" src="js/jquery/jquery.i18n.properties-1.0.4.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/jquery.form.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/download.jQuery.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/md5.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/table.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/modaldbox.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/utils.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/xml_helper.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/layout_manager.js" language="javascript"></script>
        <!-- Add required scripts for ajax calls -->
        <script type="text/javascript">
            var g_platformName = "mifi";      
            if (typeof jQuery == 'undefined') { 
                window.location.reload(true);
            }   
        </script>
        <script type="text/javascript" src="js/base/ajax_calls.js" language="javascript"></script>
        <style type="text/css">
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
                background-color: #f5f7fa;
            }
            .auth-container {
                width: 100%;
                max-width: 1000px;
                margin: 40px auto;
                padding: 20px;
            }
            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                background-color: #fff;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .title {
                font-size: 24px;
                color: #1976d2;
                margin: 0;
            }
            .back-button {
                background-color: #1976d2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
            }
            .back-button:hover {
                background-color: #0d47a1;
            }
            .device-card {
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                padding: 20px;
                margin-bottom: 20px;
            }
            .device-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }
            .device-name {
                font-size: 18px;
                font-weight: 500;
                color: #333;
            }
            .device-status {
                font-size: 14px;
                padding: 5px 10px;
                border-radius: 20px;
                font-weight: 500;
            }
            .status-connected {
                background-color: #e8f5e9;
                color: #2e7d32;
            }
            .status-blocked {
                background-color: #ffebee;
                color: #c62828;
            }
            .device-details {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 15px; /* Space for the button */
            }
            .detail-item {
                margin-bottom: 10px;
            }
            .detail-label {
                font-size: 13px;
                color: #666;
                margin-bottom: 5px;
            }
            .detail-value {
                font-size: 15px;
                color: #333;
                font-weight: 500;
            }
            .auth-button {
                background-color: #4CAF50; /* Green */
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
                margin-top: 10px;
            }
            .auth-button:hover {
                background-color: #45a049;
            }
            .summary-card {
                background-color: #e3f2fd;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
            }
            .summary-icon {
                font-size: 24px;
                margin-right: 15px;
                color: #1976d2;
            }
            .summary-text {
                font-size: 16px;
                color: #333;
            }
            .summary-count {
                font-weight: bold;
                color: #1976d2;
            }
            
            /* Modal Styles */
            .modal {
                display: none; 
                position: fixed; 
                z-index: 1000; 
                left: 0;
                top: 0;
                width: 100%; 
                height: 100%; 
                overflow: auto; 
                background-color: rgba(0,0,0,0.5); 
            }
            .modal-content {
                background-color: #fefefe;
                margin: 15% auto; 
                padding: 25px;
                border: 1px solid #888;
                border-radius: 8px;
                width: 80%; 
                max-width: 450px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            }
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
                margin-bottom: 20px;
            }
            .modal-title {
                font-size: 20px;
                color: #1976d2;
                margin: 0;
            }
            .close-button {
                color: #aaa;
                font-size: 28px;
                font-weight: bold;
                cursor: pointer;
            }
            .close-button:hover,
            .close-button:focus {
                color: black;
                text-decoration: none;
            }
            .modal-body .form-group {
                margin-bottom: 15px;
            }
            .modal-body label {
                display: block;
                margin-bottom: 5px;
                font-size: 14px;
                color: #333;
            }
            .modal-body input[type="text"],
            .modal-body input[type="tel"] {
                width: calc(100% - 20px);
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 15px;
            }
            .modal-footer {
                padding-top: 15px;
                border-top: 1px solid #eee;
                margin-top: 20px;
                text-align: right;
            }
            .modal-button {
                padding: 10px 18px;
                border-radius: 4px;
                border: none;
                font-size: 15px;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            .submit-button {
                background-color: #1976d2;
                color: white;
                margin-left: 10px;
            }
            .submit-button:hover {
                background-color: #0d47a1;
            }
            .cancel-button {
                background-color: #f0f0f0;
                color: #333;
            }
            .cancel-button:hover {
                background-color: #e0e0e0;
            }

            /* Resend Button Styles */
            .verification-input-group {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .verification-input-group input {
                flex: 1;
            }
            .resend-button {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 15px;
                cursor: pointer;
                font-size: 14px;
                white-space: nowrap;
                transition: background-color 0.3s;
            }
            .resend-button:hover {
                background-color: #f57c00;
            }
            .resend-button:disabled {
                background-color: #ccc;
                cursor: not-allowed;
            }
            .resend-countdown {
                color: #666;
                font-size: 12px;
            }

            /* Auth Status Styles */
            .auth-status-button {
                margin-left: auto;
                background-color: #1976d2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.3s;
            }

            .auth-status-button:hover {
                background-color: #0d47a1;
            }

            .auth-status-list {
                max-height: 400px;
                overflow-y: auto;
            }

            .auth-status-item {
                padding: 10px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .auth-status-item:last-child {
                border-bottom: none;
            }

            .auth-mac {
                font-weight: bold;
                color: #1976d2;
            }

            .auth-time {
                color: #666;
                font-size: 0.9em;
            }

            @media (max-width: 768px) {
                .auth-container {
                    padding: 10px;
                    margin: 20px auto;
                }
                .device-details {
                    grid-template-columns: 1fr;
                }
                .modal-content {
                    width: 75%;
                    margin: 20% auto;
                }
            }
        </style>
        <script type="text/javascript">
            // 解析XML并显示设备信息
            function parseAndDisplayDeviceData(xmlData) {
                try {
                    // 解析XML字符串
                    var parser = new DOMParser();
                    var xmlDoc = parser.parseFromString(xmlData, "text/xml");
                    
                    // 获取设备列表
                    var devices = xmlDoc.getElementsByTagName("Item");
                    var deviceContainer = document.getElementById("deviceContainer");
                    deviceContainer.innerHTML = ""; // 清空容器
                    
                    // 获取连接设备数量
                    var connectedDevCount = "0";
                    var connectedDevElement = xmlDoc.getElementsByTagName("nr_connected_dev")[0];
                    if (connectedDevElement && connectedDevElement.textContent) {
                        connectedDevCount = connectedDevElement.textContent;
                    } else {
                        // 如果找不到元素，则计算连接设备的数量
                        var connectedCount = 0;
                        for (var i = 0; i < devices.length; i++) {
                            var connected = devices[i].getElementsByTagName("connected")[0];
                            if (connected && connected.textContent === "1") {
                                connectedCount++;
                            }
                        }
                        connectedDevCount = connectedCount.toString();
                    }
                    document.getElementById("connectedCount").textContent = connectedDevCount;
                    
                    // 遍历每个设备并创建设备卡片
                    for (var i = 0; i < devices.length; i++) {
                        var device = devices[i];
                        
                        // 安全地获取元素文本
                        function getElementText(parent, tagName) {
                            var element = parent.getElementsByTagName(tagName)[0];
                            return element ? element.textContent : "";
                        }
                        
                        var mac = getElementText(device, "mac");
                        var name = getElementText(device, "name");
                        var blocked = getElementText(device, "blocked") === "1";
                        var connType = getElementText(device, "conn_type");
                        var ipAddress = getElementText(device, "ip_address");
                        var connected = getElementText(device, "connected") === "1";
                        var connTime = getElementText(device, "conn_time");
                        
                        // 创建设备卡片
                        var deviceCard = document.createElement("div");
                        deviceCard.className = "device-card";
                        
                        // 设备标题和状态
                        var statusClass = connected ? "status-connected" : "status-blocked";
                        var statusText = connected ? "已连接" : "已断开";
                        if (blocked === true) {
                            statusClass = "status-blocked";
                            statusText = "已阻止";
                        }
                        
                        deviceCard.innerHTML = `
                            <div class="device-header">
                                <div class="device-name">${name || "未知设备"}</div>
                            </div>
                            <div class="device-details">
                                <div class="detail-item">
                                    <div class="detail-label">MAC地址</div>
                                    <div class="detail-value">${mac || "未知"}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">IP地址</div>
                                    <div class="detail-value">${ipAddress || "未知"}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">连接类型</div>
                                    <div class="detail-value">${connType || "未知"}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">连接时间</div>
                                    <div class="detail-value">${connTime || "未知"}</div>
                                </div>
                            </div>
                            <button class="auth-button" onclick="showAuthModal(\'${mac}\')">认证</button>
                        `;
                        
                        deviceContainer.appendChild(deviceCard);
                    }
                    
                    // 如果没有设备，显示提示信息
                    if (devices.length === 0) {
                        deviceContainer.innerHTML = 
                            `<div class="device-card">
                                <div class="device-header">
                                    <div class="device-name">无设备</div>
                                </div>
                                <div class="device-details">
                                    <div class="detail-item">
                                        <div class="detail-value">未找到任何设备</div>
                                    </div>
                                </div>
                            </div>`;
                    }
                    
                } catch (e) {
                    showErrorMessage("解析错误", "解析XML数据时出错: " + e.message);
                }
            }
            
            // 返回登录页面
            function goBack() {
                window.location.href = "index.html";
            }
            
            // 初始化页面
            function initPage() {
                try {
                    // 请求device_management XML数据
                    var xml = callProductXML("device_management_all");
                    
                    // 处理XML数据（可能是对象或字符串）
                    if (xml) {
                        // 尝试使用jQuery解析设备信息
                        if (window.jQuery) {
                            // 创建一个新的设备数据对象
                            var deviceData = {
                                devices: [],
                                connectedCount: 0
                            };
                            
                            // 尝试使用jQuery解析设备信息
                            var $xml = $(xml);
                            var $items = $xml.find('Item');
                            
                            
                            $items.each(function(index) {
                                var device = {
                                    mac: $(this).find('mac').text() || '',
                                    name: $(this).find('name').text() || '',
                                    name_type: $(this).find('name_type').text() || '0',
                                    blocked: $(this).find('blocked').text() || '0',
                                    conn_type: $(this).find('conn_type').text() || '',
                                    ip_address: $(this).find('ip_address').text() || '',
                                    connected: $(this).find('connected').text() || '0',
                                    conn_time: $(this).find('conn_time').text() || ''
                                };
                                deviceData.devices.push(device);
                                
                                if (device.connected === '1') {
                                    deviceData.connectedCount++;
                                }
                            });
                            
                            // 如果没有找到连接数，尝试从XML中获取
                            if (!deviceData.connectedCount) {
                                var connCount = $xml.find('nr_connected_dev').text();
                                if (connCount) {
                                    deviceData.connectedCount = parseInt(connCount, 10);
                                }
                            }
                            
                            parseAndDisplayDeviceData(new XMLSerializer().serializeToString(xml.documentElement));
                            
                            // 始终显示认证状态按钮
                            document.getElementById('showAuthStatusButton').style.display = 'inline-block';
                            
                            // 查询认证状态数据
                            queryAuthStatus();
                        } else {
                            // 如果jQuery不可用，尝试使用原生方法
                            var xmlString;
                            if (xml.documentElement) {
                                xmlString = new XMLSerializer().serializeToString(xml);
                            } else if (typeof xml === 'string') {
                                xmlString = xml;
                            }
                            
                            if (xmlString) {
                                parseAndDisplayDeviceData(xmlString);
                            } else {
                                throw new Error("无法提取有效的设备数据");
                            }
                        }
                    } else {
                        throw new Error("无法获取设备管理数据");
                    }
                } catch (e) {
                    showErrorMessage("初始化错误", "获取或处理设备数据时出错: " + e.message);
                }
            }
            
            // 显示设备数据（JSON格式）
            function displayDeviceData(data) {
                try {
                    var deviceContainer = document.getElementById("deviceContainer");
                    deviceContainer.innerHTML = ""; // 清空容器
                    
                    // 设置连接设备数量
                    document.getElementById("connectedCount").textContent = data.connectedCount || "0";
                    
                    // 如果没有设备，显示提示信息
                    if (!data.devices || data.devices.length === 0) {
                        deviceContainer.innerHTML = 
                            `<div class="device-card">
                                <div class="device-header">
                                    <div class="device-name">无设备</div>
                                </div>
                                <div class="device-details">
                                    <div class="detail-item">
                                        <div class="detail-value">未找到任何设备</div>
                                    </div>
                                </div>
                            </div>`;
                        return;
                    }
                    
                    // 遍历每个设备并创建设备卡片
                    data.devices.forEach(function(device) {
                        var deviceCard = document.createElement("div");
                        deviceCard.className = "device-card";
                        
                        // 设备标题和状态
                        var isConnected = device.connected === "1";
                        var isBlocked = device.blocked === "1";
                        var statusClass = isConnected ? "status-connected" : "status-blocked";
                        var statusText = isConnected ? "已连接" : "已断开";
                        
                        if (isBlocked) {
                            statusClass = "status-blocked";
                            statusText = "已阻止";
                        }
                        
                        deviceCard.innerHTML = `
                            <div class="device-header">
                                <div class="device-name">${device.name || "未知设备"}</div>
                            </div>
                            <div class="device-details">
                                <div class="detail-item">
                                    <div class="detail-label">MAC地址</div>
                                    <div class="detail-value">${device.mac || "未知"}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">IP地址</div>
                                    <div class="detail-value">${device.ip_address || "未知"}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">连接类型</div>
                                    <div class="detail-value">${device.conn_type || "未知"}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">连接时间</div>
                                    <div class="detail-value">${device.conn_time || "未知"}</div>
                                </div>
                            </div>
                            <button class="auth-button" onclick="showAuthModal(\'${device.mac}\')">认证</button>
                        `;
                        
                        deviceContainer.appendChild(deviceCard);
                    });
                } catch (e) {
                    showErrorMessage("显示错误", "显示设备数据时出错: " + e.message);
                }
            }
            
            // 显示错误信息
            function showErrorMessage(title, message, debugInfo) {
                var deviceContainer = document.getElementById("deviceContainer");
                var debugHtml = debugInfo ? 
                    `<div class="detail-item">
                        <div class="detail-label">调试信息</div>
                        <div class="detail-value" style="word-break: break-all; font-size: 12px;">${debugInfo}</div>
                    </div>` : '';
                
                deviceContainer.innerHTML = 
                    `<div class="device-card">
                        <div class="device-header">
                            <div class="device-name">${title}</div>
                        </div>
                        <div class="device-details">
                            <div class="detail-item">
                                <div class="detail-value">${message}</div>
                            </div>
                            ${debugHtml}
                        </div>
                    </div>`;
            }
            
            // 显示无数据信息
            function showNoDataMessage() {
                var deviceContainer = document.getElementById("deviceContainer");
                deviceContainer.innerHTML = 
                    `<div class="device-card">
                        <div class="device-header">
                            <div class="device-name">无数据</div>
                        </div>
                        <div class="device-details">
                            <div class="detail-item">
                                <div class="detail-value">未找到设备数据</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">可能的原因</div>
                                <div class="detail-value">
                                    1. 认证请求未成功获取数据<br>
                                    2. 浏览器存储功能受限<br>
                                    3. 数据传输过程中出错
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">建议操作</div>
                                <div class="detail-value">
                                    <button onclick="goBack()" style="background-color: #1976d2; color: white; border: none; border-radius: 4px; padding: 8px 15px; cursor: pointer;">返回重试</button>
                                </div>
                            </div>
                        </div>
                    </div>`;
            }

            // Modal functions
            // 全局变量用于倒计时
            var resendTimer = null;
            var resendCountdown = 0;
            
            function showAuthModal(macAddress) {
                document.getElementById('authDeviceMac').value = macAddress;
                document.getElementById('phoneNumber').value = ''; // Clear previous input
                document.getElementById('verificationCode').value = ''; // Clear previous input
                
                // Reset modal to initial state (phone input and get code button)
                document.getElementById('verificationCodeGroup').style.display = 'none';
                document.getElementById('getVerificationCodeButton').style.display = 'inline-block';
                document.getElementById('confirmAuthButton').style.display = 'none';
                document.getElementById('phoneNumber').disabled = false;
                
                // 清除倒计时
                if (resendTimer) {
                    clearInterval(resendTimer);
                    resendTimer = null;
                }

                document.getElementById('authModal').style.display = 'block';
            }

            function hideAuthModal() {
                document.getElementById('authModal').style.display = 'none';
                // 清除倒计时
                if (resendTimer) {
                    clearInterval(resendTimer);
                    resendTimer = null;
                }
            }

            function requestVerificationCode() {
                var macAddress = document.getElementById('authDeviceMac').value;
                var phoneNumber = document.getElementById('phoneNumber').value;

                if (!phoneNumber) {
                    alert("请输入手机号。");
                    return;
                }
                
                // Validate phone number format (basic example)
                var phoneRegex = /^1[3-9]\d{9}$/;
                if (!phoneRegex.test(phoneNumber)) {
                    alert("请输入有效的11位手机号码。");
                    return;
                }


                // Construct the XML data
                var xmlRequest = 
                    `<?xml version="1.0" encoding="US-ASCII"?>
                        <RGW>
                            <authentication>
                                <type>1</type>
                                <phone>${phoneNumber}</phone>
                                <mac>${macAddress}</mac>
                            </authentication>
                        </RGW>`;

                try {
                    // 显示loading
                    document.getElementById('PleaseWait').style.display = 'block';
                    // 使用原生XMLHttpRequest发送请求
                    var xhr = new XMLHttpRequest();
                    var host = window.location.protocol + "//" + window.location.host + "/";
                    var url = host + 'xml_action.cgi?method=set&module=duster&file=authentication';
                    
                    xhr.open("POST", url, true);
                    xhr.setRequestHeader("Content-Type", "text/xml;charset=UTF-8");
                    
                    // 设置回调函数
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            // 隐藏loading
                            document.getElementById('PleaseWait').style.display = 'none';
                            try {
                                if (xhr.responseXML) {
                                    var codeNode = xhr.responseXML.getElementsByTagName("code")[0];
                                    var msgNode = xhr.responseXML.getElementsByTagName("msg")[0];
                                    
                                    var code = codeNode ? codeNode.textContent : "";
                                    var msg = msgNode ? msgNode.textContent : "";
                                    
                                    if (msg && typeof UniDecode === 'function') {
                                        msg = UniDecode(msg);
                                    }
                                    
                                    if (code === "200") {
                                        // 成功时更新UI状态
                                        document.getElementById('phoneNumber').disabled = true;
                                        document.getElementById('verificationCodeGroup').style.display = 'block';
                                        document.getElementById('getVerificationCodeButton').style.display = 'none';
                                        document.getElementById('confirmAuthButton').style.display = 'inline-block';
                                        document.getElementById('verificationCode').focus();
                                        
                                        // 启动重新发送倒计时
                                        startResendCountdown();
                                    } else {
                                        // 失败时显示错误信息
                                        alert("获取验证码失败: " + (msg || "未知错误"));
                                    }
                                } else {
                                    alert("获取验证码失败: 响应数据格式错误");
                                }
                            } catch (e) {
                                alert("获取验证码失败: " + e.message);
                            }
                        }
                    };
                    
                    // 发送请求
                    xhr.send(xmlRequest);
                    
                } catch (e) {
                    alert("发送验证码请求失败: " + e.message);
                }
            }

            function submitDeviceAuth() {
                var macAddress = document.getElementById('authDeviceMac').value;
                var phoneNumber = document.getElementById('phoneNumber').value;
                var verificationCode = document.getElementById('verificationCode').value;

                if (!verificationCode) {
                    alert("请输入验证码。");
                    return;
                }
                
                // 验证码必须是6位数字
                if (!/^[0-9]{6}$/.test(verificationCode)) {
                    alert("验证码必须是6位数字。");
                    return;
                }

                
                // Construct the XML for final authentication
                var xmlAuthRequest = 
                    `<?xml version="1.0" encoding="US-ASCII"?>
<RGW>
    <authentication>
        <type>2</type>
        <phone>${phoneNumber}</phone>
        <mac>${macAddress}</mac>
        <code>${verificationCode}</code>
    </authentication>
</RGW>`;

                try {
                    // 显示loading
                    document.getElementById('PleaseWait').style.display = 'block';
                    // 使用原生XMLHttpRequest发送请求
                    var xhr = new XMLHttpRequest();
                    var host = window.location.protocol + "//" + window.location.host + "/";
                    var url = host + 'xml_action.cgi?method=set&module=duster&file=authentication';
                    
                    xhr.open("POST", url, true);
                    xhr.setRequestHeader("Content-Type", "text/xml;charset=UTF-8");
                    
                    // 设置回调函数
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            // 隐藏loading
                            document.getElementById('PleaseWait').style.display = 'none';
                            
                            try {
                                // 从响应文本中提取JSON字符串
                                if (xhr.responseXML) {
                                    var codeNode = xhr.responseXML.getElementsByTagName("code")[0];
                                    var msgNode = xhr.responseXML.getElementsByTagName("msg")[0];
                                    
                                    var code = codeNode ? codeNode.textContent : "";
                                    var msg = msgNode ? msgNode.textContent : "";
                                    
                                    // 使用UniDecode函数解码消息
                                    if (msg && typeof UniDecode === 'function') {
                                        msg = UniDecode(msg);
                                    }
                                    
                                    
                                    if (code === "200") {
                                        alert("认证成功！" + (msg ? " " + msg : ""));
                                        
                                        setTimeout(function() {
                                            try {
                                                var newXml = callProductXML("device_management_all");
                                                if (newXml) {
                                                    parseAndDisplayDeviceData(new XMLSerializer().serializeToString(newXml.documentElement));
                                                }
                                                queryAuthStatus();
                                            } catch (e) {
                                            }
                                        }, 1000);
                                    } else {
                                        alert("认证失败: " + (msg || "未知错误"));
                                    }
                                } else {
                                    alert("响应数据格式错误");
                                }
                            } catch (e) {
                                alert("处理响应数据时出错: " + e.message);
                            }
                            
                            hideAuthModal();
                        }
                    };
                    
                    // 发送请求
                    xhr.send(xmlAuthRequest);
                    
                } catch (e) {
                    alert("发送认证请求失败: " + e.message);
                }
            }

            // 查询认证状态数据
            function queryAuthStatus() {
                // 始终显示认证状态按钮
                document.getElementById('showAuthStatusButton').style.display = 'inline-block';
                
                try {
                    // 发送type=5的请求查询认证记录
                    var xmlRequest = 
                        `<?xml version="1.0" encoding="US-ASCII"?>
                        <RGW>
                            <authentication>
                                <type>5</type>
                            </authentication>
                        </RGW>`;
                    
                    var xhr = new XMLHttpRequest();
                    var host = window.location.protocol + "//" + window.location.host + "/";
                    var url = host + 'xml_action.cgi?method=set&module=duster&file=authentication';
                    
                    xhr.open("POST", url, true);
                    xhr.setRequestHeader("Content-Type", "text/xml;charset=UTF-8");
                    
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            processAuthStatusResponse(xhr.responseXML);
                        }
                    };
                    
                    xhr.send(xmlRequest);
                } catch (e) {
                    window.authStatusData = [];
                }
            }
            
            // 处理认证状态响应
            function processAuthStatusResponse(xmlData) {
                try {
                    if (!xmlData) {
                        window.authStatusData = [];
                        return;
                    }
                    
                    var successNodes = xmlData.getElementsByTagName("success");
                    
                    if (successNodes && successNodes.length > 0) {
                        var successContent = successNodes[0].textContent;
                        
                        if (successContent && successContent.trim()) {
                            // 解析格式: mac,time^mac,time
                            var records = successContent.split('^');
                            window.authStatusData = records.map(record => {
                                var parts = record.split(',');
                                return {
                                    mac: parts[0] ? parts[0].trim() : '',
                                    time: parts[1] ? parts[1].trim() : ''
                                };
                            }).filter(item => item.mac); // 过滤空数据
                        } else {
                            window.authStatusData = [];
                        }
                    } else {
                        window.authStatusData = [];
                    }
                } catch (e) {
                    window.authStatusData = [];
                }
            }

            // 显示认证状态 Modal
            function showAuthStatusModal() {
                // 显示等待框
                document.getElementById('PleaseWait').style.display = 'block';
                
                // 先查询最新的认证状态
                var xmlRequest = 
                    `<?xml version="1.0" encoding="US-ASCII"?>
                    <RGW>
                        <authentication>
                            <type>5</type>
                        </authentication>
                    </RGW>`;
                
                var xhr = new XMLHttpRequest();
                var host = window.location.protocol + "//" + window.location.host + "/";
                var url = host + 'xml_action.cgi?method=set&module=duster&file=authentication';
                
                xhr.open("POST", url, true);
                xhr.setRequestHeader("Content-Type", "text/xml;charset=UTF-8");
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        // 隐藏等待框
                        document.getElementById('PleaseWait').style.display = 'none';
                        processAuthStatusResponse(xhr.responseXML);
                        displayAuthStatusModal();
                    }
                };
                
                xhr.send(xmlRequest);
            }
            
            // 显示认证状态数据
            function displayAuthStatusModal() {
                var authStatusList = document.getElementById('authStatusList');
                authStatusList.innerHTML = ''; // 清空现有内容
                
                if (window.authStatusData && window.authStatusData.length > 0) {
                    window.authStatusData.forEach(status => {
                        var statusItem = document.createElement('div');
                        statusItem.className = 'auth-status-item';
                        statusItem.innerHTML = `
                            <span class="auth-mac">${status.mac}</span>
                            <span class="auth-time">${status.time}</span>
                        `;
                        authStatusList.appendChild(statusItem);
                    });
                } else {
                    authStatusList.innerHTML = '<div class="auth-status-item">暂无认证状态信息</div>';
                }
                
                document.getElementById('authStatusModal').style.display = 'block';
            }

            // 隐藏认证状态 Modal
            function hideAuthStatusModal() {
                document.getElementById('authStatusModal').style.display = 'none';
            }
            
            // 启动重新发送倒计时
            function startResendCountdown() {
                resendCountdown = 60;
                var resendButton = document.getElementById('resendCodeButton');
                var countdownSpan = document.getElementById('resendCountdown');
                
                resendButton.disabled = true;
                
                resendTimer = setInterval(function() {
                    countdownSpan.textContent = '(' + resendCountdown + 's)';
                    resendCountdown--;
                    
                    if (resendCountdown < 0) {
                        clearInterval(resendTimer);
                        resendTimer = null;
                        resendButton.disabled = false;
                        countdownSpan.textContent = '';
                    }
                }, 1000);
            }
            
            // 重新发送验证码
            function resendVerificationCode() {
                var macAddress = document.getElementById('authDeviceMac').value;
                var phoneNumber = document.getElementById('phoneNumber').value;
                
                if (!phoneNumber) {
                    alert("手机号不能为空。");
                    return;
                }
                
                // 构造XML请求
                var xmlRequest = 
                    `<?xml version="1.0" encoding="US-ASCII"?>
                        <RGW>
                            <authentication>
                                <type>1</type>
                                <phone>${phoneNumber}</phone>
                                <mac>${macAddress}</mac>
                            </authentication>
                        </RGW>`;
                
                try {
                    // 显示loading
                    document.getElementById('PleaseWait').style.display = 'block';
                    
                    var xhr = new XMLHttpRequest();
                    var host = window.location.protocol + "//" + window.location.host + "/";
                    var url = host + 'xml_action.cgi?method=set&module=duster&file=authentication';
                    
                    xhr.open("POST", url, true);
                    xhr.setRequestHeader("Content-Type", "text/xml;charset=UTF-8");
                    
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            document.getElementById('PleaseWait').style.display = 'none';
                            
                            try {
                                if (xhr.responseXML) {
                                    var codeNode = xhr.responseXML.getElementsByTagName("code")[0];
                                    var msgNode = xhr.responseXML.getElementsByTagName("msg")[0];
                                    
                                    var code = codeNode ? codeNode.textContent : "";
                                    var msg = msgNode ? msgNode.textContent : "";
                                    
                                    if (msg && typeof UniDecode === 'function') {
                                        msg = UniDecode(msg);
                                    }
                                    
                                    if (code === "200") {
                                        alert("验证码已重新发送！");
                                        // 清空验证码输入框
                                        document.getElementById('verificationCode').value = '';
                                        document.getElementById('verificationCode').focus();
                                        // 重新启动倒计时
                                        startResendCountdown();
                                    } else {
                                        alert("重新发送验证码失败: " + (msg || "未知错误"));
                                    }
                                } else {
                                    alert("重新发送验证码失败: 响应数据格式错误");
                                }
                            } catch (e) {
                                alert("重新发送验证码失败: " + e.message);
                            }
                        }
                    };
                    
                    xhr.send(xmlRequest);
                    
                } catch (e) {
                    alert("发送验证码请求失败: " + e.message);
                }
            }
        </script>
    </head>
    <body onload="initPage()">
        <div class="auth-container">
            <div class="header">
                <h1 class="title">设备认证</h1>
                <button class="back-button" onclick="goBack()">返回登录</button>
            </div>
            
            <div class="summary-card">
                <div class="summary-icon">📊</div>
                <div class="summary-text">当前已连接设备: <span id="connectedCount" class="summary-count">0</span></div>
                <button id="showAuthStatusButton" class="auth-status-button" style="display: none;" onclick="showAuthStatusModal()">查看认证状态</button>
            </div>
            
            <div id="deviceContainer">
                <!-- 设备信息将在这里动态生成 -->
                <div class="device-card">
                    <div class="device-header">
                        <div class="device-name">加载中...</div>
                    </div>
                    <div class="device-details">
                        <div class="detail-item">
                            <div class="detail-value">正在加载设备数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Authentication Modal -->
        <div id="authModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">设备认证</h2>
                    <span class="close-button" onclick="hideAuthModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="authDeviceMac" value="">
                    <div class="form-group">
                        <label for="phoneNumber">手机号:</label>
                        <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="请输入手机号">
                    </div>
                    <div class="form-group" id="verificationCodeGroup" style="display: none;">
                        <label for="verificationCode">验证码:</label>
                        <div class="verification-input-group">
                            <input type="text" id="verificationCode" name="verificationCode" placeholder="请输入6位验证码" maxlength="6" pattern="[0-9]{6}">
                            <button type="button" class="resend-button" id="resendCodeButton" onclick="resendVerificationCode()" disabled>重新发送<span id="resendCountdown" class="resend-countdown"></span></button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modal-button cancel-button" onclick="hideAuthModal()">取消</button>
                    <button type="button" class="modal-button" id="getVerificationCodeButton" onclick="requestVerificationCode()">获取验证码</button>
                    <button type="button" class="modal-button submit-button" id="confirmAuthButton" onclick="submitDeviceAuth()" style="display: none;">确认认证</button>
                </div>
            </div>
        </div>

        <!-- Authentication Status Modal -->
        <div id="authStatusModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">认证状态信息</h2>
                    <span class="close-button" onclick="hideAuthStatusModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="authStatusList" class="auth-status-list">
                        <!-- 认证状态信息将在这里动态生成 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modal-button" onclick="hideAuthStatusModal()">关闭</button>
                </div>
            </div>
        </div>

        <!-- Add PleaseWait Modal for ajax calls -->
        <div id="PleaseWait" class="modal" style="display: none;">
            <div class="modal-content" style="width: auto; padding: 15px;">
                <h3 id="lPleaseWait" style="margin: 0;">请稍候...</h3>
            </div>
        </div>
    </body>
</html> 