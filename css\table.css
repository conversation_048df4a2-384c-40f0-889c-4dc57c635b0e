a img {	border:0	}	

/* antd 风格表格样式 */
table.example {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  border-collapse: separate;
  box-shadow: 0 2px 8px #f0f1f2;
  margin: 16px 0;
  background: #fff;
  overflow: hidden;
}

table.example th, table.example td {
  border: none;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  color: #262626;
}

table.example thead th {
  background: #8bbde2;
  color: #262626;
  font-weight: 500;
}

table.example tr.alternate {
  background: #fafafa;
}

table.example tbody tr:hover {
  background: #e6f7ff;
  transition: background 0.3s;
}

table.example tfoot td {
  background: #fafafa;
}

/* 保留原有特殊样式 */
.accLogTbl { max-height: 305px; overflow: auto; border:1px solid #8bbde2; background:#fff; }
.accLogTbl table.example { border:0;  border-bottom:1px solid #8bbde2; }
.accLogTbl table.example th:first-child, .accLogTbl table.example td:first-child { border-left:0; }

th.table-sortable {
  cursor:pointer;
  background-image:url("../images/sortable.png");
  background-position:center 50%;
  background-repeat:no-repeat;
  padding-left:12px;
}
th.table-sorted-asc {
  background-image:url("../images/sorted_up.png");
  background-position:center 50%;
  background-repeat:no-repeat;
}
th.table-sorted-desc {
  background-image:url("../images/sorted_down.png");
  background-position:center 50%;
  background-repeat:no-repeat;
}

select.table-autofilter {
  font-size:smaller;
}

/* 其他原有样式保留 */

/* Examples which stray from the default */
table.altstripe tr.alternate2 {
	background-color:#ccffff;
}
/* Icons box */
.iconset {
	margin:5px;
	border:1px solid #cccccc;
	border-color:#cccccc #666666 #666666 #cccccc;
	text-align:center;
	cursor:pointer;
	width:100px;
}
.iconset img {
	margin:3px;
}

/* Documentation */
tr.doc_section {
	font-weight:bold;
	text-align:center;
	background-color:#dddddd;
}

