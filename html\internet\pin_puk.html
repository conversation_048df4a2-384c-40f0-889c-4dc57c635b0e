<a href='#' class='help' onclick="getHelp('PinPuk')">&nbsp;</a>

<div id="PinPukAttempts" style="display: none">
	<label id="lAttempts" class="title"></label>
	<label id="vPinAttmepts"> </label>
	<label id="vPukAttmepts"> </label>
	<br>
</div>

<div id="ProvidePin" style="display: none">
	<label id="lProvidePin" class="title"></label>
	<br style="clear:both" />
	<label id="lEnterPin"></label>
	<input name="" type="password" maxlength="8" id="txtPin" class="textfield"  onclick="clearAlertError()"/>
    	<div class="pBoxCont" ><label id="lAlertError" class="error"></label></div>
	<div align='right'><span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='ProvidePin()'  /></span> </div>
</div>

<div id="ResetPinUsingPuk" style="display: none">
	<label id="lResetPin" class="title"></label>

	<br style="clear:both" />
	<label id="lEnterPuk"> </label>
	<input name="" type="password" maxlength="10" id="txtPuk0" class="textfield" />

	<br style="clear:both" />
	<label id="lEnterNewPin"> </label>
	<input name="" type="password" maxlength="8" id="txtNewPin0" class="textfield" onclick="clearAlertError0()"/>
    	<div class="pBoxCont" ><label id="lAlertError0" class="error"></label></div>
	<div align='right'><span class="btnWrp"><input type='button' id='btUpdate0' value='Save' onclick='resetPinUsingPuk()'  /></span> </div>
</div>

<div id="EnableDisablePin" style="display: none">
	<label id="lEnableDisablePin" class="title"></label>

	<br style="clear:both" />
	<label id="lEnterPin"> </label>
	<input name="" type="password" maxlength="8" id="txtPin1" class="textfield" onclick="clearAlertError1()"/>
    	<div class="pBoxCont" ><label id="lAlertError1" class="error"></label></div>
	<div align='right'><span class="btnWrp"><input type='button' id='btUpdate1' value='' onclick='EnableDisablePin()'  /></span> </div>
</div>

<div id="ChangePin" style="display: none">
	<label id="lChangePin" class="title"></label>

	<br style="clear:both" />
	<label id="lEnterPin1"> </label>
	<input name="" type="password" maxlength="8" id="txtPin2" class="textfield" onclick="clearAlertError2()"/>

	<br style="clear:both" />
	<label id="lEnterNewPin1"> </label>
	<input name="" type="password" maxlength="8" id="txtNewPin2" class="textfield" onclick="clearAlertError2()"/>
    	<div class="pBoxCont" ><label id="lAlertError2" class="error"></label></div>
	<div align='right'><span class="btnWrp"><input type='button' id='btUpdate2' value='Save' onclick='ChangePin()'  /></span> </div>
</div>

