var g_menues = new Array(0);
var g_objContent = null;
var g_objXML =  $().XML_Operations();
var g_clickedItem ='User Management';
var  _dashboardInterval        = 15000;
var  _connectedDeviceInterval  = 60000;
var _trafficstatisticsInterval = 60000;
var _networkActivityInterval   = 60000;
var _storageSettingsInterval = 30000;
var  _WiFiInterval  = 25000;

var  _dashboardIntervalID;
var  _connectedDeviceIntervalID;
var _trafficstatisticsIntervalID;
var _networkActivityIntervalID;
var _storageSettingsIntervalID;
var _WiFiIntervalID;
/* This function get the XML from the server via ajax.
 *  Get is Method and success fucntion is callback funciton when the request success
 */
document.onkeydown = function (e) {
    if(null == g_objContent)
        return true;
    var ev = window.event || e;
    var code = ev.keyCode || ev.which;
    if (code == 116) {
        ev.keyCode ? ev.keyCode = 0 : ev.which = 0;
        cancelBubble = true;
        g_objContent.onLoad(true);
        return false;
    }
}



function callXML(xml,sFucntion) {

    $.ajax({
    type: "GET",
    url: xml,
    dataType: "xml",
    async: false,
    success: sFucntion

    });
}

/* This is important function which parses the UIxml file
 * Creates the Menu and submenu depending upon XML items
 *
 */
function parseXml(xml) {
    document.getElementById('phoneMenu').innerHTML = "";
    
    var antMenuElement = document.getElementById('main-menu');
    if(antMenuElement) {
        antMenuElement.innerHTML = "";
    }
    
    var sideMenuElement = document.getElementById('side-menu');
    if(sideMenuElement) {
        sideMenuElement.innerHTML = "";
    }
    
    var menuIndex = 0;
    $(xml).find("Tab").each(function() {
        var tabName = jQuery.i18n.prop($(this).attr("Name").toString());
        var menu = new Array(0);
        var i = 0;
        
        if($(this).attr("type").toString() == 'submenuabsent') {
            menu[i] = new Array(2);
            menu[i][0] = $(this).attr("implFunction").toString();
            menu[i][1] = $(this).attr("xmlName").toString();
            i++;
            
            if(antMenuElement) {
                antMenuElement.innerHTML += "<li class='ant-menu-item' id='ant-menu-"+menuIndex+"' onClick='createMenu("+(menuIndex+1)+")'>" + tabName + "</li>";
            }
            
            if(sideMenuElement) {
                sideMenuElement.innerHTML += "<li><a href='#' id='sidenav-main-"+(menuIndex+1)+"' onClick='createMenu("+(menuIndex+1)+")'>" + tabName + "</a></li>";
            }
        } else {
            $(this).find("Menu").each(function() {
                menu[i] = new Array(3);
                menu[i][0] = $(this).attr("id").toString();
                menu[i][1] = $(this).attr("implFunction").toString();
                menu[i][2] = $(this).attr("xmlName").toString();
                i++;
            });
            
            if(antMenuElement) {
                var submenuId = "submenu-"+menuIndex;
                antMenuElement.innerHTML += 
                    "<li class='ant-menu-submenu ant-menu-submenu-inline' id='"+submenuId+"'>" +
                    "  <div class='ant-menu-submenu-title' onClick='toggleAntSubmenu(\""+submenuId+"\")'>" +
                    "    " + tabName +
                    "    <span class='ant-menu-submenu-arrow'></span>" +
                    "  </div>" +
                    "  <ul class='ant-menu ant-menu-sub ant-menu-hidden' id='"+submenuId+"-items'></ul>" +
                    "</li>";
                
                var submenuItemsEl = document.getElementById(submenuId+"-items");
                for(var j = 0; j < i; j++) {
                    var subMenuId = menu[j][0];
                    submenuItemsEl.innerHTML += "<li class='ant-menu-item' id='ant-menu-item-"+subMenuId+"' onClick='displayForm(\""+subMenuId+"\")'>" + jQuery.i18n.prop(subMenuId) + "</li>";
                }
            }
            
            if(sideMenuElement) {
                sideMenuElement.innerHTML += "<li><a href='#' id='sidenav-main-"+(menuIndex+1)+"' onClick='createMenu("+(menuIndex+1)+")'>" + tabName + "</a></li>";
            }
        }
        
        g_menues[menuIndex++] = menu;
        
        if(document.getElementById('phoneMenu') && document.getElementById('phoneMenu').childElementCount === 0) {
            if(menu[0].length==2) {
                document.getElementById('phoneMenu').innerHTML += "<li><a href='#' class='main-nav-item' id='sidenav-"+(menuIndex)+"' onClick='createMenu("+(menuIndex)+")'>" + tabName + "</a></li>";
            } else {
                document.getElementById('phoneMenu').innerHTML += "<li><button class='dropdown-btn' id='dropdown-btn-"+(menuIndex)+"' onClick='toggleDropdown("+(menuIndex)+")'>" + tabName + " <i class='fa fa-caret-down menu-arrow'></i></button><div class='dropdown-container' id='dropdown-container-"+(menuIndex)+"'></div></li>";    
                
                var dropdownContainer = document.getElementById('dropdown-container-'+(menuIndex));
                for(var j = 0; j < menu.length; j++) {
                    var subMenuId = menu[j][0];
                    dropdownContainer.innerHTML += "<a href='#' onClick='displayForm(\""+subMenuId+"\")' class='submenu-item' id='submenu-"+subMenuId+"'>" + jQuery.i18n.prop(menu[j][0]) + "</a>";
                }
            }
        }
        
        document.getElementById('homepic').style.display = "visible";
    });
}

/*
 * Create the submeny from XML items
 */
function createMenu(index) {
    // 防止重复快速点击
    if (createMenu.isProcessing) {
        return;
    }
    createMenu.isProcessing = true;
    
    try {
        var temp = index;
        removeMenuClass();
    
    var antMenuItemId = 'ant-menu-' + (temp - 1);
    var antMenuItem = document.getElementById(antMenuItemId);

    if (temp === 1) {
        antMenuItem = document.getElementById('ant-menu-0');
    }
    
    if(antMenuItem) {
        var allMenuItems = document.querySelectorAll('.ant-menu-item');
        allMenuItems.forEach(function(item) {
            item.classList.remove("ant-menu-item-selected");
        });
        
        antMenuItem.classList.add("ant-menu-item-selected");
    }
    
    var sideMainItems = document.querySelectorAll('#side-menu a');
    for(var i = 0; i < sideMainItems.length; i++) {
        sideMainItems[i].classList.remove('on');
    }
    
    var sideMainItem = document.getElementById('sidenav-main-'+temp);
    if(sideMainItem) {
        sideMainItem.classList.add('on');
    }
    
    var sidenavItems = document.querySelectorAll('.main-nav-item');
    for(var i = 0; i < sidenavItems.length; i++) {
        sidenavItems[i].classList.remove('active');
    }
    
    var sidenavItem = document.getElementById('sidenav-'+temp);
    if(sidenavItem) {
        sidenavItem.classList.add('active');
    }
    
    var isDashboard = (index === 1);
    
    var menu = g_menues[index-1];
    if(menu[0].length==2) {
        clearRefreshTimers();
        var obj = eval('$("#mainColumn").'+ menu[0][0] + '({})');
        obj.setXMLName(menu[0][1]);
        obj.onLoad(true);
        g_objContent = obj;
        
        var homepic = document.getElementById('homepic');
        var infoRow = document.querySelector('.info-row');
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        var isManuallyOpened = infoRow && infoRow.classList.contains('open');
        
        if(homepic) {
            homepic.style.display = isDashboard ? "block" : "none";
        }
        
        if(infoToggleBtn) {
            if(isDashboard) {
                var isMobile = window.innerWidth <= 768;
                infoToggleBtn.style.display = isMobile ? "flex" : "none";
            } else {
                infoToggleBtn.style.display = "none";
            }
        }
        
        if(infoRow) {
            if(isDashboard) {
                var isMobile = window.innerWidth <= 768;
                if(!isManuallyOpened) {
                    infoRow.style.display = isMobile ? "none" : "grid";
                }
            } else {
                if(!isManuallyOpened) {
                    infoRow.style.display = "none";
                }
            }
        }
    } else {
        var mainColumnElement = document.getElementById('mainColumn');
        if (!mainColumnElement) {
            return;
        }
        
        mainColumnElement.innerHTML = "<div id='Content' class='content' style='width:100%'></div><br class='clear' /><br class='clear' />";
        
        if (document.getElementById('Content')) {
            displayForm(menu[0][0]);
        } else {
        }
        
        var homepic = document.getElementById('homepic');
        var infoRow = document.querySelector('.info-row');
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        
        if(homepic) homepic.style.display = isDashboard ? "block" : "none";
        if(infoRow) infoRow.style.display = isDashboard ? (window.innerWidth <= 768 ? "none" : "grid") : "none";
        if(infoToggleBtn) infoToggleBtn.style.display = isDashboard && window.innerWidth <= 768 ? "flex" : "none";
    }
    
    if(menu.length > 0 && menu[0].length > 2) {
        var phoneMenu = document.getElementById('phoneMenu');
        if(phoneMenu) {
            phoneMenu.innerHTML = '';
            
            for(var i = 0; i < menu.length; i++) {
                var subMenuId = menu[i][0];
                phoneMenu.innerHTML += "<li><a href='#' onClick='displayForm(\""+subMenuId+"\")' class='submenu-item' id='submenu-"+subMenuId+"'>" + jQuery.i18n.prop(subMenuId) + "</a></li>";
            }
        }
    }
    
    if(isDashboard && typeof initInfoRowVisibility === 'function') {
        setTimeout(initInfoRowVisibility, 100);
    }
    } catch (error) {
        console.error('创建菜单时出错:', error);
    } finally {
        // 延迟重置处理标志
        setTimeout(function() {
            createMenu.isProcessing = false;
        }, 200);
    }
}

function removeMenuClass() {
    var menuItems = document.querySelectorAll('.ant-menu-item');
    menuItems.forEach(function(item) {
        item.classList.remove("ant-menu-item-selected");
    });
}

/*
* Function for passing the JavaScript
*/
function createMenuFromXML() {
    callXML("xml/ui_" + g_platformName + ".xml", function(xml) {
        parseXml(xml);
        
        setTimeout(function() {
            
            var sideMenu = document.getElementById('side-menu');
            var phoneMenu = document.getElementById('phoneMenu');
            
            if (sideMenu && phoneMenu) {
                sideMenu.innerHTML = '';
                phoneMenu.innerHTML = '';
                
                for (var i = 0; i < g_menues.length; i++) {
                    var menu = g_menues[i];
                    var menuIndex = i + 1;
                    
                    var mainMenuName = '';
                    var mainMenuItem = document.querySelector('#ant-menu-' + i);
                    if (mainMenuItem) {
                        mainMenuName = mainMenuItem.textContent.trim();
                    } else {
                        var submenuTitle = document.querySelector('#submenu-' + i + ' .ant-menu-submenu-title');
                        if (submenuTitle) {
                            mainMenuName = submenuTitle.textContent.trim();
                        }
                    }
                    
                    if (!mainMenuName) {
                        continue;
                    }
                    
                    if (menu[0] && menu[0].length == 2) {
                        sideMenu.innerHTML += "<li><a href='#' class='main-nav-item' id='sidenav-"+menuIndex+"' onClick='createMenu("+menuIndex+")'>" + mainMenuName + "</a></li>";
                    } 
                    else {
                        sideMenu.innerHTML += "<li><a href='#' class='main-nav-item' id='sidenav-"+menuIndex+"' onClick='createMenu("+menuIndex+")'>" + mainMenuName + "</a></li>";
                    }
                }
                
            } else {
            }
            
            if (typeof initInfoRowVisibility === 'function') {
                
                var infoRow = document.querySelector('.info-row');
                var wasManuallyOpened = infoRow && infoRow.classList.contains('open');
                
                initInfoRowVisibility();
                
                if (wasManuallyOpened && infoRow) {
                    if (!infoRow.classList.contains('open')) {
                        infoRow.classList.add('open');
                    }
                    
                    var isMobile = window.innerWidth <= 768;
                    infoRow.style.display = isMobile ? 'block' : 'grid';
                    
                    var infoToggleBtn = document.getElementById('infoToggleBtn');
                    if (infoToggleBtn && !infoToggleBtn.classList.contains('open')) {
                        infoToggleBtn.classList.add('open');
                    }
                }
            }
        }, 50);
    });
}

/*
 * Check which item is selected and take appropriate action to execute the
 * panel class, and call his onLoad function as well as set the XML Name
 */
function displayForm(clickedItem) {
    // 防止重复快速点击
    if (displayForm.isProcessing) {
        return;
    }
    displayForm.isProcessing = true;
    
    try {
        clearRefreshTimers();

        g_objContent = null;
    
    var contentElement = document.getElementById("Content");
    var mainColumnElement = document.getElementById("mainColumn");
    
    if (!contentElement && mainColumnElement) {
        
        mainColumnElement.innerHTML = "<div id='Content' class='content' style='width:100%'></div><br class='clear' /><br class='clear' />";
        
        contentElement = document.getElementById("Content");
    }
    
    if (!contentElement) {
        return;
    }
    
    var homepic = document.getElementById('homepic');
    var infoRow = document.querySelector('.info-row');
    var infoToggleBtn = document.getElementById('infoToggleBtn');
    
    var isManuallyOpened = infoRow && infoRow.classList.contains('open');
    
    if(homepic) homepic.style.display = "none";
    if(infoToggleBtn) infoToggleBtn.style.display = "none";
    
    if(infoRow && !isManuallyOpened) {
        infoRow.style.display = "none";
    }
    
    for(var i=0; i<g_menues.length; i++) {
        var _menu = g_menues[i];
        for(var j=0; j<_menu.length; j++) {
            if(_menu[j][2]!='temp') {
                if(_menu[j][0]==clickedItem) {
                    try {
                        var obj = eval('$("#Content").'+ _menu[j][1] + '("'+ _menu[j][0]+'")');
                        if (obj && typeof obj.setXMLName === 'function') {
                            obj.setXMLName(_menu[j][2]);
                            obj.onLoad(true);
                            g_objContent = obj;
                        } else {
                            contentElement.innerHTML = "<div class='error-message'>模块初始化失败: " + _menu[j][1] + "</div>";
                        }
                    } catch (e) {
                        contentElement.innerHTML = "<div class='error-message'>加载模块失败: " + _menu[j][1] + "<br>错误: " + e.message + "</div>";
                    }
                    break;
                }
            }
        }
    }
    
    if(g_objContent == null && contentElement) {
        contentElement.innerHTML = "";
    }

    setActiveSubmenuInAntDesign(clickedItem);
    } catch (error) {
        console.error('显示表单时出错:', error);
    } finally {
        // 延迟重置处理标志
        setTimeout(function() {
            displayForm.isProcessing = false;
        }, 200);
    }
}

function setActiveSubmenuInAntDesign(clickedItem) {
    var submenuItems = document.querySelectorAll('.submenu-item');
    submenuItems.forEach(function(item) {
        item.classList.remove('active');
    });
    
    var menuItem = document.getElementById('submenu-'+clickedItem);
    if(menuItem) {
        menuItem.classList.add('active');
        
        var parentDropdown = menuItem.closest('.dropdown-container');
        if(parentDropdown) {
            parentDropdown.style.display = 'block';
            
            var btnId = parentDropdown.id.replace('dropdown-container-', 'dropdown-btn-');
            var dropdownBtn = document.getElementById(btnId);
            if(dropdownBtn) {
                var allDropdownBtns = document.querySelectorAll('.dropdown-btn');
                allDropdownBtns.forEach(function(btn) {
                    btn.classList.remove('active');
                });
                
                dropdownBtn.classList.add('active');
            }
        }
    }
    
    var antMenuItem = document.getElementById('ant-menu-item-'+clickedItem);
    if(antMenuItem) {
        var allAntMenuItems = document.querySelectorAll('.ant-menu-item');
        allAntMenuItems.forEach(function(item) {
            item.classList.remove('ant-menu-item-selected');
        });
        
        antMenuItem.classList.add('ant-menu-item-selected');
        
        var parentSubmenu = antMenuItem.closest('.ant-menu-sub');
        if(parentSubmenu && parentSubmenu.id) {
            var submenuId = parentSubmenu.id.replace('-items', '');
            var submenu = document.getElementById(submenuId);
            if(submenu) {
                var allSubmenu = document.querySelectorAll('.ant-menu-submenu');
                allSubmenu.forEach(function(item) {
                    item.classList.remove('ant-menu-submenu-open');
                    var itemsEl = document.getElementById(item.id + '-items');
                    if(itemsEl) {
                        itemsEl.classList.add('ant-menu-hidden');
                    }
                });
                
                submenu.classList.add('ant-menu-submenu-open');
                parentSubmenu.classList.remove('ant-menu-hidden');
            }
        }
    }
}

function clearRefreshTimers() {
    try {
        if (_dashboardIntervalID) {
            clearInterval(_dashboardIntervalID);
            _dashboardIntervalID = null;
        }
        if (_connectedDeviceIntervalID) {
            clearInterval(_connectedDeviceIntervalID);
            _connectedDeviceIntervalID = null;
        }
        if (_trafficstatisticsIntervalID) {
            clearInterval(_trafficstatisticsIntervalID);
            _trafficstatisticsIntervalID = null;
        }
        if (_networkActivityIntervalID) {
            clearInterval(_networkActivityIntervalID);
            _networkActivityIntervalID = null;
        }
        if (_storageSettingsIntervalID) {
            clearInterval(_storageSettingsIntervalID);
            _storageSettingsIntervalID = null;
        }
        if (_WiFiIntervalID) {
            clearInterval(_WiFiIntervalID);
            _WiFiIntervalID = null;
        }
    } catch (e) {
        console.error('清理定时器时出错:', e);
    }
}

function dashboardOnClick(menuIndex,subMenuID) {
    var infoRow = document.querySelector('.info-row');
    var wasManuallyOpened = infoRow && infoRow.classList.contains('open');
    
    var selMenuIdx;
    for (var menuIdx = 0; menuIdx < g_menues.length; ++menuIdx) {
        var menu = g_menues[menuIdx];
        for (var subMenuIdx = 0; subMenuIdx < menu.length; ++subMenuIdx) {
            if (subMenuID == menu[subMenuIdx][0]) {
                selMenuIdx = menuIdx + 1;
                break;
            }
        }
    }
    createMenu(selMenuIdx);
    displayForm(subMenuID);
    
    if (wasManuallyOpened && infoRow) {
        if (!infoRow.classList.contains('open')) {
            infoRow.classList.add('open');
        }
        
        var isMobile = window.innerWidth <= 768;
        infoRow.style.display = isMobile ? 'block' : 'grid';
        
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        if (infoToggleBtn) {
            if (!infoToggleBtn.classList.contains('open')) {
                infoToggleBtn.classList.add('open');
            }
            
            if (!isMobile) {
                infoToggleBtn.style.display = "none";
            } else {
                infoToggleBtn.style.display = "flex";
            }
        }
    }
}

function openNav() {
    document.getElementById("mySidenav").style.width = "250px";
}

function closeNav() {
    document.getElementById("mySidenav").style.width = "0";
}

function toggleDropdown(index) {
    var dropdown = document.getElementById('dropdown-container-'+index);
    if(dropdown) {
        var isOpen = dropdown.style.display === 'block';
        
        var allDropdowns = document.getElementsByClassName('dropdown-container');
        for(var i = 0; i < allDropdowns.length; i++) {
            allDropdowns[i].style.display = 'none';
            
            var btnId = allDropdowns[i].id.replace('dropdown-container-', 'dropdown-btn-');
            var btn = document.getElementById(btnId);
            if(btn) {
                btn.classList.remove('active');
            }
        }
        
        if(!isOpen) {
            dropdown.style.display = 'block';
            
            var currentBtn = document.getElementById('dropdown-btn-'+index);
            if(currentBtn) {
                currentBtn.classList.add('active');
            }
            
            if(dropdown.children.length === 0) {
                var menu = g_menues[index-1];
                if(menu && menu.length > 0 && menu[0].length > 2) {
                    for(var j = 0; j < menu.length; j++) {
                        var subMenuId = menu[j][0];
                        dropdown.innerHTML += "<a href='#' onClick='displayForm(\""+subMenuId+"\")' class='submenu-item' id='submenu-"+subMenuId+"'>" + jQuery.i18n.prop(menu[j][0]) + "</a>";
                    }
                }
            }
        }
    }
}

function setActive(element) {
    var menuItems = document.querySelectorAll('.leftMenu li');
    for(var i = 0; i < menuItems.length; i++) {
        menuItems[i].classList.remove('active');
    }
    
    element.parentNode.classList.add('active');
}

document.addEventListener('click', function(event) {
    var sidenav = document.getElementById('mySidenav');
    var mobileMenuBtn = document.getElementById('mobileMenubtn');
    
    if(event.target === mobileMenuBtn || sidenav.contains(event.target)) {
        return;
    }
    
    if(sidenav.style.width === '250px') {
        closeNav();
    }
});

var functionMap = {
    "loadHTML": function(arg) { return $("#Content").loadHTML(arg); },
};

/**
 * 切换Ant Design风格子菜单的显示/隐藏
 */
function toggleAntSubmenu(submenuId) {
    var submenu = document.getElementById(submenuId);
    var submenuItems = document.getElementById(submenuId + "-items");
    
    if(submenu.classList.contains("ant-menu-submenu-open")) {
        submenu.classList.remove("ant-menu-submenu-open");
        submenuItems.classList.add("ant-menu-hidden");
    } else {
        var allOpenSubmenus = document.querySelectorAll('.ant-menu-submenu-open');
        allOpenSubmenus.forEach(function(item) {
            item.classList.remove("ant-menu-submenu-open");
            var itemsEl = document.getElementById(item.id + "-items");
            if(itemsEl) {
                itemsEl.classList.add("ant-menu-hidden");
            }
        });
        
        submenu.classList.add("ant-menu-submenu-open");
        submenuItems.classList.remove("ant-menu-hidden");
        
        var menuItems = submenuItems.querySelectorAll('.ant-menu-item');
        menuItems.forEach(function(item) {
            item.style.margin = '0';
        });
    }
}

/**
 * 初始化设备信息区域的显示状态
 * 只在主界面/仪表盘页面显示设备信息
 */
function initInfoRowVisibility() {
    var infoToggleBtn = document.getElementById('infoToggleBtn');
    var infoRow = document.querySelector('.info-row');
    var homepic = document.getElementById('homepic');

    if (!infoToggleBtn || !infoRow || !homepic) {
        return;
    }

    var isMobile = window.innerWidth <= 768;
    var isMainDashboard = false;
    
    
    var selectedAntMenuItem = document.querySelector('.ant-menu-item-selected');
    if (selectedAntMenuItem) {
        isMainDashboard = (selectedAntMenuItem.id === 'ant-menu-0');
    }
    
    if (!isMainDashboard) {
        var selectedSideMenuItem = document.querySelector('#side-menu a.on') || 
                                  document.querySelector('#sidenav-1.active');
        if (selectedSideMenuItem) {
            isMainDashboard = (selectedSideMenuItem.id === 'sidenav-main-1' || 
                              selectedSideMenuItem.id === 'sidenav-1');
        }
    }
    
    if (!isMainDashboard) {
        var selectedMobileMenuItem = document.querySelector('.main-nav-item.active');
        if (selectedMobileMenuItem) {
            isMainDashboard = (selectedMobileMenuItem.id === 'sidenav-1');
        }
    }

    if (!isMainDashboard && 
        !document.querySelector('.ant-menu-item-selected') && 
        !document.querySelector('#side-menu a.on') && 
        !document.querySelector('.main-nav-item.active')) {
        var contentElement = document.getElementById('Content');
        if (!contentElement || contentElement.children.length === 0) {
            isMainDashboard = true; 
        }
    }
    

    if (isMainDashboard) {
        homepic.style.display = "block"; 
        

        var isManuallyOpened = infoRow.classList.contains('open');
        
        if (isMobile) {
            infoToggleBtn.style.display = "flex";
            

            if (!isManuallyOpened) {
                infoRow.style.display = "none"; 
            }

        } else { 
            infoToggleBtn.style.display = "none";
            

            if (!isManuallyOpened) {
                infoRow.style.display = "grid"; 
            }

        }
    } else {

        homepic.style.display = "none";
        infoToggleBtn.style.display = "none";
        infoRow.style.display = "none";
    }
}


document.addEventListener("DOMContentLoaded", function() {

    setTimeout(initInfoRowVisibility, 500);
});


window.addEventListener("resize", function() {

    var infoRow = document.querySelector('.info-row');
    var isManuallyOpened = false;
    

    if (infoRow && infoRow.classList.contains('open')) {
        isManuallyOpened = true;
    }
    

    initInfoRowVisibility();
    

    if (isManuallyOpened && infoRow) {

        if (!infoRow.classList.contains('open')) {
            infoRow.classList.add('open');
        }
        

        var isMobile = window.innerWidth <= 768;
        infoRow.style.display = isMobile ? 'block' : 'grid';
        
        var infoToggleBtn = document.getElementById('infoToggleBtn');
        if (infoToggleBtn) {
            if (!infoToggleBtn.classList.contains('open')) {
                infoToggleBtn.classList.add('open');
            }
        }
    }
});