(function ($) {
    $.fn.objStorageSettings = function (InIt) {

        var rdRadioStorage;

	var controlMapExisting=new Array(0);
        var controlMapCurrent=new Array(0);

        var xmlName = '';

	var _arrayStorageDevices=new Array(0);

	var mapData;
	
	var FsErr=0;

        this.onLoad = function (flag) {
            _arrayStorageDevices=new Array(0);
            var index = 0;

	    if (flag)
		this.loadHTML();
                document.getElementById("title").innerHTML = jQuery.i18n.prop(InIt);
            var xml = getData(xmlName);

            var mode;

            var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);
	
	    var arrayLabels = document.getElementsByTagName("th");
            lableLocaliztion(arrayLabels);

            var buttonID = document.getElementById("btUpdate").id;
            buttonLocaliztion(buttonID);
	    
	    var buttonID = document.getElementById("btnSafelyRemoveDevices").id;
	    buttonLocaliztion(buttonID);

	    mode=$(xml).find("enabled").text();
	    rdRadioStorage.setRadioButton(mode);
            controlMapExisting = g_objXML.putMapElement(controlMapExisting,index++, "RGW/storage/enabled",mode);

            if ( mode == "0") {
		document.getElementById('deviceusage').style.display = 'none'; 
            }
	    else {
                document.getElementById('deviceusage').style.display = 'block';
            }

	    controlMapCurrent = g_objXML.copyArray(controlMapExisting,controlMapCurrent);

	    var nameSD, usedSpaceSD, availableSpaceSD;
	    var indexStorageDevices=0;

            $(xml).find("storage_devices_list").each(function(){
		    $(this).find("Item").each(function(){
			    nameSD = $(this).find("name").text();
			    usedSpaceSD = $(this).find("used_space").text();
			    availableSpaceSD = $(this).find("available_space").text();
			    _arrayStorageDevices[indexStorageDevices]=new Array(4);
			    _arrayStorageDevices[indexStorageDevices][0]=indexStorageDevices;
			    _arrayStorageDevices[indexStorageDevices][1]=nameSD;
			    _arrayStorageDevices[indexStorageDevices][2]=usedSpaceSD;
			    _arrayStorageDevices[indexStorageDevices][3]=availableSpaceSD;
			    indexStorageDevices++;
			});
		});

	    this.loadStorageDevicesTableData(_arrayStorageDevices);

	    if ( FsErr == "1" ) {
		fs_busy_err = $(xml).find("fs_busy_err").text();
		if ( fs_busy_err == "1" ) {
		    showAlert(jQuery.i18n.prop("lstoarageError"));
		}
		FsErr=0;
	    }

	}

        this.loadStorageDevicesTableData = function(arrayTableData){

	    var tableStorageDevices=document.getElementById('tableStorageDevices');
            var tBodytable = tableStorageDevices.getElementsByTagName('tbody')[0];

            clearTabaleRows('tableStorageDevices');
	    if(arrayTableData.length==0){
              var row1 =  tBodytable.insertRow(0);
              var rowCol1 = row1.insertCell(0);
              rowCol1.colSpan = 4;
              rowCol1.innerHTML = jQuery.i18n.prop("tableNoData");
            }
            else{
	    for(var i=0;i<arrayTableData.length;i++){
		var arrayTableDataRow=arrayTableData[i];
		var row =  tBodytable.insertRow(i);

                var indexCol= row.insertCell(0);
                var nameSDCol = row.insertCell(1);
                var used_spaceSDCol = row.insertCell(2);
                var available_spaceSDCol = row.insertCell(3);
		
		indexCol.style.display='none';
                indexCol.innerHTML=arrayTableDataRow[0];
                nameSDCol.innerHTML=arrayTableDataRow[1];
                used_spaceSDCol.innerHTML=arrayTableDataRow[2];
                available_spaceSDCol.innerHTML=arrayTableDataRow[3];
            }
            }
	    Table.stripe(tableStorageDevices,"alternate","table-stripeclass");

    	}

        this.onPost = function () {
            var _controlMap = this.getPostData();
            if(_controlMap.length>0) {
                postXML("storage", g_objXML.getXMLDocToString(g_objXML.createXML(_controlMap)));
                //this.onLoad();
            }
        }
 this.onPostSuccess = function (){
        this.onLoad(false);
        }
        this.getPostData = function (){

            var index = 0;
            var mapData = new Array(0);

            controlMapCurrent[index++][1] = rdRadioStorage.getRadioButton();

            mapData = g_objXML.copyArray(controlMapCurrent,mapData);
            mapData = g_objXML.getChangedArray(controlMapExisting,mapData,true);

            return mapData;
        }

        this.loadHTML =  function() {
            document.getElementById('Content').innerHTML ="";
            document.getElementById('Content').innerHTML = callProductHTML("html/storage/storage_settings.html");
            rdRadioStorage = $("#rdRadioStorage").enabled_disabled("rdRadioStorage");
        }

        this.setXMLName = function (_xmlname){
            xmlName = _xmlname;
        }

        this.removeStorageDevices = function (){

	    mapData=null;
            mapData = new Array();
	    
	    this.putMapElement("RGW/storage/eject_event","1");

	    postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
	    //alert(g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
	    FsErr = 1;
	    //this.onLoad();
	}

        this.putMapElement = function(xpath,value){
            mapData[0]=new Array(2);
            mapData[0][0]=xpath;
            mapData[0][1]=value;
        }
        return this.each(function () {
		_storageSettingsIntervalID = setInterval( "g_objContent.onLoad(false)", _storageSettingsInterval);
            });

    }
})(jQuery);

function showDeviceUsage(){

    if ( document.getElementById('rdRadioStorageEnabled').checked ) 
        document.getElementById('deviceusage').style.display = 'block';
    else
        document.getElementById('deviceusage').style.display = 'none';
}

function removeDevices(){
    g_objContent.removeStorageDevices();
}
