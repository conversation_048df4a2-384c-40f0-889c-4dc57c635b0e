<?xml version="1.0" encoding="US-ASCII" ?>
<RGW> 
<sysinfo>
	<device_name/>
	<device_model/>
	<router_startup/>
	<gwname/>
	<gwdomain/>
	<namespace_version/>
	<auto_upgrade_status/>
	<actmon_enable/>
	<devmon_enable/>
	<version_num/>
	<version_date/>
	<number_of_boots/>
</sysinfo>
<lan>
        <dhcp>
	        <status/>
	        <start/>
          	<end/>
        	<lease_time/>
        	<max_leases/>
        </dhcp>
	<ip/>
	<mask/>
	<gateway/>
	<upnp_status/>
	<static_lease_list_meta/>
	<static_lease_list/>
	<modver/>
</lan>
<ntp>
	<server/>
</ntp>
<firewall>
	<mode/>
	<dos_protect/>
	<new_conn_log/>
	<drop_log/>
	<vpn>
		<ipsec_passthrough/>
		<pptp_passthrough/>
		<l2tp_passthrough/>
	</vpn>
	<modver/>
</firewall>
<static_route>
	<static_route/>
</static_route>
<upgrade>
	<remote_url/>
	<remote_ver_num/>
</upgrade>
<remote>
	<rlog_ip/>
</remote>
<management>
	<router_username/>
	<router_password/>
	<web_wlan_enable/>
	<httpd_port/>
	<syslogd_enable/>
	<web_wan_enable/>
	<syslogd_rem_ip/>
	<turbo_mode/>
	<qs_complete/>
	<modver/>
</management>
<custom_fw>
	<custom_rules_list_meta/>
	<custom_rules_list/>
	<modver/>
</custom_fw>
<device_management>
	<known_devices_list_meta/>
	<known_devices_list/>
	<modver/>
</device_management>
<locale>
	<timezone>
		<name/>
		<string/>
		<daylight/>
	</timezone>
	<language/>
	<modver/>
</locale>
<wan>
	<orig_mac/>
	<device_uuid/>
	<proto/>
	<advanced_setting/>
	<cdns_enable/>
	<cdns1/>
	<cdns2/>
	<cellular>
		<pin/>
		<puk/>
		<isp_supported_list_meta/>
		<isp_supported_list/>
		<ISP_name/>
		<username/>
		<password/>
		<baudrate/>
		<init1/>
		<init2/>
		<accessnumber/>
		<advanced>
			<connectmode/>
			<idle/>
		</advanced>
	</cellular>
	<wifi>
		<ssid></ssid>
		<enc></enc>
		<cipher></cipher>
		<psk></psk>
	</wifi>
	<modver/>
</wan>
<wlan_security>
	<mode/>
	<ssid/>
	<ssid_bcast/>
	<wps_enable/>
	<WPA2-PSK>
		<mode/>
		<key/>
		<rekey/>
	</WPA2-PSK>
	<WPA-PSK>
		<mode/>
		<key/>
		<rekey/>
	</WPA-PSK>
	<Mixed>
		<mode/>
		<key/>
		<rekey/>
	</Mixed>
	<WEP>
		<key1/>
		<key2/>
		<key3/>
		<key4/>
		<auth/>
		<encrypt/>
		<default_key/>
	</WEP>
	<WAPI-PSK>
		<key/>
		<key_type/>
	</WAPI-PSK>
	<modver/>
</wlan_security>
<wlan_mac_filters>
	<enable/>
	<mode/>
	<allow_list_meta/>
	<allow_list/>
	<deny_list_meta/>
	<deny_list/>
	<modver/>
</wlan_mac_filters>
<wlan_settings>
	<wlan_enable/>
	<mac/>
	<net_mode/>
	<channel/>
	<bandwidth/>
	<max_clients/>
	<modver/>
</wlan_settings>
<wlan_wps_client>
	<modver/>
</wlan_wps_client>
<wlan_cli_scan>
	<wireless_network_list_meta/>
        <wireless_network_list/>
        <modver/>
</wlan_cli_scan>
<file_sharing>
	<enabled/>
</file_sharing>
<control_telnet>
	<enable_telnet/>
	<modver/>
</control_telnet>
<pin_puk>
	<modver/>
</pin_puk>
<statistics>
	<modver/>
</statistics>
<dynamic_dns>
	<provider/>
	<username/>
	<password/>
	<hostname/>
	<wildcard/>
	<type/>
	<modver/>
</dynamic_dns>
<internet_access_control>
	<internet_access_control_list_meta/>
	<internet_access_control_list/>
	<modver/>
</internet_access_control>
<port_forward_trigger>
	<port_forward_list_meta/>
	<port_forward_list/>
	<port_trigger_list_meta/>
	<port_trigger_list/>
	<modver/>
</port_forward_trigger>
<device_date>
	<modver/>
</device_date>
<theme>
	<name/>
	<modver/>
</theme>
</RGW>
