(function($) {
    $.fn.objPowerOffRouter = function(InIt) {

        var xmlName = '';
        
        this.onLoad = function(flag) {
            //console.log('PowerOffRouter onLoad 被调用, flag:', flag);
            
            this.loadHTML();
            
            // 等待DOM元素加载完成
            setTimeout(() => {
                try {
                    var titleElement = document.getElementById("title");
                    if (titleElement) {
                        titleElement.innerHTML = jQuery.i18n.prop(InIt) || '关闭路由器';
                        //console.log('标题设置成功');
                    } else {
                        //console.error('title元素未找到');
                    }
                    
                    var arrayLabels = document.getElementsByTagName("label");
                    if (arrayLabels.length > 0) {
                        lableLocaliztion(arrayLabels);
                        //console.log('标签本地化完成, 共', arrayLabels.length, '个标签');
                    }
                    
                    var buttonElement = document.getElementById("btPowerOffRouter");
                    if (buttonElement) {
                        buttonLocaliztion(buttonElement.id);
                        //console.log('按钮本地化完成');
                    } else {
                        //console.error('btPowerOffRouter按钮未找到');
                    }
                    
                    // 绑定按钮事件
                    this.bindEvents();
                } catch (e) {
                    //console.error('onLoad初始化失败:', e);
                }
            }, 100);
        }
        
        this.loadHTML = function() {
            try {
                //console.log('开始加载PowerOff_router.html');
                var contentDiv = document.getElementById('Content');
                if (!contentDiv) {
                    //console.error('Content元素未找到');
                    return;
                }
                
                contentDiv.innerHTML = "";
                var htmlContent = callProductHTML("html/router/PowerOff_router.html");
                
                if (htmlContent && htmlContent.trim() !== '') {
                    contentDiv.innerHTML = htmlContent;
                    //console.log('PowerOff_router.html加载成功');
                } else {
                    //console.error('PowerOff_router.html内容为空或加载失败');
                    // 使用备用HTML
                    contentDiv.innerHTML = this.getFallbackHTML();
                }
            } catch (e) {
                //console.error('加载HTML失败:', e);
                document.getElementById('Content').innerHTML = this.getFallbackHTML();
            }
        }

        function _PowerOff() {
            //console.log('开始执行关闭路由器操作');
            
            try {
                // 发送关闭路由器的XML请求
                var mapData = new Array(0);
                mapData = putMapElement(mapData, "RGW/power_off", "1", 0);
                
                if (typeof g_objXML !== 'undefined' && g_objXML) {
                    var xmlString = g_objXML.getXMLDocToString(g_objXML.createXML(mapData));
                    //console.log('发送XML数据:', xmlString);
                    postXML(xmlName, xmlString);
                } else {
                    //console.error('g_objXML未定义，使用简单XML');
                    var simpleXML = '<?xml version="1.0" encoding="US-ASCII"?><RGW><power_off>1</power_off></RGW>';
                    postXML(xmlName, simpleXML);
                }
                
                sm('PowerOffRouterModalBox', 319, 170);
                document.getElementById("h1PowerOffRouter").innerHTML = jQuery.i18n.prop("h1PowerOffRouter");
                document.getElementById("labelPowerOffRouter").innerHTML = jQuery.i18n.prop("labelPowerOffRouter");

                afterPowerOffRouterID = setInterval("afterPowerOff()", 50000);
            } catch (e) {
                //console.error('关闭路由器操作失败:', e);
                alert('关闭路由器失败: ' + e.message);
            }
        }

        this.afterPowerOff = function() {
            hm();
            clearInterval(afterPowerOffRouterID);
            clearAuthheader();
        }

        this.onPost = function() {
            // 处理表单提交，直接调用关闭函数
            _PowerOff();
        }
        
        this.getFallbackHTML = function() {
            return `
                <label id="title" class="title">关闭路由器</label>
                <label id='lPowerOffRouter'>点击下面的按钮关闭路由器</label>
                <br><br>
                <div align='center'>
                    <span class="btnWrp">
                        <input type='button' id='btPowerOffRouter' value='PowerOff Router' onclick='powerOffRouter()' />
                    </span>
                </div>
                
                <div class="popUpBox" id="PowerOffRouterModalBox" style="display: none">
                    <h1 id="h1PowerOffRouter">关闭路由器</h1>
                    <div align="center">
                        <label id="labelPowerOffRouter">正在关闭路由器...</label>
                    </div>
                </div>
                
                <div class="popUpBox" id="PowerOffRouterBox" style="display: none">
                    <h1 id="h1PowerOffRouter">关闭路由器</h1>
                    <a class="close" style="width: 40px;">&nbsp;</a><br style="clear:both" />
                    <div align="center">
                        <label id="lQueryPowerOffRouter">确定要关闭路由器吗？</label>
                    </div>
                    <div class="bottonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:right">
                        <a href="#." id="btnModalCancle" onclick="onPowerOffCancel()" class="cancel">取消</a>
                        <span class="btnWrp">
                            <button value="OK" id="btnPowerOffOK" onclick="OnPowerOffOK()">确定</button>
                        </span>
                    </div>
                </div>
            `;
        }
        
        this.setXMLName = function(_xmlname) {
            xmlName = _xmlname;
        }

        this.bindEvents = function() {
            var self = this;
            
            // 使用事件委托来绑定事件，确保元素存在
            $(document).on('click', '#btPowerOffRouter', function() {
                //console.log('关闭路由器按钮被点击');
                
                sm('PowerOffRouterBox', 360, 170);
                document.getElementById("btnPowerOffOK").innerHTML = jQuery.i18n.prop("btnPowerOffOK");
                document.getElementById("btnModalCancle").innerHTML = jQuery.i18n.prop("btnModalCancle");
                document.getElementById("h1PowerOffRouter").innerHTML = jQuery.i18n.prop("h1PowerOffRouter");
                document.getElementById("lQueryPowerOffRouter").innerHTML = jQuery.i18n.prop("lQueryPowerOffRouter");
            });
            
            $(document).on('click', '#btnPowerOffOK', function() {
                //console.log('确认关闭按钮被点击');
                hm();
                _PowerOff();
            });
        }

       
        return this.each(function() {
        });
    }
})(jQuery);

// 全局函数，供setInterval调用
function afterPowerOff() {
    hm();
    if (typeof afterPowerOffRouterID !== 'undefined') {
        clearInterval(afterPowerOffRouterID);
    }
    clearAuthheader();
}

// 全局函数，处理关闭路由器按钮点击
function powerOffRouter() {
    //console.log('全局powerOffRouter函数被调用');
    sm('PowerOffRouterBox', 360, 170);
    document.getElementById("btnPowerOffOK").innerHTML = jQuery.i18n.prop("btnPowerOffOK");
    document.getElementById("btnModalCancle").innerHTML = jQuery.i18n.prop("btnModalCancle");
    document.getElementById("h1PowerOffRouter").innerHTML = jQuery.i18n.prop("h1PowerOffRouter");
    document.getElementById("lQueryPowerOffRouter").innerHTML = jQuery.i18n.prop("lQueryPowerOffRouter");
}

// 全局函数，处理确认关闭
function OnPowerOffOK() {
    //console.log('确认关闭函数被调用');
    hm();
    if (g_objContent && typeof g_objContent.onPost === 'function') {
        g_objContent.onPost();
    }
}

// 全局函数，处理取消关闭
function onPowerOffCancel() {
    //console.log('取消关闭函数被调用');
    hm();
}


