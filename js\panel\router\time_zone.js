(function ($) {
    $.fn.objTimeZone = function (InIt) {
        var timezoneStringArray =  new Array(2);
        timezoneStringArray[0] = new Array();
        timezoneStringArray[1] = new Array();
        var indexString;
        var xmlName = '';
        var xmlTimeZone;
        var xml;
        var item=0;
        var mapData;
        this.onLoad = function () {

            //GetMachineTimezone();
            var index = 0;
            this.loadHTML();
            document.getElementById("title").innerHTML = jQuery.i18n.prop(InIt);
            xml = getData(xmlName);
            xmlTimeZone = getTimeZoneData("tzdatabase.xml");
            var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);

            $(xml).find("timezone").each(function(){
                document.getElementById("ldeviceTimeZoneValue").innerHTML=$(this).find("name").text();
            });

            $(xmlTimeZone).find("Item").each(function(){
                var  tz_name =  $(this).find("tz_name").text();
                var  tz_str =  $(this).find("tz_str").text();
              
                timezoneStringArray[0][index] = tz_name;
                timezoneStringArray[1][index] = tz_str;
                var opt = document.createElement("option");
                document.getElementById("drpdwnTimeZone").options.add(opt);
                opt.text = tz_name;
                opt.value = index;
                index++;
            });
          
       
        }
        this.putMapElement = function(xpath,value,index){
            mapData[index]=new Array(2);
            mapData[index][0]=xpath;
            mapData[index][1]=value;
        }
        this.onPost  =  function  () {
            mapData=null;
            mapData = new Array();
            if(document.getElementById("getConnDevTimeZone").checked==true){
                if(indexString!=-1){
                    if(item != indexString){
                        this.putMapElement("RGW/locale/timezone/string",timezoneStringArray[1][indexString],0);
                        this.putMapElement("RGW/locale/timezone/name",timezoneStringArray[0][indexString],1);
                        if(mapData.length>0){
                            postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
                            //this.onLoad();
                        }
                    }
                    item = indexString;
                }
            }
            if(document.getElementById("setConnDevTimeZone").checked==true){
                var obj = document.getElementById("drpdwnTimeZone");
                var ind = obj.options[obj.selectedIndex].value;
                if(item!=ind){
                    this.putMapElement("RGW/locale/timezone/string",timezoneStringArray[1][ind],0);
                    this.putMapElement("RGW/locale/timezone/name",timezoneStringArray[0][ind],1);
                    if(mapData.length>0){
                        postXML(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
                        //this.onLoad();
                    }
                }
                item=ind;
            }
         
        }
        this.onPostSuccess = function (){
        this.onLoad(false);
        }
        this.setXMLName = function (_xmlname){
            xmlName = _xmlname;
        }
        this.loadHTML = function() {
            document.getElementById('Content').innerHTML = "";
            document.getElementById('Content').innerHTML = callProductHTML("html/router/time_zone.html");
        }
        this.getTimeZoneArray = function(){
            return timezoneStringArray;
        }
        this.setMachineTimezone = function()
        {
            var i = GetMachineTimezoneGmtOffset();
            var gmtOffset = GetMachineTimezoneGmtOffsetStr(i);
            var dstStr = GetMachineTimezoneDstStartStr(i);
		
   
            indexString =  setConnectedDeviceTimezoneStr(gmtOffset,dstStr,timezoneStringArray);
            if(indexString!=-1)
                document.getElementById("lConnectedDeviceTimeZone").innerHTML=timezoneStringArray[0][indexString];
            else
                document.getElementById("lConnectedDeviceTimeZone").innerHTML=jQuery.i18n.prop("ErrTimeZoneNotFound");

        }
        return this.each(function () {
           
           
            });
    }
})(jQuery);
function rbTimeZoneCicked(){
    if(document.getElementById("getConnDevTimeZone").checked==true){
        g_objContent.setMachineTimezone();
    }

}
