/**
 * 全局电池监控器
 * 确保电池信息在所有界面都能正常显示和更新
 */

(function() {
    'use strict';
    
    // 全局电池监控器配置
    var GlobalBatteryMonitor = {
        updateInterval: 2000,
        
        intervalId: null,
        
        lastBatteryInfo: {
            percentage: 0,
            isCharging: false,
            voltage: '未知'
        },
        
        initialized: false,
        
        init: function() {
            if (this.initialized) {
                return;
            }
            
            this.initialized = true;
            
            this.updateBatteryInfo();
            
            this.startMonitoring();
            
            this.setupPageChangeListener();
        },
        
        // 启动监控
        startMonitoring: function() {
            var self = this;
            
            if (this.intervalId) {
                clearInterval(this.intervalId);
            }
            
            this.intervalId = setInterval(function() {
                self.updateBatteryInfo();
            }, this.updateInterval);
            
            
            setTimeout(function() {
                self.syncChargingStatusFromMainPage();
            }, 1000);
        },
        
        syncChargingStatusFromMainPage: function() {
            try {
                var chargingElement = document.getElementById('chargingStatus');
                if (chargingElement && chargingElement.textContent) {
                    var content = chargingElement.textContent;
                    var isCharging = content.indexOf('充电中') > -1 || 
                                   content.indexOf('充电') > -1 || 
                                   content.indexOf('Charging') > -1;
                    
                    if (isCharging !== this.lastBatteryInfo.isCharging) {
                        this.lastBatteryInfo.isCharging = isCharging;
                        this.updateBatteryDOM(this.lastBatteryInfo.percentage, isCharging, this.lastBatteryInfo.voltage);
                    }
                }
            } catch (error) {
            }
        },
        
        // 停止监控
        stopMonitoring: function() {
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
        },
        
        // 更新电池信息
        updateBatteryInfo: function() {
            try {
                
                var chargingElement = document.getElementById('chargingStatus');
                var batteryElement = document.getElementById('batteryLevel');
                
                
                if (chargingElement && batteryElement) {
                    var chargingText = chargingElement.textContent || '';
                    var batteryText = batteryElement.textContent || '0%';
                    
                    var isCharging = chargingText.indexOf('充电中') > -1 || chargingText.indexOf('Charging') > -1;
                    var percentMatch = batteryText.match(/(\d+)%/);
                    var percentage = percentMatch ? parseInt(percentMatch[1]) : 0;

                    this.forceUpdateAllBatteryDisplays(percentage, isCharging);
                    return;
                }
                
                
                if (this.getBatteryFromDashboardXML()) return;
                if (this.getBatteryFromDOM()) return;
                this.getBatteryFromAPI();
                
            } catch (error) {
            }
        },
        
        forceUpdateAllBatteryDisplays: function(percentage, isCharging) {
            
            var batteryElements = ['batteryLevel'];
            batteryElements.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    var oldText = element.textContent;
                    element.textContent = percentage + '%';
                }
            });
            
            var chargingElements = ['chargingStatus'];
            chargingElements.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    var chargingText = isCharging ? '充电中' : '未充电';
                    if (typeof jQuery !== 'undefined' && jQuery.i18n && jQuery.i18n.prop) {
                        var translatedCharging = jQuery.i18n.prop('lCharging') || jQuery.i18n.prop('charging');
                        var translatedNotCharging = jQuery.i18n.prop('lUncharged') || jQuery.i18n.prop('not_charging');
                        chargingText = isCharging ? 
                            (translatedCharging || '充电中') : 
                            (translatedNotCharging || '未充电');
                    }
                    var oldText = element.textContent;
                    element.textContent = chargingText;
                }
            });
            
            var batteryLevelVisual = document.getElementById('batteryLevelVisual');
            if (batteryLevelVisual) {
                batteryLevelVisual.style.width = percentage + '%';
                batteryLevelVisual.className = 'battery-level';
                
                if (isCharging) {
                    batteryLevelVisual.classList.add('charging');
                } else if (percentage >= 60) {
                    batteryLevelVisual.classList.add('high');
                } else if (percentage >= 30) {
                    batteryLevelVisual.classList.add('medium');
                } else {
                    batteryLevelVisual.classList.add('low');
                }
            }
            
            this.lastBatteryInfo = {
                percentage: percentage,
                isCharging: isCharging,
                voltage: percentage + 'V'
            };
            
        },
        
        getBatteryFromDashboardXML: function() {
            try {
                if (typeof window.g_batteryInfo !== 'undefined' && window.g_batteryInfo) {
                    this.updateBatteryDisplay(
                        window.g_batteryInfo.percentage,
                        window.g_batteryInfo.isCharging,
                        window.g_batteryInfo.voltage
                    );
                    return true;
                }
                
                if (typeof window.g_Battery_connect !== 'undefined' && window.g_Battery_connect !== '0') {
                    var percentage = parseInt(window.g_Battery_voltage) || 0;
                    var isCharging = (window.g_Battery_charging === "1" || window.g_Battery_charge === "1" || window.g_Battery_charge === "2");
                    var voltage = window.g_Battery_voltage + 'V';
                    
                    this.updateBatteryDisplay(percentage, isCharging, voltage);
                    return true;
                }
                
                if (typeof xml !== 'undefined' && xml) {
                    var batteryCharging = $(xml).find("Battery_charging").text();
                    var batteryCharge = $(xml).find("Battery_charge").text();
                    var batteryVoltage = $(xml).find("Battery_voltage").text();
                    var batteryConnect = $(xml).find("Battery_connect").text();
                    
                    if (batteryConnect && batteryConnect !== '0') {
                        var percentage = parseInt(batteryVoltage) || 0;
                        var isCharging = (batteryCharging === "1" || batteryCharge === "1" || batteryCharge === "2");
                        
                        this.updateBatteryDisplay(percentage, isCharging, batteryVoltage + 'V');
                        return true;
                    }
                }
            } catch (error) {
            }
            return false;
        },
        
        getBatteryFromDOM: function() {
            try {
                var percentage = this.lastBatteryInfo.percentage;
                var isCharging = this.lastBatteryInfo.isCharging;
                var voltage = this.lastBatteryInfo.voltage;
                var hasUpdate = false;
                
                var batteryElements = [
                    'pDashBetteryInfo',
                    'lDashBatteryQuantity', 
                    'batteryLevel'
                ];
                
                for (var i = 0; i < batteryElements.length; i++) {
                    var element = document.getElementById(batteryElements[i]);
                    if (element && element.textContent) {
                        var content = element.textContent;
                        var percentMatch = content.match(/(\d+)%/);
                        
                        if (percentMatch && percentMatch[1]) {
                            percentage = parseInt(percentMatch[1]);
                            hasUpdate = true;
                        }
                    }
                }
                
                var chargingElement = document.getElementById('chargingStatus');
                if (chargingElement && chargingElement.textContent) {
                    var content = chargingElement.textContent;
                    isCharging = content.indexOf('充电中') > -1 || 
                               content.indexOf('充电') > -1 || 
                               content.indexOf('Charging') > -1;
                    hasUpdate = true;
                } else {
                    var otherChargingElements = ['lDashChargeStatus'];
                    for (var j = 0; j < otherChargingElements.length; j++) {
                        var element = document.getElementById(otherChargingElements[j]);
                        if (element && element.textContent) {
                            var content = element.textContent;
                            isCharging = content.indexOf('充电中') > -1 || 
                                       content.indexOf('充电') > -1 || 
                                       content.indexOf('Charging') > -1;
                            hasUpdate = true;
                            break;
                        }
                    }
                }
                
                if (hasUpdate) {
                    this.updateBatteryDisplay(percentage, isCharging, voltage);
                    return true;
                }
                
            } catch (error) {
            }
            return false;
        },
        
        getBatteryFromAPI: function() {
            try {
                if (typeof getData === 'function') {
                    var data = getData('');
                    if (data) {
                        this.parseBatteryFromXML(data);
                    }
                }
            } catch (error) {
            }
        },
        
        parseBatteryFromXML: function(xmlData) {
            try {
                if (xmlData) {
                    var batteryCharging = $(xmlData).find("Battery_charging").text();
                    var batteryCharge = $(xmlData).find("Battery_charge").text();
                    var batteryVoltage = $(xmlData).find("Battery_voltage").text();
                    var batteryConnect = $(xmlData).find("Battery_connect").text();
                    
                    if (batteryConnect && batteryConnect !== '0') {
                        var percentage = parseInt(batteryVoltage) || 0;
                        var isCharging = (batteryCharging === "1" || batteryCharge === "1" || batteryCharge === "2");
                        
                        this.updateBatteryDisplay(percentage, isCharging, batteryVoltage + 'V');
                    }
                }
            } catch (error) {
            }
        },
        
        updateBatteryDisplay: function(percentage, isCharging, voltage) {
            this.lastBatteryInfo = {
                percentage: percentage,
                isCharging: isCharging,
                voltage: voltage
            };
            
            this.updateBatteryDOM(percentage, isCharging, voltage);
            
            if (typeof window.setDashboardBatteryInfo === 'function') {
                window.setDashboardBatteryInfo(percentage, isCharging, voltage);
            }
                        if (typeof updateHeaderBatteryInfo === 'function') {
                updateHeaderBatteryInfo(percentage, isCharging, voltage);
            }
            
        },
        
        updateBatteryDOM: function(percentage, isCharging, voltage) {
            try {
                var batteryLevelElement = document.getElementById('batteryLevel');
                if (batteryLevelElement) {
                    batteryLevelElement.textContent = percentage + '%';
                }
                
                var chargingStatusElement = document.getElementById('chargingStatus');
                if (chargingStatusElement) {
                    var chargingText = isCharging ? '充电中' : '未充电';
                    if (typeof jQuery !== 'undefined' && jQuery.i18n && jQuery.i18n.prop) {
                        chargingText = isCharging ? 
                            (jQuery.i18n.prop('lCharging') || jQuery.i18n.prop('charging') || '充电中') : 
                            (jQuery.i18n.prop('lUncharged') || jQuery.i18n.prop('not_charging') || '未充电');
                    }
                    chargingStatusElement.textContent = chargingText;
                }
                
                // 更新电池图标
                var batteryLevelVisual = document.getElementById('batteryLevelVisual');
                if (batteryLevelVisual) {
                    batteryLevelVisual.style.width = percentage + '%';
                    batteryLevelVisual.className = 'battery-level';
                    
                    if (isCharging) {
                        batteryLevelVisual.classList.add('charging');
                    } else if (percentage >= 60) {
                        batteryLevelVisual.classList.add('high');
                    } else if (percentage >= 30) {
                        batteryLevelVisual.classList.add('medium');
                    } else {
                        batteryLevelVisual.classList.add('low');
                    }
                }
            } catch (error) {
            }
        },
        
        setupPageChangeListener: function() {
            var self = this;
            
            var lastUrl = location.href;
            var urlCheckInterval = setInterval(function() {
                var currentUrl = location.href;
                if (currentUrl !== lastUrl) {
                    lastUrl = currentUrl;
                    
                    setTimeout(function() {
                        if (!self.intervalId) {
                            self.startMonitoring();
                        }
                        self.updateBatteryInfo();
                    }, 1000);
                }
            }, 1000);
            
            var observer = new MutationObserver(function(mutations) {
                var shouldUpdate = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                if (node.id && (node.id.indexOf('battery') > -1 || node.id.indexOf('Battery') > -1)) {
                                    shouldUpdate = true;
                                    break;
                                }
                                var batteryElements = node.querySelectorAll && node.querySelectorAll('[id*="battery"], [id*="Battery"]');
                                if (batteryElements && batteryElements.length > 0) {
                                    shouldUpdate = true;
                                    break;
                                }
                            }
                        }
                    }
                });
                
                if (shouldUpdate) {
                    setTimeout(function() {
                        self.updateBatteryInfo();
                    }, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },
        
        forceUpdate: function() {
            this.updateBatteryInfo();
        },
        
        getCurrentBatteryInfo: function() {
            return this.lastBatteryInfo;
        }
    };
    
    window.GlobalBatteryMonitor = GlobalBatteryMonitor;
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                GlobalBatteryMonitor.init();
            }, 2000);
        });
    } else {
        setTimeout(function() {
            GlobalBatteryMonitor.init();
        }, 2000);
    }
    
    window.addEventListener('beforeunload', function() {
        GlobalBatteryMonitor.stopMonitoring();
    });
    
})();