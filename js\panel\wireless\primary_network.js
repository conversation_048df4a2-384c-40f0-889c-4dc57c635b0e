var rbWiFiSetup = null;
var wps_enable;
var _net_mode;
var _strSecurityType = '';
var IsWpsMatch = false;
var g_strEncryptionMode;
var g_strWpa2PskPasswd="";
var g_strMixedPasswd="";

(function($) {

    $.fn.objWire_Sec = function(InIt) {

        var _controlMapExisting = new Array(0);
        var _controlMapCurrent = new Array(0);
        var _xmlname = '';
        var strSSID = '';
        // var _radEDNw = null;
        var _radVINwStatus = null;
        var _radKeyType = null;
        var ssid_bcast = '';
        var _xml = '';
        var _cipher = '';
		var ssv_wifi6;
        this.onLoad = function(flag) {
            if (flag) {
                this.loadHTML();               
            }
			this.addRadios();
            buttonLocaliztion(document.getElementById("btUpdate").id);
            document.getElementById("title").innerHTML = jQuery.i18n.prop(InIt);
            $("#lWpsCfgBtn").val(jQuery.i18n.prop("lWpsCfgBtn"));
            this.dispalyAllNone();
            _xml = getData(_xmlname);
            var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);
            var optionElements = document.getElementsByTagName("option");
            pElementLocaliztion(optionElements);

            $(_xml).find("wlan_settings").each(function() {
                _net_mode = $(_xml).find("net_mode").text();
				ssv_wifi6 = $(_xml).find("ssv_wifi6").text();
            });

            _strSecurityType = this.getMode();

            strSSID = $(_xml).find("ssid").text();
            document.getElementById("tbSSID").value = UniDecode(strSSID);

            var wapiSupport  = $(_xml).find("wapi_support").text();
            if(0 == wapiSupport)
            {
                $("#dropdownWAPI").hide();
            }
            else
            {
                $("#dropdownWAPI").show();
            }

			if (1 == ssv_wifi6) {
				$("#dropdownWAPI").hide();
				//$("#dropdownWEP").hide();
				if ($("#drpdwnSecurityType option").length < 5)
				{
				    $("#drpdwnSecurityType").append("<option id='dropdownWPA3' value='WPA3-SAE'> WPA3-SAE </option>");
					$("#drpdwnSecurityType").append("<option id='dropdownWPA2WPA3' value='WPA2-WPA3'> WPA2-WPA3 </option>");
				}
			}

            ssid_bcast = $(_xml).find("ssid_bcast").text();
            _radVINwStatus.setRadioButton(ssid_bcast);

			
            //�л���������ģʽ�����WPA2-PSK��MIXED ����
            //���ȱ������룬������뱻�����ʾ���������
            $(_xml).find("WPA2-PSK").each(function() {
                strPass = $(this).find("key").text();
				if("" != strPass)
                {
                    g_strWpa2PskPasswd = strPass;
                }          
            });
			$(_xml).find("Mixed").each(function() {
                strPass = $(this).find("key").text();
				if("" != strPass)
                {
                    g_strMixedPasswd = strPass;
                }          
            });
           
            document.getElementById("drpdwnSecurityType").value = _strSecurityType;
            g_strEncryptionMode = _strSecurityType;

            switch (_strSecurityType) {
                case 'WPA2-PSK': {
                    if(1 == ssid_bcast) {
                        $("#divWpsCfgDlg").show();
                    } else {
                        $("#divWpsCfgDlg").hide();
                    }
                    this.loadWPA2_PSKData("WPA2-PSK");
                    break;
                }
                case 'Mixed': {
                    if(1 == ssid_bcast) {
                        $("#divWpsCfgDlg").show();
                    } else {
                        $("#divWpsCfgDlg").hide();
                    }
                    this.loadMixedData("Mixed");
                    break;
                }
                case 'WPA-PSK': {
                    if(1 == ssid_bcast) {
                        $("#divWpsCfgDlg").show();
                    } else {
                        $("#divWpsCfgDlg").hide();
                    }
                    this.loadWPA_PSKData("WPA-PSK");
                    break;
                }
                case 'None': {
                    if(1 == ssid_bcast) {
                        $("#divWpsCfgDlg").show();
                    } else {
                        $("#divWpsCfgDlg").hide();
                    }
                    this.loadDisabledData();
                    break;

                }
                case 'WEP': {
                    $("#divWpsCfgDlg").hide();
                    this.loadWEPData();
                    break;
                }
                case 'WAPI-PSK': {
                    $("#divWpsCfgDlg").hide();
                    this.loadWAPI_PSKData();
                    break;
                }
				case 'WPA2-WPA3':
				case 'WPA3-SAE': {
					if(1 == ssid_bcast) {
                        $("#divWpsCfgDlg").show();
                    } else {
                        $("#divWpsCfgDlg").hide();
                    }
                    this.loadWPA3_SAEData("WPA3-SAE");
                    break;
				}
            }

            this.copyControlArray();
        }
        this.clearControlArray = function() {
            _controlMapExisting = null;
            _controlMapCurrent = null;
            _controlMapExisting = new Array(0);
            _controlMapCurrent = new Array(0);
        }
        this.copyControlArray = function() {
            _controlMapCurrent = g_objXML.copyArray(_controlMapExisting, _controlMapCurrent);
        }
        this.onPost = function(flag) {
            if (this.isValid()) {
                document.getElementById('lPassErrorMesPN').style.display = 'none';
                var _controlMap = this.getPostData();
                if (_controlMap.length > 0) {


                    if (flag) {
                        postXML(_xmlname, g_objXML.getXMLDocToString(g_objXML.createXML(_controlMap)));
                        //this.onLoad();
                    } else {
                        return _controlMap;
                    }

                }

            }
            return _controlMap;
        }
        this.onPostSuccess = function() {
            this.onLoad(false);
        }
        this.clearOption = function() {
            document.getElementById("divCipher").innerHTML = '<select id="drpdwnCipher" onchange="changedCiperSetting()"></select>';
        }
        this.getPostData = function() {

            var mode = document.getElementById("drpdwnSecurityType").value;
            switch (mode) {
                case 'WPA2-PSK': {
                    return this.getWPA2_PSKData();
                    break;
                }
                case 'Mixed': {
                    return this.getMixedData();
                    break;
                }
                /*  case 'WPA-PSK': {
                      return this.getWPA_PSKData();
                      break;
                  }*/
                case 'None': {
                    return this.getDisabledData();
                    break;

                }
                case 'WEP': {
                    return this.getWEPData();
                    break;
                }
                case 'WAPI-PSK': {
                    return this.getWAPI_PSKData();
                    break;
                }
				case 'WPA2-WPA3':
				case 'WPA3-SAE': {
					return this.getWPA3_SAEData();
                    break;
				}
            }
        }
        this.isValid = function() {
            var strSelected = document.getElementById("drpdwnSecurityType").value;
            if (strSelected == 'WPA2-PSK' || strSelected == 'Mixed' ||strSelected == 'WPA3-SAE' || strSelected == 'WPA2-WPA3'/*|| strSelected == 'WPA-PSK'*/) {
                if (isChineseChar($("#tbpass").val())) {
                    document.getElementById('lPassErrorMesPN').style.display = 'block';
                    document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lChineseCharError');
                    return false;

                }

                if (isChineseChar($("#tbpassText").val())) {
                    document.getElementById('lPassErrorMesPN').style.display = 'block';
                    document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lChineseCharError');
                    return false;

                }
                if (document.getElementById("tbpass").style.display == "block") {
                    if (!(textBoxMinLength("tbpass", 8) && textBoxMinLength("tbre_password", 8))) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lminLengthError8');
                        return false;
                    }
                }
                if (document.getElementById("tbpassText").style.display == "block")
                    if (!(textBoxMinLength("tbpassText", 8) && textBoxMinLength("tbre_passwordtext", 8))) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lminLengthError8');
                        return false;
                    }
                if (document.getElementById("tbpass").style.display == "block")
                    if (!(textBoxMaxLength("tbpass", 63) && textBoxMaxLength("tbre_password", 63))) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lmaxLengthError64');
                        return false;
                    }
                if (document.getElementById("tbpassText").style.display == "block")
                    if (!(textBoxMaxLength("tbpassText", 63) && textBoxMaxLength("tbre_passwordtext", 63))) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lmaxLengthError64');
                        return false;
                    }
            }
            if (strSelected == 'WEP') {
                var pass = '';
                var value = document.getElementById("tbpass").value;
                if (document.getElementById("tbpassText").style.display == "block")
                    value = document.getElementById("tbpassText").value;

                if (document.getElementById("drpdwnEncryType").value == '0') {
                    if (document.getElementById("tbpassText").style.display == "block") {
                        if (!(textBoxLength("tbpassText", 5))) {
                            document.getElementById('lPassErrorMesPN').style.display = 'block';
                            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError5');
                            return false;
                        }
                    } else {
                        if (!(textBoxLength("tbpass", 5))) {
                            document.getElementById('lPassErrorMesPN').style.display = 'block';
                            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError5');
                            return false;
                        }
                    }

                } else if (document.getElementById("drpdwnEncryType").value == '1') {
                    var re1 = /^[0-9a-fA-F]{10}$/;
                    if (!re1.test(value)) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lHexPasswdLength10Error');
                        return false;

                    } else {
                        pass = value;
                        document.getElementById("tbpass").value = pass;
                        document.getElementById("tbpassText").value = pass;
                    }
                } else if (document.getElementById("drpdwnEncryType").value == '2') {
                    if (document.getElementById("tbpassText").style.display == "block") {
                        if (!(textBoxLength("tbpassText", 13))) {
                            document.getElementById('lPassErrorMesPN').style.display = 'block';
                            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError13');
                            return false;
                        }
                    } else {
                        if (!(textBoxLength("tbpass", 13))) {
                            document.getElementById('lPassErrorMesPN').style.display = 'block';
                            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError13');
                            return false;
                        }
                    }
                } else if (document.getElementById("drpdwnEncryType").value == '3') {
                    var re3 = /^[0-9a-fA-F]{26}$/;
                    if (!re3.test(value)) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lHexPasswdLength26Error');
                        return false;

                    } else {
                        pass = value;
                        document.getElementById("tbpass").value = pass;
                        document.getElementById("tbpassText").value = pass;
                    }
                }

            }
            if (strSelected == 'WAPI-PSK') {
                var pass = '';
                var value = _radKeyType.getRadioButton();
                if (value == '0') {
                    var re1 = /^[0-9]{8,64}$/;
                    var value1 = document.getElementById("tbpass").value;
                    if (document.getElementById("tbpassText").style.display == "block")
                        value1 = document.getElementById("tbpassText").value;

                    if (!re1.test(value1)) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError8_64');
                        return false;
                    }
                    value1 = document.getElementById("tbre_password").value;
                    if (document.getElementById("tbre_passwordtext").style.display == "block")
                        value1 = document.getElementById("tbpassText").value;

                    if (!re1.test(value1)) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError8_64');
                        return false;
                    }
                } else {
                    var re0 = /^[0-9a-fA-F]{8,64}$/;
                    var value1 = document.getElementById("tbpass").value;
                    if (document.getElementById("tbpassText").style.display == "block")
                        value1 = document.getElementById("tbpassText").value;

                    if (!re0.test(value1)) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError8_64hex');
                        return false;
                    }
                    value1 = document.getElementById("tbre_password").value;
                    if (document.getElementById("tbre_passwordtext").style.display == "block")
                        value1 = document.getElementById("tbpassText").value;

                    if (!re0.test(value1)) {
                        document.getElementById('lPassErrorMesPN').style.display = 'block';
                        document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lLengthError8_64hex');
                        return false;
                    }
                }
            }
            return true;
        }

        this.addRadios = function() {
            //_radEDNw =$("#nwRadio").enabled_disabled("nwRadio");
            _radVINwStatus = $("#nwRadiovisi").visible_invisible("nwRadiovisi");
            //   rbWiFiSetup = $("#rbWiFiSetup").enabled_disabled("rbWiFiSetup");
            _radKeyType = $("#RadioAH").ascii_hex("RadioAH");
            var c_rdRadio1 = document.getElementById('nwRadiovisiVisible');
            var c_rdRadio2 = document.getElementById('nwRadiovisiInvisible');
            c_rdRadio1.onclick = HideSsidClicked;
            c_rdRadio2.onclick = HideSsidClicked;
        }

        this.loadHTML = function() {
            document.getElementById('Content').innerHTML = "";
            document.getElementById('Content').innerHTML = callProductHTML("html/wireless/primary_network.html");
        }
        this.setXMLName = function(xmlname) {
            _xmlname = xmlname;
        }
        this.getMode = function() {
            var mode = "";
            try {
                var modeElements = $(_xml).find("mode");
                if (modeElements && modeElements.length > 0) {
                    if (navigator.appName.indexOf("Microsoft") != -1) {
                        mode = modeElements[0].text || "";
                    } else {
                        // FIREFOX or others
                        mode = modeElements[0].textContent || "";
                    }
                } else {
                    ////console.warn("未找到mode元素");
                }
            } catch (e) {
                ////console.error("获取mode时出错:", e);
            }
            return mode;
        }
        this.getNetMode = function() {
            var net_mode = "";
            try {
                var netModeElements = $(_xml).find("net_mode");
                if (netModeElements && netModeElements.length > 0) {
                    if (navigator.appName.indexOf("Microsoft") != -1) {
                        net_mode = netModeElements[0].text || "";
                    } else {
                        // FIREFOX or others
                        net_mode = netModeElements[0].textContent || "";
                    }
                } else {
                    ////console.warn("未找到net_mode元素");
                }
            } catch (e) {
                ////console.error("获取net_mode时出错:", e);
            }
            return net_mode;
        }


        this.loadCommonSecurity = function(type) {
            var status = '';
            var strPass = '';
            var strCipher = '';
            var index = 0;
            var mode = "";
            displayBlock("divCipher");
            displayBlock("lpass");
            displayBlock("lwpa");
            displayBlock("lunmaskpass");
            displayBlock("chkUnmask");
            displayBlock("tbpass");
            mode = this.getMode();
            $(_xml).find(type).each(function() {
                strPass = $(this).find("key").text();
                strCipher = $(this).find("mode").text();
            });

            //�л���������ģʽ�����WPA2-PSK��MIXED ����
            //���ȱ������룬������뱻�����ʾ���������
            if("WPA2-PSK" == type)
            {
                if("" != strPass)
                {
                    g_strWpa2PskPasswd = strPass;
                }
                else
                {
                    strPass = g_strWpa2PskPasswd;
                }
            }
            if("Mixed" == type)
            {
                if("" != strPass)
                {
                    g_strMixedPasswd = strPass;
                }
                else
                {
                    strPass = g_strMixedPasswd;
                }                
            }
            
            document.getElementById("tbpass").maxLength = 63;
            document.getElementById("tbpassText").maxLength = 63;
            document.getElementById("tbre_password").maxLength = 63;
            document.getElementById("tbre_passwordtext").maxLength = 63;
            document.getElementById("tbpass").value = strPass;
            document.getElementById("tbre_password").value = strPass;
            document.getElementById("tbpassText").value = strPass;
            document.getElementById("tbre_passwordtext").value = strPass;
            document.getElementById("drpdwnCipher").value = strCipher;

            //   _controlMapExisting = g_objXML.putMapElement(_controlMapExisting,index++, "RGW/wlan_security/wps_enable", wps_enable);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid", strSSID);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid_bcast", ssid_bcast);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/mode", mode);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/" + type + "/key", strPass);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/" + type + "/mode", strCipher);
        }
        this.addOption = function(id, text, value) {
            var opt = document.createElement("option");
            document.getElementById(id).options.add(opt);
            opt.text = text;
            opt.value = value;

        }
        this.loadWPA2_PSKData = function(type) {
            clearPasswordCheckBox();
			$("#drpdwnCipher").empty();
            if (_net_mode == 0) {
                this.addOption('drpdwnCipher', jQuery.i18n.prop('AES_Stronger'), 'AES-CCMP');
            } else {
                this.addOption('drpdwnCipher', jQuery.i18n.prop('TKIP_Strong'), 'TKIP');
                this.addOption('drpdwnCipher', jQuery.i18n.prop('AES_Stronger'), 'AES-CCMP');

                $(_xml).find("WPA-PSK").each(function() {
                    _cipher = $(this).find("mode").text();
                });

                if (_cipher == "TKIP") {
                    document.getElementById("drpdwnCipher").selectedIndex = 0;
                } else {
                    document.getElementById("drpdwnCipher").selectedIndex = 1;
                }
            }
            this.loadCommonSecurity(type);
        }

        this.loadWPA_PSKData = function(type) {
            clearPasswordCheckBox();
			$("#drpdwnCipher").empty();
            if (_net_mode == 0) {
                this.addOption('drpdwnCipher', jQuery.i18n.prop('AES_Stronger'), 'AES-CCMP');
            } else {
                this.addOption('drpdwnCipher', jQuery.i18n.prop('TKIP_Strong'), 'TKIP');
                this.addOption('drpdwnCipher', jQuery.i18n.prop('AES_Stronger'), 'AES-CCMP');

                $(_xml).find("WPA-PSK").each(function() {
                    _cipher = $(this).find("mode").text();
                });

                if (_cipher == "TKIP") {
                    document.getElementById("drpdwnCipher").selectedIndex = 0;
                } else {
                    document.getElementById("drpdwnCipher").selectedIndex = 1;
                }
            }
            this.loadCommonSecurity(type);
        }
        this.loadMixedData = function(type) {
            clearPasswordCheckBox();
			$("#drpdwnCipher").empty();
            this.addOption('drpdwnCipher', 'WPA-TKIP/WPA2-AES', 'AES-CCMP');
            this.loadCommonSecurity(type);

        }
        this.loadDisabledData = function() {
            var index = 0;
            var mode = '';

            mode = this.getMode();
            //_controlMapExisting = g_objXML.putMapElement(_controlMapExisting,index++, "RGW/wlan_security/wps_enable", wps_enable);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid", strSSID);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid_bcast", ssid_bcast);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/mode", mode);
        }
        this.loadWAPI_PSKData = function() {
            var index = 0;
            var mode = '';
            var wapi_key_type;
            var wapi_key;
            clearPasswordCheckBox();
            displayBlock("div_wapi");
            displayBlock("chkUnmask");
            displayBlock("lpass");
            displayBlock("tbpass");
            displayBlock("lunmaskpass");

            $(_xml).find("WAPI-PSK").each(function() {
                wapi_key_type = $(this).find("key_type").text();
                wapi_key = $(this).find("key").text();
            });
            document.getElementById("tbpass").maxLength = 63;
            document.getElementById("tbpassText").maxLength = 63;
            document.getElementById("tbre_password").maxLength = 63;
            document.getElementById("tbre_passwordtext").maxLength = 63;
            document.getElementById("tbpass").value = wapi_key;
            document.getElementById("tbre_password").value = wapi_key;
            document.getElementById("tbpassText").value = wapi_key;
            document.getElementById("tbre_passwordtext").value = wapi_key;

            _radKeyType.setRadioButton(wapi_key_type);

            mode = this.getMode();

            //  _controlMapExisting = g_objXML.putMapElement(_controlMapExisting,index++, "RGW/wlan_security/wps_enable", wps_enable);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid", strSSID);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid_bcast", ssid_bcast);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/mode", mode);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/WAPI-PSK/key_type", wapi_key_type);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/WAPI-PSK/key", wapi_key);
        }

        this.loadWEPData = function() {
            clearPasswordCheckBox();
            displayBlock("lAuth");
            displayBlock("drpdwnAuthType");
            displayBlock("lEncryption");
            displayBlock("drpdwnEncryType");
            displayBlock("chkUnmask");
            displayBlock("lpass");
            displayBlock("tbpass");
            displayBlock("lunmaskpass");

            var strPass = '';
            var strAuth = '';
            var strEncrypt = '';
            var status = '';
            var mode;
            var index = 0;
            mode = this.getMode();
            $(_xml).find("WEP").each(function() {
                strPass = $(this).find("key1").text();
                strAuth = $(this).find("auth").text();
                strEncrypt = $(this).find("encrypt").text();
            });
            document.getElementById("tbpass").maxLength = 28;
            document.getElementById("tbpassText").maxLength = 28;
            document.getElementById("tbre_password").maxLength = 28;
            document.getElementById("tbre_passwordtext").maxLength = 28;
            document.getElementById("tbpass").value = strPass;
            document.getElementById("tbre_password").value = strPass;
            document.getElementById("tbpassText").value = strPass;
            document.getElementById("tbre_passwordtext").value = strPass;

            document.getElementById("drpdwnAuthType").value = strAuth;
            document.getElementById("drpdwnEncryType").value = strEncrypt;
            //  _controlMapExisting = g_objXML.putMapElement(_controlMapExisting,index++, "RGW/wlan_security/wps_enable", wps_enable);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid", strSSID);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/ssid_bcast", ssid_bcast);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/mode", mode);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/WEP/key1", strPass);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/WEP/auth", strAuth);
            _controlMapExisting = g_objXML.putMapElement(_controlMapExisting, index++, "RGW/wlan_security/WEP/encrypt", strEncrypt);
        }

		this.loadWPA3_SAEData = function(type) {
            clearPasswordCheckBox();
			$("#drpdwnCipher").empty();
            this.addOption('drpdwnCipher', jQuery.i18n.prop('AES_Stronger'), 'AES-CCMP');

            this.loadCommonSecurity(type);
        }
				
        this.getCommonData = function() {
            var index = 0;
            var mapData = new Array(0);
            //  _controlMapCurrent[index++][1] = rbWiFiSetup.getRadioButton();
            _controlMapCurrent[index++][1] = UniEncode(document.getElementById("tbSSID").value);
            _controlMapCurrent[index++][1] = _radVINwStatus.getRadioButton();
            _controlMapCurrent[index++][1] = document.getElementById("drpdwnSecurityType").value;
            if (document.getElementById("tbpass").style.display == "block")
                _controlMapCurrent[index++][1] = document.getElementById("tbpass").value;
            if (document.getElementById("tbpassText").style.display == "block")
                _controlMapCurrent[index++][1] = document.getElementById("tbpassText").value;
            _controlMapCurrent[index++][1] = document.getElementById("drpdwnCipher").value;
            mapData = g_objXML.copyArray(_controlMapCurrent, mapData);
            mapData = g_objXML.getChangedArray(_controlMapExisting, mapData, true);
            return mapData;
        }
        this.getWPA2_PSKData = function() {
            return this.getCommonData();
        }

        this.getWPA_PSKData = function() {
            return this.getCommonData();
        }
        this.getMixedData = function() {
            return this.getCommonData();
        }
		this.getWPA3_SAEData = function() {
            return this.getCommonData();
        }
		
        this.getDisabledData = function() {
            var mapData = new Array(0);
            var index = 0;
            //_controlMapCurrent[index++][1] = rbWiFiSetup.getRadioButton();
            _controlMapCurrent[index++][1] = UniEncode(document.getElementById("tbSSID").value);
            _controlMapCurrent[index++][1] = _radVINwStatus.getRadioButton();
            _controlMapCurrent[index++][1] = "None";
            mapData = g_objXML.copyArray(_controlMapCurrent, mapData);
            mapData = g_objXML.getChangedArray(_controlMapExisting, mapData, true);
            return mapData;
        }
        this.getWAPI_PSKData = function() {
            var mapData = new Array(0);
            var index = 0;
            // _controlMapCurrent[index++][1] = rbWiFiSetup.getRadioButton();
            _controlMapCurrent[index++][1] = UniEncode(document.getElementById("tbSSID").value);
            _controlMapCurrent[index++][1] = _radVINwStatus.getRadioButton();
            _controlMapCurrent[index++][1] = document.getElementById("drpdwnSecurityType").value;
            _controlMapCurrent[index++][1] = _radKeyType.getRadioButton();
            if (document.getElementById("tbpass").style.display == "block")
                _controlMapCurrent[index++][1] = document.getElementById("tbpass").value;
            if (document.getElementById("tbpassText").style.display == "block")
                _controlMapCurrent[index++][1] = document.getElementById("tbpassText").value;
            mapData = g_objXML.copyArray(_controlMapCurrent, mapData);
            mapData = g_objXML.getChangedArray(_controlMapExisting, mapData, true);
            return mapData;
        }
        this.getWEPData = function() {
            var index = 0;
            var mapData = new Array(0);
            //_controlMapCurrent[index++][1] = rbWiFiSetup.getRadioButton();
            _controlMapCurrent[index++][1] = UniEncode(document.getElementById("tbSSID").value);
            _controlMapCurrent[index++][1] = _radVINwStatus.getRadioButton();
            _controlMapCurrent[index++][1] = document.getElementById("drpdwnSecurityType").value;
            if (document.getElementById("tbpass").style.display == "block")
                _controlMapCurrent[index++][1] = document.getElementById("tbpass").value;
            if (document.getElementById("tbpassText").style.display == "block")
                _controlMapCurrent[index++][1] = document.getElementById("tbpassText").value;
            _controlMapCurrent[index++][1] = document.getElementById("drpdwnAuthType").value;
            _controlMapCurrent[index++][1] = document.getElementById("drpdwnEncryType").value;
            mapData = g_objXML.copyArray(_controlMapCurrent, mapData);
            mapData = g_objXML.getChangedArray(_controlMapExisting, mapData, true);
            return mapData;

        }

        this.dispalyAllNone = function() {
            document.getElementById("lAuth").style.display = "none";
            document.getElementById("drpdwnAuthType").style.display = "none";
            document.getElementById("lEncryption").style.display = "none";
            document.getElementById("drpdwnEncryType").style.display = "none";
            document.getElementById("tbpass").style.display = "none";
            document.getElementById('chkUnmask').style.display = 'none';
            document.getElementById('lpass').style.display = 'none';
            document.getElementById('chkUnmask').style.display = 'none';
            document.getElementById('lunmaskpass').style.display = 'none';
            document.getElementById('lRetypePassword').style.display = 'none';
            document.getElementById('tbre_password').style.display = 'none';
            document.getElementById('lwpa').style.display = 'none';
            document.getElementById('divCipher').style.display = 'none';
            document.getElementById('tbpassText').style.display = 'none';
            document.getElementById('tbre_passwordtext').style.display = 'none';
            document.getElementById('lPassErrorMesPN').style.display = 'none';
            document.getElementById('div_wapi').style.display = 'none';
        }

        return this.each(function() {
        });
    }
})(jQuery);

function HideSsidClicked() {
    if (document.getElementById("nwRadiovisiInvisible").checked) {
        var ret = confirm(jQuery.i18n.prop("lWpsDisabledInSsidHideStatus"));
        if(ret) {
            $("#divWpsCfgDlg").hide();
        } else {
            document.getElementById("nwRadiovisiVisible").checked = true;
        }

    } else {
        $("#divWpsCfgDlg").show();
    }
}



function ShowWPSCfgDlg() {
    sm("MBAddWPSClient", 300, 200);
    localizeWPSDialog();
	$("#lWPSStatus").text("");

	_WPSXML = callProductXML("uapxb_wlan_security_settings");
    var WPSStatus = $(_WPSXML).find("wps_status").text();
	if(1 != WPSStatus && "" != WPSStatus){
		EnabledWPSBtn();
		IsWpsMatch = false;
	}
    if(IsWpsMatch) {
        DisabledWPSBtn();
    }
}

function DisabledWPSBtn(){
	$("#btnRegister").attr("disabled", true);
    $("#btnPush").attr("disabled", true);
    $("#txtRouterPinMB").attr("disabled", true);
    $("#divcancel_session").show();
}

function EnabledWPSBtn(){
	$("#btnRegister").attr("disabled", false);
    $("#btnPush").attr("disabled", false);
    $("#txtRouterPinMB").attr("disabled", false);
    $("#divcancel_session").hide();
}

function QueryWpsStatus() {

    _WPSXML = callProductXML("uapxb_wlan_security_settings");
    var WPSStatus = $(_WPSXML).find("wps_status").text();


	if(1 != WPSStatus && "" != WPSStatus){
		EnabledWPSBtn();
		IsWpsMatch = false;
	}
	
    if (WPSStatus == 0) {
        $("#lWPSStatus").text("");
    } else if ((WPSStatus == 1 || "" == WPSStatus) && IsWpsMatch) {
    	 $("#lWPSStatus").text(jQuery.i18n.prop("lWpsMatchPro"));
         setTimeout(QueryWpsStatus, 3000);
    } else if (WPSStatus == 2) {    	
        $("#lWPSStatus").text(jQuery.i18n.prop("lWpsMatchSuccess"));
		showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsMatchSuccess"));
    } else if (WPSStatus == 3) {    
        $("#lWPSStatus").text(jQuery.i18n.prop("lWpsMatchFailed"));
		 showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsMatchFailed"));
    } else if (WPSStatus == 4) {    	
        $("#lWPSStatus").text(jQuery.i18n.prop("lWpsMatchInterrupt"));
		showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsMatchInterrupt"));
    } else if (WPSStatus == 5) {    
        $("#lWPSStatus").text(jQuery.i18n.prop("lWpsPinCheckFail"));
		showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsPinCheckFail"));
    } else {
        $("#lWPSStatus").text("Unkown Error.");
    }

}

function QueryWpsStatusEx() {

    _WPSXML = callProductXML("uapxb_wlan_security_settings");
    var WPSStatus = $(_WPSXML).find("wps_status").text();
    if (WPSStatus == 1) {
        setTimeout("QueryWpsStatusEx", 1000);
    }  else if (WPSStatus == 3) {
        showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsMatchFailed"));
    } else if (WPSStatus == 4) {
        showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsMatchInterrupt"));
    } else if (WPSStatus == 5) {
        showMsgBox(jQuery.i18n.prop("lWarning"),jQuery.i18n.prop("lWpsPinCheckFail"));
    }
}


function localizeWPSDialog() {
    $("#h1AddWPSClient").text(jQuery.i18n.prop("h1AddWPSClient"));
    $("#lWPStext").text(jQuery.i18n.prop("lWPStext"));
    $("#lCancelWpsSession").text(jQuery.i18n.prop("lCancelWpsSession"));
    $("#spanWPSPushButton").text(jQuery.i18n.prop("spanWPSPushButton"));
    $("#spanEnterPin").text(jQuery.i18n.prop("spanEnterPin"));
    $("#btnPush").val(jQuery.i18n.prop("btnPush"));
    $("#btnRegister").val(jQuery.i18n.prop("btnRegister"));
    $("#btnClose").val(jQuery.i18n.prop("btnClose"));

}

function btnPushClicked() {
    var mapData = new Array(0);
    mapData = putMapElement(mapData, "RGW/wlan_security/WPS/connect_method", 1, 0);
    postXML("uapxb_wlan_security_settings", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));   
	IsWpsMatch = true;
    setTimeout(QueryWpsStatus, 3000);
}
function btnRegisterClicked() {
    var mapData = new Array(0);
    var client_pin = $("#txtRouterPinMB").val();
    if("-" == client_pin.substr(4,1)) {
        client_pin = client_pin.replace("-","");
    }

    if ((client_pin.length == 8 || client_pin.length == 4)&& Number(client_pin)) {
        mapData = putMapElement(mapData, "RGW/wlan_security/WPS/connect_method", 2,0);
        mapData = putMapElement(mapData, "RGW/wlan_security/WPS/wps_pin", client_pin,1);
		DisabledWPSBtn();
        postXML("uapxb_wlan_security_settings", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
        IsWpsMatch = true;
        setTimeout(QueryWpsStatus, 3000);
    } else {
        $("#lWPSError").show();
        $("#lWPSError").text(jQuery.i18n.prop("lWPSPinError"));
    }
}


function cancelSessionClicked() {
    var mapData = new Array(0);
    mapData = putMapElement(mapData,"RGW/wlan_security/WPS/connect_method", 3,0);
    postXML("uapxb_wlan_security_settings", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));

}

function WPSClientClose() {
    hm();
}

function changedCiperSetting() {
    var linkObj = document.getElementById("drpdwnCipher");
    var value = linkObj.options[linkObj.selectedIndex].value;
    if("TKIP" == value) {
        if($("#divWpsCfgDlg").show())
            $("#divWpsCfgDlg").hide()
        }

    if("AES-CCMP" == value) {
        if($("#divWpsCfgDlg").hide()) {
            var linkObj = document.getElementById("drpdwnSecurityType");
            var value = linkObj.options[linkObj.selectedIndex].value;
            switch (value) {

                case 'WPA2-PSK': {
                    $("#divWpsCfgDlg").show();
                    break;
                }

                case 'WPA-PSK': {
                    $("#divWpsCfgDlg").show();
                    break;
                }

				case 'WPA2-WPA3':
				case 'WPA3-SAE': {
					$("#divWpsCfgDlg").show();
                    break;
                }
            }

        }
    }
}



function RouterPinMBChange() {
    document.getElementById("lWPSError").style.display = "none";
}

function clearPasswordCheckBox() {
    document.getElementById("chkUnmask").checked = false;
    document.getElementById("tbpassText").style.display = "none";
    document.getElementById("tbre_passwordtext").style.display = "none"
}

function changedSecuritySetting() {

    var linkObj = document.getElementById("drpdwnSecurityType");
    var value = linkObj.options[linkObj.selectedIndex].value;
    g_objContent.dispalyAllNone();
    g_objContent.clearOption();
    g_objContent.clearControlArray();
    switch (value) {
        case 'WPA2-PSK': {
            //$("#divWpsCfgDlg").show();
            g_objContent.loadWPA2_PSKData("WPA2-PSK");
            break;
        }
        case 'Mixed': {
            //$("#divWpsCfgDlg").show();
            g_objContent.loadMixedData("Mixed");
            break;
        }
        case 'WPA-PSK': {
            //$("#divWpsCfgDlg").show();
            g_objContent.loadWPA_PSKData("WPA-PSK");
            break;
        }
        case 'None': {
            var ret = confirm(jQuery.i18n.prop("lNoneEncrypModeTip"));
            if(!ret) {
                $("#drpdwnSecurityType").val(g_strEncryptionMode);
                changedSecuritySetting();
            }
            //$("#divWpsCfgDlg").show();
            g_objContent.loadDisabledData();
            break;
        }
        case 'WEP': {

            if (_net_mode == 0) {
                showAlert(jQuery.i18n.prop("lnoWEPfor11n"));
                document.getElementById("drpdwnSecurityType").value = _strSecurityType;
                changedSecuritySetting();
            } else {
                var ret = confirm(jQuery.i18n.prop("lWpsDisabledInWepMode"));
                if(ret) {
                    $("#divWpsCfgDlg").hide();
                } else {
                    $("#drpdwnSecurityType").val(g_strEncryptionMode);
                    changedSecuritySetting();
                }
                g_objContent.loadWEPData();
            }

            break;
        }
        case 'WAPI-PSK': {
            $("#divWpsCfgDlg").hide();
            g_objContent.loadWAPI_PSKData();
            break;
        }
		case 'WPA3-SAE': {
            g_objContent.loadWPA3_SAEData("WPA3-SAE");
            break;
        }
    case 'WPA2-WPA3': {
            g_objContent.loadWPA3_SAEData("WPA2-WPA3");
            break;
        }
    }
    g_objContent.copyControlArray();
    g_strEncryptionMode = $("#drpdwnSecurityType").val();

}
function unmaskPassword() {
    var strPass = '';
    var strRepass = '';
    if (document.getElementById("chkUnmask").checked) {
        if (document.getElementById("tbpass").style.display == "block" || document.getElementById("tbpassText").style.display == "block") {
            strPass = document.getElementById("tbpass").value;
            document.getElementById("tbpassText").style.display = "block";
            document.getElementById("tbpass").style.display = "none";
            document.getElementById("tbpassText").value = strPass;
        }
        if (document.getElementById("tbre_password").style.display == "block" || document.getElementById("tbre_passwordtext").style.display == "block") {
            strRepass = document.getElementById("tbre_password").value;
            document.getElementById("tbre_passwordtext").style.display = "block";
            document.getElementById("tbre_password").style.display = "none";
            document.getElementById("tbre_passwordtext").value = strRepass;
        }

    } else {
        if (document.getElementById("tbpass").style.display == "block" || document.getElementById("tbpassText").style.display == "block") {
            strPass = document.getElementById("tbpassText").value;
            document.getElementById("tbpass").style.display = "block";
            document.getElementById("tbpassText").style.display = "none";
            document.getElementById("tbpass").value = strPass;
        }
        if (document.getElementById("tbre_password").style.display == "block" || document.getElementById("tbre_passwordtext").style.display == "block") {
            strRepass = document.getElementById("tbre_passwordtext").value;
            document.getElementById("tbre_password").style.display = "block";
            document.getElementById("tbre_passwordtext").style.display = "none";
            document.getElementById("tbre_password").value = strRepass;
        }


    }
}
function lengthInUtf8Bytes(str){
	// Matches only the 10.. bytes that are non-initial characters in a multi-byte sequence.
	var m = encodeURIComponent(str).match(/%[89ABab]/g);
	return str.length +(m ? m.length :0);
}
function validateSecurityPassword() {
	if (lengthInUtf8Bytes(document.getElementById("tbSSID").value) > 32) {
		return;
	}
    if (document.getElementById('tbpass').style.display == "block") {
        if (document.getElementById('tbpass').value != document.getElementById('tbre_password').value) {
            document.getElementById('lPassErrorMesPN').style.display = 'block';
            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lPassErrorMes');
            document.getElementById("tbre_password").value = '';
        } else {
            document.getElementById('lPassErrorMesPN').style.display = 'none';
            setData();
        }
    } else {
        if (document.getElementById('tbre_passwordtext').value != document.getElementById('tbpassText').value) {
            document.getElementById('lPassErrorMesPN').style.display = 'block';
            document.getElementById('lPassErrorMesPN').innerHTML = jQuery.i18n.prop('lPassErrorMes');
            document.getElementById("tbre_passwordtext").value = '';
        } else {
            document.getElementById('lPassErrorMesPN').style.display = 'none';
            setData();
        }
    }
}
function securityPasswordChanged() {
    if (!document.getElementById("chkUnmask").checked) {
        document.getElementById("tbre_password").value = '';
        document.getElementById('lRetypePassword').style.display = 'block';
        document.getElementById('tbre_password').style.display = 'block';
    } else {
        document.getElementById("tbre_passwordtext").value = '';
        document.getElementById('lRetypePassword').style.display = 'block';
        document.getElementById('tbre_passwordtext').style.display = 'block';

    }
}
function checkSsidLength(maxLength){
	var inputLen = lengthInUtf8Bytes(document.getElementById("tbSSID").value);
	if (inputLen > maxLength) {
		$("#lssidError").show();
		$("#lssidError").text(jQuery.i18n.prop("lssidlenError"));
	} else {
		$("#lssidError").hide();
	}
}
