var devEnv=0;

// 请求管理器
var requestManager = {
    activeRequests: new Map(),
    requestCounter: 0,
    
    addRequest: function(xhr, id) {
        this.activeRequests.set(id || ++this.requestCounter, xhr);
        return id || this.requestCounter;
    },
    
    removeRequest: function(id) {
        this.activeRequests.delete(id);
    },
    
    cancelAll: function() {
        this.activeRequests.forEach(function(xhr) {
            if (xhr && xhr.readyState !== 4) {
                xhr.abort();
            }
        });
        this.activeRequests.clear();
    }
};

/* This function sends xmldata as a string to thr server by
 * using ajax post call.
 * parameters are XML Name and xml Data as as string.
 * on success it returns the respoce XML which is call posted
 */

function postXML(xmlName,xmlData,timeOutInterval,bHideWaitBox) {
//document.getElementById("loadingdivimage").style.display = "block";
	if(undefined == bHideWaitBox) 
		bHideWaitBox = false;
    if(timeOutInterval==undefined)
        timeOutInterval = 360000;
    if(xmlName != "locale" && !bHideWaitBox) {
        sm("PleaseWait",150,100);
        $("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));
    }
    resetInterval();
    var url = "";   
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=set&module=duster&file='+xmlName;
    if(devEnv=="1")
        alert(xmlData);
    
    var requestId = requestManager.requestCounter + 1;
    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            requestManager.addRequest(xhr, requestId);
            xhr.setRequestHeader("Authorization",getAuthHeader("POST"))
        },
    url: url,
    processData: false,
    data: xmlData,
    async: true,
    dataType: "xml",
    timeout: timeOutInterval,
    success:function(data, textStatus) {    		
    		if(xmlName != "locale" && !bHideWaitBox) {
                hm();
            }
			
            if($(data).find("login_status").text() == "UNAUTHORIZED") {
                if (typeof window.setManualLogout === 'function') {
                    window.setManualLogout();
                }
                clearAuthheader();
            }
        },
    complete: function(XMLHttpRequest, textStatus) {
            requestManager.removeRequest(requestId);
            if(xmlName != "locale" && !bHideWaitBox) {
                hm();
            }
             if(null == g_InternetConnectionObj && null == g_PrimaryNetworkObj){
                if (g_objContent && typeof g_objContent.onPostSuccess === 'function') {
                    g_objContent.onPostSuccess();
                }
             }
          
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            requestManager.removeRequest(requestId);
            if(xmlName != "locale" && !bHideWaitBox) {
                hm();
            }
            // 忽略被取消的请求错误
            if(textStatus === "abort") {
                return;
            }
            // alert("In Error:" + textStatus);
            if(textStatus!="timeout")
                showAlert(jQuery.i18n.prop('lErrorPost'));
            else
                showAlert(jQuery.i18n.prop('lErrorTimeOut'));
        }
    });

    return true;
}


function PostSyncXMLEx(xmlName, xmlData,callbackFun) {
    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=set&module=duster&file=' + xmlName;
    resetInterval();
    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("POST"))
        },
    complete: function(XMLHttpRequest, textStatus) {
            hm();
			 if(callbackFun) {
                callbackFun();
            }
        },
    url: url,
    dataType: "xml",
    timeout: 60000,
    data: xmlData,
    async: false
    });
}

function webdav_postXML(xmlName,xmlData,timeOutInterval) {
//document.getElementById("loadingdivimage").style.display = "block";
    if(timeOutInterval==undefined)
        timeOutInterval = 360000;
    //if(xmlName != "locale") {
    //    sm("PleaseWait",150,100);
   //     $("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));
   // }
    resetInterval();
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=set&module=duster&file='+xmlName;
    if(devEnv=="1")
        alert(xmlData);
    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization",getAuthHeader("POST"))
        },
    url: url,
    processData: false,
    data: xmlData,
    async: true,
    dataType: "xml",
    timeout: timeOutInterval,
    success:function(data,textStatus) {
            showAlert(jQuery.i18n.prop("lsharesettingresultsuc"));
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
    		if(XMLHttpRequest.status==200)
    		{
    			showAlert(jQuery.i18n.prop("lsharesettingresultsuc"));
    		}
			else
			{
				showAlert(jQuery.i18n.prop("lsharesettingresultsuc"));
			}
        }
    });

    return true;
}

function postXMLEx(xmlName, xmlData, timeOutInterval, queryFun) {
    if (timeOutInterval == undefined)
        timeOutInterval = 360000;

    sm("PleaseWait", 150, 100);
    $("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));

    resetInterval();
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host + 'xml_action.cgi?method=set&module=duster&file=' + xmlName;

    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("POST"))
        },
    url: url,
    processData: false,
    data: xmlData,
    async: true,
    dataType: "xml",
    timeout: timeOutInterval,
    success:function(data, textStatus) {
            if($(data).find("login_status").text() == "UNAUTHORIZED") {
                clearAuthheader();
            }
        },
    complete: function(XMLHttpRequest, textStatus) {
            hm();
            setTimeout(queryFun, 5000);
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            hm();
            bIsScanNetwork = false;
            if (textStatus != "timeout")
                showAlert(jQuery.i18n.prop('lScanNetworkError'));
            else
                showAlert(jQuery.i18n.prop('lScanNetworkTimeOut'));
        }
    });

    return true;
}

function postXMLTimeset(xmlName, xmlData, timeOutInterval, queryFun) {
    if (timeOutInterval == undefined)
        timeOutInterval = 360000;

    sm("PleaseWait", 150, 100);
    $("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));

    resetInterval();
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host + 'xml_action.cgi?method=set&module=duster&file=' + xmlName;

    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("POST"))
        },
    url: url,
    processData: false,
    data: xmlData,
    async: true,
    dataType: "xml",
    timeout: timeOutInterval,
    complete: function(XMLHttpRequest, textStatus) {
            hm();
            setTimeout(queryFun, 500);
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            hm();
            if (textStatus != "timeout")
                showAlert(jQuery.i18n.prop('pFailedcompleteupdateTime'));
            else
                showAlert(jQuery.i18n.prop('pUpdateTimeOut'));
        }
    });
    return true;
}



function postXMLlocale(xmlName,xmlData,timeOutInterval) {
//document.getElementById("loadingdivimage").style.display = "block";
    if(timeOutInterval==undefined)
        timeOutInterval = 180000;
    resetInterval();
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=set&module=duster&file='+xmlName;
    if(devEnv=="1")
        alert(xmlData);
    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization",getAuthHeader("POST"))
        },
    url: url,
    processData: false,
    data: xmlData,
    async: true,
    dataType: "xml",
    timeout: timeOutInterval,
    success:function(data, textStatus) {
            if($(data).find("login_status").text() == "UNAUTHORIZED") {
                if (typeof window.setManualLogout === 'function') {
                    window.setManualLogout();
                }
                clearAuthheader();
            }
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            if(textStatus!="timeout")
                alert(jQuery.i18n.prop('lErrorPost'));
            else
                alert(jQuery.i18n.prop('lErrorTimeOut'));
        }
    });

    return true;
}

// 轻量级状态查询函数
function getBasicStatus(callback) {
    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=get&module=duster&file=status_basic';
    
    $.ajax({
        type: "GET",
        url: url,
        dataType: "xml",
        timeout: 10000,
        beforeSend: function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("GET"));
        },
        success: function(data) {
            if (callback) callback(data);
        },
        error: function() {
        }
    });
}

function getNetworkStatus(callback) {
    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=get&module=duster&file=status_network';
    
    $.ajax({
        type: "GET",
        url: url,
        dataType: "xml",
        timeout: 15000,
        beforeSend: function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("GET"));
        },
        success: function(data) {
            if (callback) callback(data);
        },
        error: function() {
        }
    });
}

function postLogout() {
    
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?Action=logout';
    
    $.ajax({
        type: "GET",
        url: url,
        dataType: "html",
        async: false,
        timeout: 5000,
        complete: function(xhr, status) {
            clearAuthheader();
        },
        error: function(xhr, status, error) {
            clearAuthheader();
        }
    });

    return true;
}
/* This function  returns HTML contect as a text to caller
 * Parameter is htmlpath path where the HTML file is Located
 * Returns RespoceText
 */
function callProductHTML(htmlName) {
    //setTimeout ( "calculateAuthheader()", 6);
    resetInterval();
    var content;
    if(username == "admin") {
        content = $.ajax( {
        type: "GET",
        url: htmlName,
        dataType: "html",
        timeout: 30000,
        async:false
        }).responseText;
    } else {
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
                if(null != g_objContent) {
                    g_objContent.onLoad(true);
                }
            },
        url: htmlName,
        dataType: "html",
        timeout: 30000,
        async:false
        }).responseText;
    }

    if(content.indexOf("E7683FTEFTA8HT08HFH09") > 0) {
        clearAuthheader();
        return null;
    } else
        return content;
}


function GetNetworkScanRetXml() {
    resetInterval();
    url = window.location.protocol + "//" + window.location.host + "/xml_action.cgi?method=get&module=duster&file=wan";
    var content =  $.ajax( {
    type: "GET",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
        },
    url: url,
    dataType: "xml",
    error:function() {
            if(bIsScanNetwork) {
                setTimeout(QueryScanResult, 15000);
            }
        },
    async:false
    }).responseXML;


    if($(content).find("login_status").text() == "UNAUTHORIZED") {
        clearAuthheader();
        return null;
    }

    return content;
}


/* This function get the XML from the server via ajax.
 *  Get is Method and return type is responceXML
 *
 */

function callProductXML(xmlName) {
    if(xmlName!="device_date")
        resetInterval();
    var url = "";
    var content;
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=get&module=duster&file='+xmlName;
    // alert(url);
    if(devEnv=="0") {
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        url: url,
        dataType: "xml",
        async:false
        }).responseXML;
    } else {
        xmlName="xml/"+xmlName+".xml";
        content = $.ajax( {
        type: "GET",
        url: xmlName,
        dataType: "xml",
        contentType: "text/xml;charset=UTF-8",
        async:false
        }).responseXML;
    }
    var login_text  = $(content).find("login_status").text();
    if(login_text == "UNAUTHORIZED") {
        clearAuthheader();
        return null;
    }

	if("KICKOFF" == $(content).find("login_status").text()){
		showAlert(jQuery.i18n.prop("lKickOffUser"));
		clearAuthheader();
        return null;
	}
	
    return content;
}

function callProductXML_webdavshare(xmlName) {
    if(xmlName!="device_date")
        resetInterval();
    var url = "";
    var content;
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=get&module=duster&file='+xmlName+Date.parse(new Date());
    // alert(url);
    if(devEnv=="0") {
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        url: url,
        dataType: "xml",
        async:false
        }).responseXML;
    } else {
        xmlName="xml/"+xmlName+".xml";
        content = $.ajax( {
        type: "GET",
        url: xmlName,
        dataType: "xml",
        contentType: "text/xml;charset=UTF-8",
        async:false
        }).responseXML;
    }
    var login_text  = $(content).find("login_status").text();
    return content;
}

function PostXMLWithResponse(xmlName, xmlData,callbackFun) {
    sm("PleaseWait", 150, 100);
    $("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));
    resetInterval();

    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=set&module=duster&file=' + xmlName;
    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("POST"))
        },
    url: url,
        timeout: 360000,
    dataType: "xml",
    data: xmlData,
    async: true,
    success:function(data, textStatus) {
            if($(data).find("login_status").text() == "UNAUTHORIZED") {
                clearAuthheader();
            }
            hm();
            if(null != callbackFun) {
                callbackFun(data);
            }
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            if(textStatus!="timeout")
                showAlert(jQuery.i18n.prop('lErrorPost'));
            else
                showAlert(jQuery.i18n.prop('lErrorTimeOut'));
        }
    });

}

function GetSmsXML(xmlName) {
    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=get&module=duster&file=' + xmlName;
    resetInterval();

    var content = $.ajax( {
    type: "GET",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("GET"))
        },
    url: url,
    dataType: "xml",
    timeout: 60000,
    async: false
    }).responseXML;

    if($(content).find("login_status").text() == "UNAUTHORIZED") {
        clearAuthheader();
        return null;
    }

    return content;
}


/* This method sets localization. It loads the Prpoerties file.
 * parameter are locale which is name of properties file
 * i.e. Message_en.properties is englist dict then parameter is en
 */
function setLocalization(locale) {

    if(locale != "en")
        locale = "cn";

    try {
        jQuery.i18n.properties( {
        name:'Messages',
        path:'properties/',
        mode:'map',
        language:locale,
        callback: function() {
            }
        });
    } catch(err) {
        var fileref=document.createElement('script');
        fileref.setAttribute("type","text/javascript");
        fileref.setAttribute("src", 'js/jquery/jquery.i18n.properties-1.0.4.js');
        document.getElementsByTagName("head")[0].appendChild(fileref);
        setLocalization(locale);
    }
}
/*
 * API used for url authentication digest checking.. it send url to server and
 * give responce to caller
 */
function authentication(url) {
    if(devEnv=='1') {
        return "200 OK";
    }
    var content = $.ajax( {
    url: url,
    dataType: "text/html",
    async:false,
    cache:false,
    beforeSend: function(xhr) {
            xhr.setRequestHeader("Authorization",getAuthHeader("GET"));
            xhr.setRequestHeader("Expires", "-1");
            xhr.setRequestHeader("Cache-Control","no-store, no-cache, must-revalidate");
            xhr.setRequestHeader("Pragma", "no-cache");
        }
    }).responseText;
    return content;
}
function getAuthType(url) {
    if(devEnv=='1') {
        return 'Digest realm="Highwmg", nonce="718337c309eacc5dc1d2558936225417", qop="auth" Content-Type: text/html Server: Lighttpd/1.4.19 Content-Length: 0 Date: Tue, 11 Oct 2005 10:44:32 GMT ';
    }
    var content = $.ajax( {
    url: url,
    type: "GET",
    dataType: "text/html",
    async:false,
    cache:false,
    beforeSend: function(xhr) {
            xhr.setRequestHeader("Expires", "-1");
            xhr.setRequestHeader("Cache-Control","no-store, no-cache, must-revalidate");
            xhr.setRequestHeader("Pragma", "no-cache");
        }
    }).getResponseHeader('WWW-Authenticate');
    return content;
}

function getVersionXML(xmlName) {
    var url = "";
    var content;
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+xmlName;
    if(devEnv=='1') {
        xmlName="xml/"+xmlName;

        content = $.ajax( {
        url: xmlName,
        dataType: "text/html",
        async:false
        }).responseText;
    } else {
        content = $.ajax( {
        url: url,
        dataType: "text/html",
        async:false
        }).responseText;
    }
    return content;
}
function getLastLoginXML(xmlName) {
    var url = "";
    var content;
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+xmlName;
    if(devEnv=='1') {
        xmlName="xml/"+xmlName;

        content = $.ajax( {
        url: xmlName,
        dataType: "text/html",
        async:false
        }).responseText;
    } else {
        content = $.ajax( {
        url: url,
        dataType: "text/html",
        async:false
        }).responseText;
    }
    return content;
}
function getTimeZoneData(xmlName) {
    resetInterval();
    var url = "";
    var content;
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+"/data/"+xmlName;

    if(devEnv=="0") {
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        url: url,
        dataType: "text/html",
        async:false
        }).responseXML;
    } else {
        xmlName="xml/"+xmlName;

        content = $.ajax( {
        type: "GET",
        url: xmlName,
        dataType: "text",
        async:false
        }).responseXML;

    }

    return content;
}
function callProductXMLNoDuster(xmlName) {
    resetInterval();
    var url = "";
    var content;
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=get&file='+xmlName;
    //  alert(url);
    if(devEnv=="0") {
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        url: url,
        dataType: "xml",
        async:false
        }).responseXML;
    } else {
        xmlName="xml/"+xmlName+".xml";
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        url: xmlName,
        dataType: "xml",
        async:false
        }).responseXML;

    }
    return content;
}
function execCommand(file,command,sFunction) {
    resetInterval();
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=get&file='+file+"&command="+command;
    if(devEnv=="1")
        return url;
    else {
        content = $.ajax( {
        type: "GET",
        'beforeSend': function(xhr) {
                xhr.setRequestHeader("Authorization",getAuthHeader("GET"))
            },
        url: url,
        dataType: "xml",
//            async:false,
        success: sFunction
        }).responseXML;
        return content;
    }
}



function PostSyncXML(xmlName, xmlData) {
    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=set&module=duster&file=' + xmlName;
    resetInterval();
    $.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("POST"))
        },
    complete: function(XMLHttpRequest, textStatus) {
            hm();
        },
    url: url,
    dataType: "xml",
    timeout: 60000,
    data: xmlData,
    async: false
    }).responseXML;
}

function GetSyncXML(xmlName) {
    var host = window.location.protocol + "//" + window.location.host + "/";
    var url = host + 'xml_action.cgi?method=get&module=duster&file=' + xmlName;

    var content = $.ajax( {
    type: "GET",
    'beforeSend': function(xhr) {
            xhr.setRequestHeader("Authorization", getAuthHeader("GET"))
        },
    url: url,
    dataType: "xml",
    timeout: 60000,
    async: false
    }).responseXML;


    if($(content).find("login_status").text() == "UNAUTHORIZED") {
        clearAuthheader();
        return null;
    }

    return content;

}

function WebDav_PropfindSyncXML(xmlName, xmlData) {
    var host = window.location.protocol + "//" + window.location.host;
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "PROPFIND",
    'beforeSend': function(xhr) {
        xhr.setRequestHeader("Authorization",webdav_getAuthHeader("PROPFIND"));
        xhr.setRequestHeader("Depth","1");
    },
    url: url,
    dataType: "xml",
    contentType: "text/xml;charset=UTF-8",
    //timeout: 60000,
    data: xmlData,
    async: false
    }).responseText;

	//var login_text  = $(content).find("login_status").text();
	return content;
	
}
function WebDav_Options() {
    var host = window.location.protocol + "//" + window.location.host;
	var xmlNametemp=xmlName;
    var url = host + "/";
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "OPTIONS",
    'beforeSend': function(xhr) {
        xhr.setRequestHeader("Authorization",webdav_getAuthHeader("OPTIONS"));
    },
    url: url,
    dataType: "xml",
    contentType: "text/xml;charset=UTF-8",
    //timeout: 60000,
    //data: xmlData,
    async: false
    }).responseText;

	//var login_text  = $(content).find("login_status").text();
	return content;
	
}
function WebDav_Shared_PropfindSyncXML(xmlName, xmlData) {
    var host = window.location.protocol + "//" + window.location.host;
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "PROPFIND",
    'beforeSend': function(xhr) {
        xhr.setRequestHeader("Depth","1");
    },
    url: url,
    dataType: "xml",
    contentType: "text/xml;charset=UTF-8",
    //timeout: 60000,
    data: xmlData,
    async: false
    }).responseText;
	//var login_text  = $(content).find("login_status").text();
	return content;
	
}


function WebDav_GetSyncXML(xmlName,ContentType) {
	sm("PleaseWait", 150, 100);
	$("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));

    var host = window.location.protocol + "//" + window.location.host;
	//var xmlNametemp=encodeURIComponent(xmlName);
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "Get",
	'beforeSend': function(xhr) {
		xhr.setRequestHeader("Authorization",webdav_getAuthHeader("GET"));
        xhr.setRequestHeader("Content-Type", ContentType)
    },
    url: url,
    //dataType: "xml",
    //contentType: ContentType,
    //timeout: 60000,
    //data: xmlData,
    async: true,
    success:function(data, textStatus) {
            hm();
        },
    complete: function(XMLHttpRequest, textStatus) {
            hm();
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            hm();
        }
    }).responseXML;
	//var login_text  = $(content).find("login_status").text();
	
	return content;
	
}

function WebDav_Shared_GetSyncXML(xmlName,ContentType) {
	sm("PleaseWait", 150, 100);
	$("#lPleaseWait").text(jQuery.i18n.prop("h1PleaseWait"));

    var host = window.location.protocol + "//" + window.location.host;
	//var xmlNametemp=encodeURIComponent(xmlName);
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "Get",
	'beforeSend': function(xhr) {
        xhr.setRequestHeader("Content-Type", ContentType)
    },
    url: url,
    //dataType: "xml",
    //contentType: ContentType,
    //timeout: 60000,
    //data: xmlData,
    async: true,
    success:function(data, textStatus) {
            hm();
        },
    complete: function(XMLHttpRequest, textStatus) {
            hm();
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            hm();
        }
    }).responseXML;
	//var login_text  = $(content).find("login_status").text();
	return content;
	
}


function WebDav_DeleteSyncXML(xmlName) {
    var host = window.location.protocol + "//" + window.location.host;
	//var xmlNametemp=encodeURIComponent(xmlName);
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "DELETE",
	'beforeSend': function(xhr) {
        xhr.setRequestHeader("Authorization",webdav_getAuthHeader("DELETE"));
    },
    url: url,
    //dataType: "xml",
    //contentType: ContentType,
    //timeout: 60000,
    //data: xmlData,
    async: false
    }).responseXML;
	//var login_text  = $(content).find("login_status").text();
	return content;
	
}

function WebDav_Shared_DeleteSyncXML(xmlName) {
    var host = window.location.protocol + "//" + window.location.host;
	//var xmlNametemp=encodeURIComponent(xmlName);
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "DELETE",
	'beforeSend': function(xhr) {
    },
    url: url,
    //dataType: "xml",
    //contentType: ContentType,
    //timeout: 60000,
    //data: xmlData,
    async: false
    }).responseXML;
	//var login_text  = $(content).find("login_status").text();
	return content;
	
}


function WebDav_MkdirSyncXML(xmlName) {
    var host = window.location.protocol + "//" + window.location.host;
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "MKCOL",
	'beforeSend': function(xhr) {
        xhr.setRequestHeader("Authorization",webdav_getAuthHeader("MKCOL"));
    },
    url: url,
    //dataType: "xml",
    //contentType: ContentType,
    //timeout: 60000,
    //data: xmlData,
    async: false
    }).responseXML;
	//var login_text  = $(content).find("login_status").text();
	return content;
	
}


function WebDav_Shared_MkdirSyncXML(xmlName) {
    var host = window.location.protocol + "//" + window.location.host;
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    //resetInterval();
    content=$.ajax( {
    type: "MKCOL",
	'beforeSend': function(xhr) {
        //xhr.setRequestHeader("Authorization",getAuthHeader("MKCOL"));
    },
    url: url,
    //dataType: "xml",
    //contentType: ContentType,
    //timeout: 60000,
    //data: xmlData,
    async: false
    }).responseXML;
	//var login_text  = $(content).find("login_status").text();
	return content;
	
}


function WebDav_PutSyncXML(xmlName,FileType,FileDataFrom,FileDataTo,FileDataTotal,FileData) {
    var host = window.location.protocol + "//" + window.location.host ;
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    content=$.ajax( {
    type: "PUT",
	processData: false,
	contentType: false,
	xhr: function() {
		var xhr = $.ajaxSettings.xhr();
	 	if (!xhr.sendAsBinary) 
		{
	 		xhr.legacySend = xhr.send;
	 		xhr.sendAsBinary = function(string) {
	 		var bytes = Array.prototype.map.call(string, function(c) {
	 			return c.charCodeAt(0) & 0xff;
	 			});
	 			this.legacySend(new Uint8Array(bytes).buffer);
	 		};
	 	}
	 	xhr.send = xhr.sendAsBinary;
	 	return xhr;
	 },
	'beforeSend': function(xhr) {
		xhr.setRequestHeader("Authorization",webdav_getAuthHeader("PUT"));
        xhr.setRequestHeader("Content-Type", FileType);
		if(FileDataFrom!=0)
		{
			xhr.setRequestHeader("Content-Range", "bytes"+FileDataFrom+"-"+FileDataTo+"/"+FileDataTotal);
		}
    },
    
    url: url,
    //dataType: "xml",
    //timeout: 60000,
    data: FileData,
    async: true,
    success:function(data, textStatus) {
            //WebDav_Upload_Ondoing();
     	},
    complete: function(XMLHttpRequest, textStatus) {
            WebDav_Upload_Ondoing();
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            WebDav_Upload_Ondoing();
        }
    }).responseXML;
	return content;
	
}


function WebDav_Shared_PutSyncXML(xmlName,FileType,FileDataFrom,FileDataTo,FileDataTotal,FileData) {
    var host = window.location.protocol + "//" + window.location.host ;
	var xmlNametemp=xmlName;
    var url = host + xmlNametemp;
	var content;
    content=$.ajax( {
    type: "PUT",
	processData: false,
	contentType: false,
	xhr: function() {
		var xhr = $.ajaxSettings.xhr();
	 	if (!xhr.sendAsBinary) 
		{
	 		xhr.legacySend = xhr.send;
	 		xhr.sendAsBinary = function(string) {
	 		var bytes = Array.prototype.map.call(string, function(c) {
	 			return c.charCodeAt(0) & 0xff;
	 			});
	 			this.legacySend(new Uint8Array(bytes).buffer);
	 		};
	 	}
	 	xhr.send = xhr.sendAsBinary;
	 	return xhr;
	 },
	'beforeSend': function(xhr) {
		//xhr.setRequestHeader("Authorization",getAuthHeader("PUT"));
        xhr.setRequestHeader("Content-Type", FileType);
		if(FileDataFrom!=0)
		{
			xhr.setRequestHeader("Content-Range", "bytes"+FileDataFrom+"-"+FileDataTo+"/"+FileDataTotal);
		}
    },
    
    url: url,
    //dataType: "xml",
    //timeout: 60000,
    data: FileData,
    async: true,
    success:function(data, textStatus) {
            //WebDav_Upload_Ondoing();
     	},
    complete: function(XMLHttpRequest, textStatus) {
            WebDav_Shared_Upload_Ondoing();
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
            WebDav_Shared_Upload_Ondoing();
        }
    }).responseXML;
	return content;
	
}


function WebDav_PostSyncXML(xmlName, xmlData) {
    //var host = window.location.protocol + "//" + window.location.host + "/";
   // var url = host +  xmlName;
    var url = "";
    var host = window.location.protocol + "//" + window.location.host + "/";
    url = host+'xml_action.cgi?method=set&module=duster&file='+xmlName;
    content=$.ajax( {
    type: "POST",
    'beforeSend': function(xhr) {
        xhr.setRequestHeader("Authorization",getAuthHeader("POST"));
    },
    url: url,
    processData: false,
    dataType: "xml",
    contentType: "text/xml;charset=UTF-8",
    //timeout: 60000,
    data: xmlData,
    async: false,
    success:function(data,textStatus) {
            showAlert(jQuery.i18n.prop("lsharesettingresultsuc"));
        },
    error: function(XMLHttpRequest, textStatus, errorThrown) {
    		if(XMLHttpRequest.status==200)
    		{
    			showAlert(jQuery.i18n.prop("lsharesettingresultsuc"));
    		}
			else
			{
				showAlert(jQuery.i18n.prop("lsharesettingresultfal"));
			}
        }
    });
	return true;
	
}
