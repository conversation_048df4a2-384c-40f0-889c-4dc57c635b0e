<a href='#' class='help' onclick="getHelp('DHCPSettings')">&nbsp;</a>
   <label id="title" class="title"></label>
    <!--<label id='lDhcpSettings'>DHCP Settings</label><br /><br /><br />-->

    <style>
        .dhcp-container {
            margin-bottom: 20px;
        }
        .dhcp-row {
            margin-bottom: 10px;
            clear: both;
            display: flex;
            align-items: center;
        }
        .dhcp-label {
            min-width: 150px;
            display: inline-block;
            margin-right: 10px;
        }
        .dhcp-content {
            display: inline-flex;
            align-items: center;
        }
        .textfield1 {
            width: 50px;
            margin: 0 5px;
        }
        .dot {
            margin: 0 5px;
        }
        #ip.subttl {
            margin-left: 10px;
        }
        .form-section {
            margin: 0;
            padding: 24px 0;
            max-width: none;
        }
        .form-group {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
            padding: 24px 32px 16px 32px;
            margin-bottom: 28px;
            border: 1px solid #e6f7ff;
            margin-right: 0;
        }
        .form-group-title {
            font-size: 15px;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 18px;
            letter-spacing: 1px;
        }
        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
        }
        .form-row label {
            min-width: 80px;
            text-align: right;
            margin-right: 12px;
            font-size: 13px;
        }
        .form-row input[type="text"],
        .form-row select {
            width: 120px;
            height: 32px;
            font-size: 13px;
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            box-sizing: border-box;
        }
        @media (max-width: 600px) {
          .form-row input[type="text"],
          .form-row select {
            width: 50px;
          }
        }
        .ip-group {
            display: flex;
            align-items: center;
        }
        .ip-group input {
            width: 36px;
            text-align: center;
            margin-right: 4px;
            font-size: 13px;
            padding: 2px 4px;
        }
        .ip-group span {
            margin: 0 2px;
            font-weight: bold;
        }
        .form-row .radio-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        .form-row .radio-group label {
            min-width: unset;
            margin-right: 4px;
            text-align: left;
            font-size: 14px;
        }
        .form-row .radio-group input[type="radio"] {
            margin-right: 4px;
        }
        .pBoxCont {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
  padding: 24px 32px 16px 32px;
  border: 1px solid #e6f7ff;
  margin-bottom: 18px;
}
    </style>

<div class="form-section">
  <div class="form-group">
    <div class="form-group-title">DHCP 基本设置</div>
    <div class="form-row">
      <label id='lDhcpRange'></label>
      <div class="ip-group">
        <select id='drpdwn_DHCP_range' class='combo1' onchange='drpdwn_DHCP_rangeChanged()'>
          <option value='192.168.' selected>192.168.</option>
          <option value='10.0.' >10.0.</option>
          <option value='172.16.' >172.16.</option>
        </select>
        <span class='dot'>.</span>
        <input type='text' id='textbox3' maxlength='3' onchange='drpdwn_DHCP_rangeChanged()'/>
        <span class='dot'>.</span>
        <input type='text' id='textbox4' maxlength='3' onchange='drpdwn_DHCP_rangeChanged()'/>
      </div>
    </div>
    <div class="form-row">
      <label id='lDevLanIP'></label>
      <label id='ip' class="subttl"></label>
    </div>
    <div class="form-row">
      <label id='lDhcpServer'></label>
      <div id='rdRadio' class="radio-group"></div>
    </div>
  </div>
  <div class="form-group">
    <div class="form-group-title">地址池设置</div>
    <div class="form-row">
      <label id='lDhcpStartAdd'></label>
      <div class="ip-group">
        <input type="text" id="ipControl_starttext0" maxlength="3" /> <span class="dot">.</span>
        <input type="text" id="ipControl_starttext1" maxlength="3" /> <span class="dot">.</span>
        <input type="text" id="ipControl_starttext2" maxlength="3" /> <span class="dot">.</span>
        <input type="text" id="ipControl_starttext3" maxlength="3" />
      </div>
    </div>
    <div class="form-row">
      <label id='lDhcpEndAdd'></label>
      <div class="ip-group">
        <input type="text" id="ipControl_endtext0" maxlength="3" /> <span class="dot">.</span>
        <input type="text" id="ipControl_endtext1" maxlength="3" /> <span class="dot">.</span>
        <input type="text" id="ipControl_endtext2" maxlength="3" /> <span class="dot">.</span>
        <input type="text" id="ipControl_endtext3" maxlength="3" />
      </div>
    </div>
    <div class="form-row">
      <label id='ldhcplt'></label>
      <input type="text" id='tbdhcplt' maxlength='10' style="width:120px;" />
      <strong id='sTimeUint' style="margin-left:8px;">(in Seconds)</strong>
    </div>
  </div>
  <div class="form-group">
    <div class="form-group-title">DNS 设置</div>
    <div class="form-row">
      <label id="DNSEnableSwitchLabel"></label>
      <select id='DNSEnableSwitchSel' onchange='DNSEnableSwitchChanged()' style="width:160px;">
        <option id='ClosedivDNSEnableSwitch' value='0'>Disabled</option>
        <option id='OpendivDNSEnableSwitch' value='1'>Enabled</option>
      </select>
    </div>
    <div class="form-row">
      <div id="divEnabledDisabledContent" class="clear clearCrm"></div>
    </div>
    <div class="form-row">
      <label id='lDnsName1'></label>
      <input type="text" id='txtDnsName1' style="width:200px;" />
    </div>
    <div class="form-row">
      <label id='lDnsName2'></label>
      <input type="text" id='txtDnsName2' style="width:200px;" />
    </div>
  </div>
  <div class="form-group">
    <div class="form-group-title">重定向功能</div>
    <div class="form-row">
      <label id="lt_dhcp_stcRedirectionFunction">redirection function</label>
      <div class="radio-group">
        <input type="radio" name="RedirectionURL" id="RedirectionURLEnabled" onchange="RedirectionURLEnabledChange()"  onClick="this.blur();"/>
        <span id="lt_dhcp_stcRedirectionURLEnabled" style="margin-right:20px;"></span>
        <input type="radio" name="RedirectionURL" id="RedirectionURLDisabled" onchange="RedirectionURLDisabledChange()"  onClick="this.blur();"/>
        <span id="lt_dhcp_stcRedirectionURLDisabled"></span>
      </div>
    </div>
    <div class="form-row" id="divEnabledDirectionUrl" style="display:none;">
      <label id="lt_dhcp_stRedirectionURL"></label>
      <input type="text" id='txtDirectionURL' style="width:200px;" />
    </div>
  </div>
  <div class="form-group">
    <div class="form-group-title">静态IP列表</div>
    <div class="form-row" style="justify-content: flex-end;">
      <span class="btnWrp"><input id="btnAddStaticIP" type="button"  value="Add Static IP" onclick="addStaticIP()" /></span>
    </div>
    <div class="form-row">
      <label id="ltbStaticIP" ></label>
    </div>
    <div class="form-row">
      <table width="100%" id="tableStaticIP" border="0" cellspacing="0" cellpadding="0" class="dataTbl10 example table-stripeclass:alternate"  style="margin-top: 5px">
        <thead>
          <tr>
            <th width="49%" id="ltMacAddress"></th>
            <th width="49%" id="ltIPAddress"></th>
            <th class="close" width="2%">&nbsp;</th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
  </div>
  <div class="form-group">
    <div class="form-group-title">DHCPv6 设置</div>
    <div class="form-row">
      <label id="DHCPV6title" class="title"></label>
    </div>
    <div class="form-row">
      <label id="lDhcpV6Server"></label>
      <input type="radio" id="statelessServerRadio" name="DhcpV6ServerRadio"><span style="margin-right:40px;" id="lstatelessServer"></span>
      <input type="radio" id="statefullServerRadio" name="DhcpV6ServerRadio"><span id="lstatefullServer"></span>
    </div>
  </div>
  <div class="form-row" style="justify-content:center;">
    <label class='error' id='lIPErrorMsg'  style='display: none'></label>
  </div>
  <div class="form-row" style="justify-content:flex-end;">
    <span class="btnWrp"><input type='button' id='btUpdate' value='Save' onclick='saveDhcp()' /></span>
  </div>
</div>

<!-- 静态IP弹窗部分保持原样 -->
<div id='divStaticIPFull' align='center' class="popUpBox popUpBox2" style='display: none'>
</div>

<div id="MBStaticIPDlg" style="display: none" >
	   <div class="popUpBox popUpBox2" style='width:500px' >
            <h1 id="h1AddStaticIP"   style='width:450px;'></h1>
	    <a href="#" class="close" onclick="btnCancelClickedStaticIP()"><img src="images/close-icon.gif" alt="" /></a><br style="clear:both" />

            <div class="pBoxCont">
              <label id="lStaticIP_MAC"></label>
              <div class="boxCont2">  <input name="input" type="text" size="10" id="txtMac1" class="sml" maxlength="2" onkeyup='setFocus("txtMac1")'/> <strong>&middot;</strong>
                <input name="input" type="text" size="10" id="txtMac2" class="sml" maxlength="2" onkeyup='setFocus("txtMac2")'/> <strong>&middot;</strong>
                <input name="input" type="text" size="10" id="txtMac3" class="sml" maxlength="2" onkeyup='setFocus("txtMac3")'/> <strong>&middot;</strong>
                <input name="input" type="text" size="10" id="txtMac4" class="sml" maxlength="2" onkeyup='setFocus("txtMac4")'/> <strong>&middot;</strong>
                <input name="input" type="text" size="10" id="txtMac5" class="sml" maxlength="2" onkeyup='setFocus("txtMac5")'/> <strong>&middot;</strong>
                <input name="input" type="text" size="10" id="txtMac6" class="sml"/>
                <br style="clear:both" /></div>

                <label id="lStaticIP_IP"></label>
                <div class="boxCont2"> <input name="input" type="text" maxlength="3" size="3" id="txtSrcIPAddress1" class="sml" onkeyup='setFocusID("txtSrcIPAddress1","txtSrcIPAddress2")'/> <strong>&middot;</strong>
                <input name="input" type="text" maxlength="3" size="3" id="txtSrcIPAddress2" class="sml" onkeyup='setFocusID("txtSrcIPAddress2","txtSrcIPAddress3")'/> <strong>&middot;</strong>
                <input name="input" type="text" maxlength="3" size="3" id="txtSrcIPAddress3" class="sml" onkeyup='setFocusID("txtSrcIPAddress3","txtSrcIPAddress4")'/> <strong>&middot;</strong>
                <input name="input" type="text" maxlength="3" size="3" id="txtSrcIPAddress4" class="sml"/>
                 <br style="clear:both" /></div>
	        <label id="lMacIpError" class="lable13" style="display: none"></label>
		<div class="bottonRow1" style="border-top:1px solid #a7a7a7; padding:7px 0; margin:7px 0 0 0; text-align:right" >
                    <a href="#." id="btnCancel" onclick="btnCancelStaticIPSetting()" class="cancel">Cancel</a>
                    <span class="btnWrp"><input id="btnAdd_dhcp" type="button"  onclick="btnAddStaticIPSetting()" /></span>

                </div>
	      </div>
	</div>
     </div>
  
   <br style="clear:both" />

