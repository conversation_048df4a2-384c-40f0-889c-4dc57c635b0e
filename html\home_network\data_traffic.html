<label id="ltitle" class="title">
</label>
    <div id='divResetAllDevices' align='right' style='display: none'>        
            <span class="btnWrp">
            <input type="button" id="lBtnResetDevices" value="Reset All Clients" onclick="ResetClients()"></span>
    </div>
<table class="example table-autosort:0 table-stripeclass:alternate" id="tableDeviceInfo">
    <thead>
        <tr>
            <th width="20%" id="lClientName">
            </th>
            <th width="20%" id="lMacAddr">
            </th>
            <th width="22%" id="lConnTime">
            </th>
            <th width="15%" id="lStatus">
            </th>
            <th width="23%" id="lAction">
            </th>
        </tr>
    </thead>
    <tbody> 
    </tbody>
</table>
<div id="DeviceInfoDiv" style="display: none">
    <div class="popUpBox popUpBox2">
        <h1 id="lDeviceInfoBoxTitle">
        </h1>
        <a href="#" class="close" onclick="hm()">
            <img src="images/close-icon.gif" alt="" /></a><br style="clear: both" />
        <div class="pBoxCont">
            <label id="lDeviceName">
            </label>
            <input type="text" size="30" id="txtDeviceName" maxlength="25" disabled/>
            <br style="clear: both" />
            <label id="lNameType">
            </label>
            <select id="deviceNameAssignedSel" disabled>
                <option value="1" id="lNameTypeAssigned"></option>
                <option value="0" id="lNameTypeNotAssigned"></option>
            </select>
            <br style="clear: both" />
            <label id="lDeviceStatus">
            </label>
            <select id="deviceStatusSel" disabled>
                <option value="0" id="DisconnectedStatus"></option>
                <option value="1" id="ConnectedStatus"></option>
                <option value="2" id="BlockStatus"></option>
            </select>
            <br style="clear: both" />
            <label id="lConnType">
            </label>
            <select id="ConnTypeSel" disabled>
                <option value="USB">USB</option>
                <option value="WIFI">WIFI</option>
            </select>
            <br style="clear: both" />
            <label id="lIpAddr">
            </label>
            <input type="text" size="30" id="txtIpAddr" maxlength="25" disabled/>
            <br style="clear: both" />
            <label id="lMacAddress">
            </label>
            <input name="input" type="text" id="txtMacAddr" disabled/>
            <br style="clear: both" />
            <label id="lLastConTime">
            </label>
            <input name="input" type="text" id="txtLastConTime" disabled/>
            <br style="clear: both" />
            <label id="lTotalConTime">
            </label>
            <input name="input" type="text" id="txtTotalConTime" disabled/>
            <br style="clear: both" />
            <label id="lMonthSendData">
            </label>
            <input name="input" type="text" id="txtMonthSendData" disabled/>
            <br style="clear: both" />
            <label id="lMonthRecvData">
            </label>
            <input name="input" type="text" id="txtMonthRecvData" disabled/>
            <br style="clear: both" />
            <label id="lMonthTotalData">
            </label>
            <input name="input" type="text" id="txtMonthTotalData" disabled/>
            <br style="clear: both" />
            <label id="lLast3DaySendData">
            </label>
            <input name="input" type="text" id="txtLast3DaySendData" disabled/>
            <br style="clear: both" />
            <label id="lLast3DayRecvData">
            </label>
            <input name="input" type="text" id="txtLast3DayRecvData" disabled/>
            <br style="clear: both" />
            <label id="lLast3DayTotalData">
            </label>
            <input name="input" type="text" id="txtLast3DayTotalData" disabled/>
            <br style="clear: both" />
           <label id="lTotalSendData">
            </label>
            <input name="input" type="text" id="txtTotalSendData" disabled/>
            <br style="clear: both" />
            <label id="lTotalRecvData">
            </label>
            <input name="input" type="text" id="txtTotalRecvData" disabled/>
            <br style="clear: both" />
            <label id="lTotalData">
            </label>
            <input name="input" type="text" id="txtTotalData" disabled/>
            <br style="clear: both" />
            <div class="buttonRow1">
                <span class="btnWrp">
                    <input id="lBtnOk" type="button" value="OK" onclick="hm()"/></span>
            </div>
        </div>
    </div>
</div>
