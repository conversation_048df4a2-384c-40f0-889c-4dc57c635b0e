<!DOCTYPE HTML>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ZY 无线路由器</title>
        <link href="css/stylesheet.css" rel="stylesheet" type="text/css" />
        <link href="css/blue-white-theme.css" rel="stylesheet" type="text/css" />
        <!-- 核心JS文件 - 同步加载 -->
        <script type="text/javascript" src="js/jquery/jquery.js" language="javascript"></script>
        <!-- 关键JS文件预加载 -->
        <script type="text/javascript" src="js/panel/dashboard.js" language="javascript"></script>
        <script type="text/javascript">
            var g_platformName = "mifi";
            var scriptsLoaded = false;
            var appInitialized = false;
            
            if (typeof jQuery == 'undefined') { 
                window.location.reload(true);
            }
            
            // 显示加载指示器
            function showLoading() {
                var loadingDiv = document.getElementById('loadingIndicator');
                if (loadingDiv) loadingDiv.style.display = 'block';
            }
            
            function hideLoading() {
                var loadingDiv = document.getElementById('loadingIndicator');
                if (loadingDiv) loadingDiv.style.display = 'none';
            }
            
            // 异步加载JS文件的函数
            function loadScript(src) {
                return new Promise(function(resolve, reject) {
                    var script = document.createElement('script');
                    script.type = 'text/javascript';
                    script.src = src;
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }
            
            // 批量加载其他功能JS文件
            function loadOtherScripts() {
                if (scriptsLoaded) return Promise.resolve();
                
                var scripts = [
                    'js/library/jquery.form.js',
                    'js/library/download.jQuery.js',
                    'js/library/table.js',
                    'js/base/validator.js',
                    'js/controls/enabled_disabled.js',
                    'js/controls/ip_address.js',
                    'js/controls/visible_invisible.js',
                    'js/controls/allow_deny.js',
                    'js/controls/always_ondemand.js',
                    'js/controls/ascii_hex.js',
                    'js/panel/wireless/primary_network.js',
                    'js/panel/quick_setup.js',
                    'js/panel/internet/internet_connection.js',
                    'js/panel/router/user_management.js',
                    'js/panel/router/poweroff_router.js',
                    'js/panel/router/software_upgrade.js',
                    'js/panel/router/conf_management.js',
                    'js/panel/router/reboot_router.js',
                    'js/panel/router/card_management.js',
                    'js/panel/wireless/wireless_settings.js',
                    'js/panel/wireless/wireless_mac_filters.js',
                    'js/panel/home_network/network_activity.js',
                    'js/panel/home_network/dhcp_settings.js',
                    'js/panel/home_network/firewall_settings.js',
                    'js/panel/home_network/connected_device.js',
                    'js/panel/home_network/custom_fw.js',
                    'js/panel/home_network/port_forwarding.js',
                    'js/panel/home_network/port_filter.js',
                    'js/panel/home_network/data_traffic.js',
                    'js/panel/internet/manual_network.js',
                    'js/panel/internet/traffic_statistics.js',
                    'js/panel/internet/traffic_setting.js'
                ];
                
                return Promise.all(scripts.map(loadScript)).then(function() {
                    scriptsLoaded = true;
                });
            }


        </script>
        <script type="text/javascript" src="js/jquery/jquery.i18n.properties-1.0.4.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/md5.js" language="javascript"></script>
        <script type="text/javascript" src="js/library/modaldbox.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/ajax_calls.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/utils.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/session_manager.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/xml_helper.js" language="javascript"></script>
        <script type="text/javascript" src="js/base/layout_manager.js" language="javascript"></script>
        <script type="text/javascript" src="js/theme-switcher.js" language="javascript"></script>
        <link rel="stylesheet" type="text/css" href="css/table.css" media="all">
        <link rel="stylesheet" type="text/css" href="css/modaldbox.css" media="all">
        <link type="application/rss+xml" rel='alternate' href='ap-x.rss'/>

        <style type="text/css">
            /* 本地图标样式 - 替代Font Awesome */
            .fa {
                display: inline-block;
                font-style: normal;
                font-variant: normal;
                text-rendering: auto;
                line-height: 1;
            }

            /* 用户图标 - 纯CSS人头轮廓 */
            .fa-user {
                display: inline-block;
                width: 1em;
                height: 1em;
                position: relative;
                vertical-align: middle;
            }

            .fa-user::before {
                content: "";
                position: absolute;
                top: 0.1em;
                left: 50%;
                transform: translateX(-50%);
                width: 0.35em;
                height: 0.35em;
                background: currentColor;
                border-radius: 50%;
            }

            .fa-user::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 0.8em;
                height: 0.5em;
                background: currentColor;
                border-radius: 0.4em 0.4em 0 0;
            }

            /* 加载图标 */
            .fa-spinner::before {
                content: "⟳";
                font-family: Arial, sans-serif;
                font-size: 1em;
            }
            .fa-spin {
                animation: fa-spin 1s infinite linear;
            }
            @keyframes fa-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
                background-color: #ffffff;
            }
            .login-container {
                width: 100%;
                height: 100vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            }
            .logo-container {
                margin-top: 40px;
                text-align: center;
                 transform: scale(2);
            }
            .logo {
                max-width: 150px;
                height: auto;
            }
            .language-selector {
                position: absolute;
                top: 20px;
                right: 20px;
                background-color: rgba(255, 255, 255, 0.8);
                padding: 5px 10px;
                border-radius: 20px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            .language-selector select {
                border: none;
                background: transparent;
                font-size: 14px;
                color: #1976d2;
                outline: none;
                cursor: pointer;
            }
            .login-box {
                background-color: #fff;
                border: none;
                border-radius: 12px;
                box-shadow: 0 8px 30px rgba(25, 118, 210, 0.15);
                width: 100%;
                max-width: 400px;
                margin: 40px auto;
                padding: 40px 30px;
                text-align: center;
                position: relative;
                overflow: hidden;
            }
            .login-box::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 5px;
                background: linear-gradient(to right, #1976d2, #64b5f6);
            }
            .login-box h2 {
                margin-top: 0;
                margin-bottom: 30px;
                color: #1976d2;
                font-size: 24px;
                font-weight: 500;
            }
            .user-avatar {
                width: 80px;
                height: 80px;
                background-color: #bbdefb;
                border-radius: 50%;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 36px;
                color: #1976d2;
                border: 3px solid #e3f2fd;
                position: relative;
            }
            .admin-label {
                font-size: 16px;
                font-weight: 500;
                color: #555;
                margin-bottom: 20px;
                display: block;
            }
            .input-container {
                margin: 20px auto;
                width: 85%;
            }
            .input-field {
                width: 100%;
                padding: 12px 15px;
                margin-bottom: 20px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 15px;
                box-sizing: border-box;
                transition: all 0.3s;
                background-color: #f5f8ff;
            }
            .input-field:focus {
                border-color: #1976d2;
                outline: none;
                box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
                background-color: #fff;
            }
            .login-button {
                background: linear-gradient(to right, #1976d2, #2196f3);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 14px 20px;
                width: 100%;
                font-size: 16px;
                cursor: pointer;
                margin-top: 15px;
                font-weight: 600;
                text-transform: uppercase;
                transition: all 0.3s;
                letter-spacing: 1px;
            }
            .login-button:hover {
                background: linear-gradient(to right, #0d47a1, #1976d2);
                box-shadow: 0 5px 15px rgba(13, 71, 161, 0.3);
                transform: translateY(-2px);
            }
            .login-button:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(13, 71, 161, 0.3);
            }
            .error-message {
                color: #f44336;
                margin-top: 15px;
                display: none;
                font-size: 14px;
                background-color: rgba(244, 67, 54, 0.1);
                padding: 8px;
                border-radius: 4px;
            }
            .footer {
                position: absolute;
                bottom: 20px;
                width: 100%;
                text-align: center;
                font-size: 12px;
                color: #777;
            }
            .version-info, .login-info {
                margin: 3px 0;
            }
            @media screen and (max-width: 480px) {
                .login-box {
                    margin: 30px 15px;
                    padding: 30px 15px;
                }
                .user-avatar {
                    width: 60px;
                    height: 60px;
                    font-size: 28px;
                }
            }
        </style>
        <script type="text/javascript">
            function setLocale(value){
                setCookie("locale", value, 365);
                setLocalization(value);
                
                // 更新语言选择器的值以匹配当前语言
                document.getElementById("languageSelect").value = value;
                
                var locale_lan = (value == 'cn') ? 'cn' : 'en';
                postXMLlocale("locale", "<?xml version=\"1.0\" encoding=\"US-ASCII\"?><RGW><locale><language>"+locale_lan+"</language></locale></RGW>",15000);
                displayControls();
                document.getElementById("password").focus();  
            }
            
            function displayControls(){
                document.getElementById("passwordLabel").innerHTML=jQuery.i18n.prop("laPassword");
                document.getElementById("loginButton").innerHTML = jQuery.i18n.prop("btnSignIn");
                document.getElementById("pageTitle").innerHTML = jQuery.i18n.prop("lTitleUAPXC");
                document.getElementById("adminLabel").innerHTML = jQuery.i18n.prop("laAdminLabel");;
                document.getElementById("password").placeholder = jQuery.i18n.prop("laPasswordPlaceholder");
            }
            
            function login() {
                var username = "admin"; // 固定用户名为admin
                var password = encodeURIComponent(document.getElementById("password").value);
                var login_done;
                
                if(password == "")
                    login_done = 0;
                else
                    login_done = doLogin(username, password);
                    
                if(login_done == 1){
                    showLoading();
                    document.getElementById("divAdminApp").innerHTML = callProductHTML("html/adminApp.html");
                    document.getElementById("divAdminApp").className = "";
                    document.getElementsByTagName("body")[0].className = "";
                    
                    // 立即初始化界面文本
                    var lableWelcome = document.getElementById("lableWelcome");
                    if(lableWelcome) lableWelcome.innerHTML = jQuery.i18n.prop("lableWelcome");
                    var quickSetup = document.getElementById("quickSetup");
                    if(quickSetup) quickSetup.innerHTML = jQuery.i18n.prop("quickSetupName");
                    var mainHelp = document.getElementById("MainHelp");
                    if(mainHelp) mainHelp.innerHTML = jQuery.i18n.prop("helpName");
                    var mainLogOut = document.getElementById("MainLogOut");
                    if(mainLogOut) mainLogOut.innerHTML = jQuery.i18n.prop("LogOutName");
                    var quickSetup2 = document.getElementById("quickSetup2");
                    if(quickSetup2) quickSetup2.innerHTML = jQuery.i18n.prop("quickSetupName");
                    var mainHelp2 = document.getElementById("MainHelp2");
                    if(mainHelp2) mainHelp2.innerHTML = jQuery.i18n.prop("helpName");
                    var mainLogOut2 = document.getElementById("MainLogOut2");
                    if(mainLogOut2) mainLogOut2.innerHTML = jQuery.i18n.prop("LogOutName");
                    
                    initAPP();
                }
                else {
                    document.getElementById('errorMessage').style.display = 'block';
                    if(login_done == 0)
                        document.getElementById("errorMessage").innerHTML = jQuery.i18n.prop("lloginfailed");
                    else if(login_done == -1)
                        document.getElementById("errorMessage").innerHTML = jQuery.i18n.prop("lnoconn");
                }
            }
            
            function checkEnter(e) {
                var characterCode;
                if(e && e.which){
                    e = e
                    characterCode = e.which
                } else {
                    e = event
                    characterCode = e.keyCode
                }
                if(characterCode == 13){
                    // 阻止默认的Enter键行为
                    if(e.preventDefault) e.preventDefault();
                    if(e.stopPropagation) e.stopPropagation();
                    
                    // 只在密码框中按Enter时才登录
                    if(document.activeElement.id === "password") {
                        login();
                    }
                    return false;
                }
            }
            
            function hideError(){
                document.getElementById('errorMessage').style.display = 'none';
            }
            
            var versionString;
            
            function initIndex(){
                if("IE6" == GetBrowserType()) {
                    var fileref=document.createElement('script');
                    fileref.setAttribute("type","text/javascript");
                    fileref.setAttribute("src", 'js/jquery/jquery.bgiframe.min.js');
                    document.getElementsByTagName("head")[0].appendChild(fileref);
                }
                
                var objXML = $().XML_Operations();
                var versionXMLData = getVersionXML("version_num.txt");
                var versionXML = objXML.getXMLDOCVersion(versionXMLData);
                var strVesrion = $(versionXML).find("firmware_version").text();
                strVesrion = strVesrion.substring(strVesrion.indexOf("UAPXC", 0), strVesrion.length);
                strVesrion = strVesrion.replace("_", " ");
                document.getElementById("version").innerHTML = strVesrion;
                versionString = strVesrion;
                
                var Last_loginString = "Last Login Time: ";
                var lastLoginXMLData = getLastLoginXML("last_login.txt");
                var lastlogindateXML = objXML.getXMLDOCVersion(lastLoginXMLData);
                var Last_loginTime = $(lastlogindateXML).find("last_login").text();
                if(Last_loginTime != '')
                    Last_loginString = Last_loginString + Last_loginTime
                else
                    Last_loginString = ""
                document.getElementById("lastLogin").innerHTML = Last_loginString;
                
                document.getElementById("password").focus();
                
                // 获取当前语言设置
                var xml = callProductXML("locale");
                var language = $(xml).find("language").text();
                var currentLocale = (language == "en") ? "en" : "cn";
                
                // 设置cookie和本地化
                setCookie("locale", currentLocale, 365);
                setLocalization(currentLocale);
                
                // 更新语言选择器的值
                document.getElementById("languageSelect").value = currentLocale;
                
                setCookie("platform", "mifi", 365);
                displayControls();
            }
            
            function initAPP(){
                document.title = jQuery.i18n.prop("lTitleUAPXC");
                document.getElementById("lVesrion").innerHTML = versionString;
                initmb();
                
                // 立即创建菜单和初始化界面
                createMenuFromXML();
                createMenu(1);
                
                // 立即显示主界面元素
                var homepic = document.getElementById('homepic');
                var infoRow = document.querySelector('.info-row');
                var dashboardItem = document.getElementById('ant-menu-0');
                
                if (dashboardItem) {
                    var allMenuItems = document.querySelectorAll('.ant-menu-item');
                    allMenuItems.forEach(function(item) {
                        item.classList.remove("ant-menu-item-selected");
                    });
                    dashboardItem.classList.add("ant-menu-item-selected");
                }
                
                if (homepic) {
                    homepic.style.display = "flex";
                }
                
                if (infoRow) {
                    if (window.innerWidth <= 768) {
                        var infoToggleBtn = document.getElementById('infoToggleBtn');
                        if (infoToggleBtn) {
                            infoToggleBtn.style.display = "flex";
                            infoToggleBtn.onclick = function() {
                                toggleInfoDisplay();
                            };
                        }
                        infoRow.style.display = 'none';
                        infoRow.classList.remove('open');
                    } else {
                        infoRow.style.display = "grid";
                    }
                }
                
                hideLoading();
                appInitialized = true;
                
                // 后台异步加载其他JS文件
                loadOtherScripts().catch(function(error) {
                    console.warn('部分JS文件加载失败:', error);
                });
            }
            
            function btnSkipQSClicked() {
                if(document.getElementById("chkSkip").checked) {
                    var mapData = new Array(0);
                    mapData = putMapElement(mapData,"RGW/management/qs_complete","1",0);
                    postXML("qs_complete", g_objXML.getXMLDocToString(g_objXML.createXML(mapData)));
                }
                
                document.getElementById("lableWelcome").innerHTML = jQuery.i18n.prop("lableWelcome");
                document.getElementById("quickSetupSpan").innerHTML = '<a href="#."  onclick="quickSetup()" id="quickSetup" >Quick Setup</a>  |  <a href="#." id="HelpName" onclick="getMainHelp()">Help</a>  |  <a href="#."  id="LogOutName"  onclick="logOut()">Log Out</a>';
                document.getElementById("quickSetup").innerHTML = jQuery.i18n.prop("quickSetupName");
                document.getElementById("HelpName").innerHTML = jQuery.i18n.prop("helpName");
                document.getElementById("LogOutName").innerHTML = jQuery.i18n.prop("LogOutName");
                
                if(document.getElementById("quickSetupSpan2")) {
                    document.getElementById("quickSetup2").innerHTML = jQuery.i18n.prop("quickSetupName");
                    document.getElementById("MainHelp2").innerHTML = jQuery.i18n.prop("helpName");
                    document.getElementById("MainLogOut2").innerHTML = jQuery.i18n.prop("LogOutName");
                }
                
                hm();
                
                // 异步加载应用JS文件后初始化菜单
                loadAppScripts().then(function() {
                    createMenuFromXML();
                    
                    setTimeout(function() {
                        createMenu(1);
                        
                        setTimeout(function() {
                            var homepic = document.getElementById('homepic');
                            var infoRow = document.querySelector('.info-row');
                            var dashboardItem = document.getElementById('ant-menu-0');
                            
                            if (dashboardItem) {
                                var allMenuItems = document.querySelectorAll('.ant-menu-item');
                                allMenuItems.forEach(function(item) {
                                    item.classList.remove("ant-menu-item-selected");
                                });
                                dashboardItem.classList.add("ant-menu-item-selected");
                            }
                            
                            if (homepic) {
                                homepic.style.display = "flex";
                            }
                            
                            if (infoRow) {
                                if (window.innerWidth <= 768) {
                                    var infoToggleBtn = document.getElementById('infoToggleBtn');
                                    if (infoToggleBtn) {
                                        infoToggleBtn.style.display = "flex";
                                    }
                                } else {
                                    infoRow.style.display = "grid";
                                }
                            }
                            
                            
                        }, 300);
                    }, 200);
                }).catch(function(error) {
                });
            }
            
            function toggleInfoDisplay() {
                var infoRow = document.querySelector('.info-row');
                var infoToggleBtn = document.getElementById('infoToggleBtn');
                
                if (!infoRow || !infoToggleBtn) return;
                
                if (infoRow.classList.contains('open')) {
                    infoRow.classList.remove('open');
                    infoRow.style.display = 'none';
                    infoToggleBtn.classList.remove('open');
                } else {
                    infoRow.classList.add('open');
                    infoRow.style.display = 'grid';
                    infoToggleBtn.classList.add('open');
                }
            }


        </script>
    </head>
    <body onload="initIndex(); initTheme();" class="loginBody">
        <div id="divAdminApp" class="login-container">
            <div class="language-selector" style="display: none;">
                <select id="languageSelect" onchange="setLocale(this.value)">
                    <option value="en">English</option>
                    <option value="cn">中文</option>
                </select>
            </div>
            
            <div class="logo-container">
                <!-- <span style="font-size: 48px; color: #1976d2; font-weight: bold;">ZY</span> -->
            </div>
            
            <div class="login-box">
                <h2 id="pageTitle">ZY 无线路由器</h2>
                
                <div class="user-avatar">
                    <svg width="36" height="36" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <span id="adminLabel" class="admin-label"></span>
                
                <div class="input-container">
                    <!-- 隐藏的用户名输入框，值固定为admin -->
                    <input type="hidden" id="username" value="admin">
                    
                    <input type="password" id="password" class="input-field" placeholder="" onkeypress="checkEnter(event)" onchange="hideError()" maxlength="32">
                    <label id="passwordLabel" style="display: none;">密码</label>
                    
                    <div class="button-container">
                        <button id="loginButton" class="login-button" onclick="login()">登录</button>
                    </div>
                    <div id="errorMessage" class="error-message">用户名或密码无效</div>
                </div>
                
                <!-- 加载指示器 -->
                <div id="loadingIndicator" style="display: none; text-align: center; margin-top: 20px; color: #1976d2;">
                    <i class="fa fa-spinner fa-spin" style="font-size: 20px; margin-right: 8px;"></i>
                    正在加载界面...
                </div>
            </div>
            
            <div class="footer" style="display: none;">
                <div id="version" class="version-info"></div>
                <div id="lastLogin" class="login-info"></div>
            </div>
        </div>
    </body>
</html>
