(function() {
    'use strict';
    
    var SESSION_TIMEOUT = 1800000; // 30分钟
    var isPageRefresh = false;
    var sessionCheckInterval = null;
    var isManualLogout = false; // 标记是否为主动登出
    
    function detectPageRefresh() {

        if (window.performance && window.performance.navigation) {
            if (performance.navigation.type === 1) {
                isPageRefresh = true;
                return true;
            }
        }
        
        var sessionData = getSessionData();
        if (sessionData && sessionData.lastActivity) {
            var timeDiff = new Date().getTime() - sessionData.lastActivity;
            if (timeDiff < 5000) { // 5秒内的活动认为是刷新
                isPageRefresh = true;
                return true;
            }
        }
        
        return false;
    }
    
    function saveSessionData() {
        try {
            var sessionData = {
                username: window.username || "",
                passwd: window.passwd || "",
                Authrealm: window.Authrealm || "",
                nonce: window.nonce || "",
                AuthQop: window.AuthQop || "",
                GnCount: window.GnCount || 1,
                sessionStartTime: new Date().getTime(),
                lastActivity: new Date().getTime(),
                isLoggedIn: true
            };
            
            sessionStorage.setItem('authSession', JSON.stringify(sessionData));
            localStorage.setItem('lastLoginTime', new Date().getTime().toString());
            return true;
        } catch (e) {
            return false;
        }
    }
    
    // 获取会话数据
    function getSessionData() {
        try {
            var sessionData = sessionStorage.getItem('authSession');
            if (sessionData) {
                return JSON.parse(sessionData);
            }
        } catch (e) {
        }
        return null;
    }
    
    // 恢复会话数据
    function restoreSessionData() {
        try {
            var sessionData = getSessionData();
            if (sessionData && sessionData.isLoggedIn) {
                window.username = sessionData.username;
                window.passwd = sessionData.passwd;
                window.Authrealm = sessionData.Authrealm;
                window.nonce = sessionData.nonce;
                window.AuthQop = sessionData.AuthQop;
                window.GnCount = sessionData.GnCount;
                
                return true;
            }
        } catch (e) {
        }
        return false;
    }
    
    // 检查会话是否有效
    function isSessionValid() {
        try {
            var sessionData = getSessionData();
            if (!sessionData || !sessionData.isLoggedIn) {
                return false;
            }
            
            var now = new Date().getTime();
            var sessionAge = now - (sessionData.sessionStartTime || 0);
            
            // 检查会话是否超时
            if (sessionAge > SESSION_TIMEOUT) {
                clearSessionData();
                return false;
            }
            
            return true;
        } catch (e) {
            return false;
        }
    }
    
    // 更新会话活动时间
    function updateSessionActivity() {
        try {
            var sessionData = getSessionData();
            if (sessionData) {
                sessionData.lastActivity = new Date().getTime();
                sessionStorage.setItem('authSession', JSON.stringify(sessionData));
            }
        } catch (e) {
        }
    }
    
    // 清除会话数据
    function clearSessionData() {
        try {
            sessionStorage.removeItem('authSession');
            localStorage.removeItem('lastLoginTime');
        } catch (e) {
        }
    }
    
    // 启动会话监控
    function startSessionMonitoring() {
        // 每分钟检查一次会话状态
        sessionCheckInterval = setInterval(function() {
            if (!isSessionValid()) {
                stopSessionMonitoring();
                // 只有在非刷新状态下才自动登出
                if (!isPageRefresh && window.clearAuthheader) {
                    window.clearAuthheader();
                }
            }
        }, 60000);
    }
    
    // 停止会话监控
    function stopSessionMonitoring() {
        if (sessionCheckInterval) {
            clearInterval(sessionCheckInterval);
            sessionCheckInterval = null;
        }
    }
    
    // 验证服务器端会话是否有效
    function validateServerSession() {
        return new Promise(function(resolve, reject) {
            try {
                var xhr = new XMLHttpRequest();
                var host = window.location.protocol + "//" + window.location.host + "/";
                var url = host + 'xml_action.cgi?method=get&module=duster&file=status1';
                
                xhr.open('GET', url, true);
                xhr.timeout = 10000; // 10秒超时
                
                if (typeof getAuthHeader === 'function') {
                    xhr.setRequestHeader("Authorization", getAuthHeader("GET"));
                }
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                var responseText = xhr.responseText;
                                if (responseText.indexOf('UNAUTHORIZED') > -1 || 
                                    responseText.indexOf('login_status') > -1 && responseText.indexOf('UNAUTHORIZED') > -1) {
                                    resolve(false);
                                } else {
                                    resolve(true);
                                }
                            } catch (e) {
                                resolve(false);
                            }
                        } else {
                            resolve(false);
                        }
                    }
                };
                
                xhr.ontimeout = function() {
                    resolve(false);
                };
                
                xhr.onerror = function() {
                    resolve(false);
                };
                
                xhr.send();
            } catch (e) {
                resolve(false);
            }
        });
    }
    
    // 处理页面刷新的登录恢复
    function handlePageRefreshLogin() {
        if (detectPageRefresh() && isSessionValid()) {
            
            // 验证服务器端会话是否有效
            validateServerSession().then(function(isServerSessionValid) {
                if (!isServerSessionValid) {
                    clearSessionData();
                    window.location = "index.html";
                    return;
                }
                
                // 服务器端会话有效，继续恢复本地会话
                if (restoreSessionData()) {
                    // 模拟登录成功的状态
                    updateSessionActivity();
                    startSessionMonitoring();
                    
                    // 如果存在主应用容器，直接显示主界面
                    var adminApp = document.getElementById("divAdminApp");
                    if (adminApp && window.callProductHTML && window.initAPP) {
                        try {
                            adminApp.innerHTML = window.callProductHTML("html/adminApp.html");
                            adminApp.className = "";
                            document.getElementsByTagName("body")[0].className = "";
                            
                            // 重新加载i18n资源并设置界面文本
                            reloadI18nAndSetText();
                            
                            window.initAPP();
                        } catch (e) {
                            // 如果恢复失败，清除会话并跳转到登录页
                            clearSessionData();
                            window.location = "index.html";
                        }
                    }
                } else {
                    clearSessionData();
                    window.location = "index.html";
                }
            }).catch(function(error) {
                clearSessionData();
                window.location = "index.html";
            });
            
            return true; // 返回true表示正在处理中
        }
        return false;
    }
    
    // 重写原始的clearAuthheader函数
    function enhancedClearAuthheader() {
        // 检查是否是页面刷新导致的清除，但如果是主动登出则忽略这个检查
        if (!isManualLogout && isPageRefresh && isSessionValid()) {
            return;
        }
        
        
        // 调用原始的clearAuthheader函数，避免重复逻辑
        if (window.originalClearAuthheader) {
            window.originalClearAuthheader();
        } else {
            // 如果原始函数不存在，执行基本的清除逻辑
            window.username = "";
            window.passwd = "";
            window.AuthQop = "";
            window.Authrealm = "";
            window.GnCount = 1;
            window.nonce = "";
            
            // 清除会话数据
            clearSessionData();
            stopSessionMonitoring();
            
            // 跳转到登录页面
            window.location = "index.html";
        }
        
        // 重置标志
        isManualLogout = false;
        isPageRefresh = false;
    }
    
    // 重写原始的doLogin函数
    function enhancedDoLogin(originalDoLogin) {
        return function(username1, passwd1) {
            var result = originalDoLogin.call(this, username1, passwd1);
            
            if (result === 1) {
                // 登录成功，保存会话数据
                saveSessionData();
                startSessionMonitoring();
            }
            
            return result;
        };
    }
    
    // 监听用户活动
    function setupActivityListeners() {
        var events = ['click', 'keypress', 'scroll', 'mousemove'];
        events.forEach(function(event) {
            document.addEventListener(event, function() {
                updateSessionActivity();
            }, true);
        });
    }
    
    // 初始化会话管理器
    function initSessionManager() {
        
        // 设置活动监听器
        setupActivityListeners();
        
        // 检查是否是页面刷新
        if (handlePageRefreshLogin()) {
            return; // 成功恢复登录状态，不需要进一步处理
        }
        
        // 重写关键函数（但不覆盖utils.js中的逻辑）
        if (window.clearAuthheader && !window.originalClearAuthheader) {
            window.originalClearAuthheader = window.clearAuthheader;
            // 不覆盖clearAuthheader，让utils.js中的逻辑优先
            // window.clearAuthheader = enhancedClearAuthheader;
        }
        
        if (window.doLogin) {
            window.originalDoLogin = window.doLogin;
            window.doLogin = enhancedDoLogin(window.originalDoLogin);
        }
        
        // 暴露必要的函数到全局作用域
        window.isPageRefresh = isPageRefresh;
        window.isSessionValid = isSessionValid;
        window.updateSessionActivity = updateSessionActivity;
        window.saveSessionData = saveSessionData;
        window.clearSessionData = clearSessionData;
        window.setManualLogout = function() {
            isManualLogout = true;
        };
    }
    
    // 重新加载i18n资源的函数
    function reloadI18nAndSetText() {
        if (window.setLocalization && window.getCookie) {
            var locale = window.getCookie('locale') || 'cn';
            window.setLocalization(locale);
            
            setTimeout(function() {
                var elements = {
                    "lableWelcome": "lableWelcome",
                    "quickSetup": "quickSetupName", 
                    "MainHelp": "helpName",
                    "MainLogOut": "LogOutName",
                    "quickSetup2": "quickSetupName",
                    "MainHelp2": "helpName", 
                    "MainLogOut2": "LogOutName"
                };
                
                for (var id in elements) {
                    var element = document.getElementById(id);
                    if (element && window.jQuery && window.jQuery.i18n) {
                        element.innerHTML = window.jQuery.i18n.prop(elements[id]);
                    }
                }
            }, 200);
        }
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSessionManager);
    } else {
        initSessionManager();
    }
    
    // 页面卸载前保存会话状态
    window.addEventListener('beforeunload', function() {
        if (window.username && window.passwd) {
            updateSessionActivity();
        }
    });
    
})();