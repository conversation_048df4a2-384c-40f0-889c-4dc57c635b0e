<div class="content">
  <div class="form-section">
    <div class="form-group">
      <div class="header-controls">
        <div class="title-section">
          <a href='#' class='help' onclick="getHelp('ConnectedDevices')">&nbsp;</a>
          <label id="title" class="title"></label>
        </div>
        <div class="view-toggle mobile-only" style="display: none;">
          <button id="cardViewBtn" class="view-btn active" onclick="switchToCardView()">卡片</button>
          <button id="tableViewBtn" class="view-btn" onclick="switchToTableView()">表格</button>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div class="desktop-view">
        <div class="table-responsive">
          <table class="example dataTbl10 table-autosort:0 table-stripeclass:alternate" id="tableConnectedDevice">
            <thead>
              <tr>
                <th style="display: none">index</th>
                <th class="name-col" id="ltName"></th>
                <th class="status-col" id="ltDeviceStatus"></th>
                <th class="ip-col" id="ltIpAddress"> </th>
                <th class="mac-col" id="ltMac"> </th>
                <th class="connection-col" id="ltConnection"></th>
                <th class="time-col" id="ltTime"></th>
              </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 移动端卡片视图 -->
      <div class="mobile-view" style="display: none;">
        <div id="mobileDeviceList" class="device-cards-container">
          <!-- 设备卡片将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* 移动端设备卡片样式 */
.device-cards-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 16px 0;
}

.device-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s ease;
}

.device-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.device-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.device-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  flex: 1;
  margin-right: 12px;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
}

.device-status-dot.online {
  background-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.device-status-dot.offline {
  background-color: #d9d9d9;
  box-shadow: 0 0 0 2px rgba(217, 217, 217, 0.2);
}

/* 在表格中的状态点样式 */
table .device-status-dot {
  margin: 0 auto;
  display: block;
}

/* 在卡片中的状态点样式 */
.device-status .device-status-dot {
  margin-right: 0;
}

.device-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 12px;
}

.device-info-item {
  display: flex;
  flex-direction: column;
}

.device-info-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
  font-weight: 500;
}

.device-info-value {
  font-size: 14px;
  color: #262626;
  word-break: break-all;
}

.device-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  display: none; /* 隐藏操作按钮区域 */
  gap: 8px;
}

.device-action-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  color: #262626;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.device-action-btn.primary {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.device-action-btn.primary:hover {
  background: #40a9ff;
}

/* 响应式显示控制 */
@media (max-width: 768px) {
  /* 在移动端优先显示卡片视图 */
  .desktop-view {
    display: none !important;
  }

  .mobile-view {
    display: block !important;
  }

  /* 如果需要在移动端显示表格，确保有横向滚动 */
  .desktop-view.force-show {
    display: block !important;
  }

  .desktop-view.force-show .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  .device-info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .device-card {
    margin: 0 -8px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .device-card:first-child {
    border-top: none;
  }

  .device-card:last-child {
    border-bottom: none;
  }

  /* 移动端标题优化 */
  .title {
    font-size: 18px !important;
    margin-bottom: 0 !important;
  }

  /* 隐藏视图切换按钮 */
  .mobile-only {
    display: none !important;
  }

  .header-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .view-toggle {
    align-self: flex-end;
  }

  /* 移动端内容区域优化 */
  .content {
    padding: 0 !important;
  }

  .form-section {
    padding: 16px !important;
  }

  /* 触摸优化 */
  .device-action-btn {
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .device-card {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* 改善可读性 */
  .device-info-value {
    font-size: 15px;
    line-height: 1.4;
  }

  .device-name {
    font-size: 17px;
    line-height: 1.3;
  }
}

@media (min-width: 769px) {
  .desktop-view {
    display: block !important;
  }

  .mobile-view {
    display: none !important;
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 16px;
}

/* 头部控制区域 */
.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-toggle {
  display: none;
  gap: 4px;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 4px;
}

.view-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: #1890ff;
  color: white;
}

.view-btn:hover:not(.active) {
  background: #e6f7ff;
  color: #1890ff;
}

.mobile-only {
  display: none;
}
</style>

<div id="box" class="dialog device-modal" style="display: none">
  <div class="modal-content">
    <div class="modal-header">
      <h1 id="h1DeviceHeader"></h1>
      <a href="#" onclick="closeModal()" class="modal-close">×</a>
    </div>
    <div class="modal-body">
      <label id="lModalHeader" class="modal-label"></label>
      <input type='text' id="tbModal" maxlength="16" value="" class="modal-input"/>
      <label class='error' id='ErrInvalidName' style='display: none'></label>
    </div>
    <div class="modal-footer">
      <button onclick="handleOkClick()" id="btnModalOk" class="modal-btn modal-btn-primary"></button>
      <button onclick="handleResetClick()" id="btnModalReset" class="modal-btn modal-btn-secondary"></button>
    </div>
  </div>
</div>

<style>
/* 移动端模态框优化 - 只在显示时生效 */
.device-modal.show {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 1000 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.modal-header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.modal-close {
  font-size: 24px;
  color: #8c8c8c;
  text-decoration: none;
  line-height: 1;
  padding: 4px;
}

.modal-close:hover {
  color: #262626;
}

.modal-body {
  padding: 20px;
}

.modal-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.modal-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
  margin-bottom: 12px;
}

.modal-input:focus {
  border-color: #1890ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.modal-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.modal-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.modal-btn-secondary {
  background: #fff;
  color: #262626;
}

.modal-btn-secondary:hover {
  border-color: #1890ff;
  color: #1890ff;
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .modal-btn:last-child {
    margin-bottom: 0;
  }
}
</style>

<script>
// 视图切换函数
function switchToCardView() {
  var desktopView = document.querySelector('.desktop-view');
  var mobileView = document.querySelector('.mobile-view');
  var cardBtn = document.getElementById('cardViewBtn');
  var tableBtn = document.getElementById('tableViewBtn');

  if (desktopView && mobileView) {
    desktopView.style.display = 'none';
    desktopView.classList.remove('force-show');
    mobileView.style.display = 'block';

    cardBtn.classList.add('active');
    tableBtn.classList.remove('active');
  }
}

function switchToTableView() {
  var desktopView = document.querySelector('.desktop-view');
  var mobileView = document.querySelector('.mobile-view');
  var cardBtn = document.getElementById('cardViewBtn');
  var tableBtn = document.getElementById('tableViewBtn');

  if (desktopView && mobileView) {
    desktopView.style.display = 'block';
    desktopView.classList.add('force-show');
    mobileView.style.display = 'none';

    cardBtn.classList.remove('active');
    tableBtn.classList.add('active');
  }
}

function closeModal() {
  var modal = document.getElementById('box');
  if (modal) {
    modal.classList.remove('show');
  }
  hm('box');
  btnCancelClicked();
}

function handleOkClick() {
  var modal = document.getElementById('box');
  if (modal) {
    modal.classList.remove('show');
  }
  btnOkSelected();
}

function handleResetClick() {
  var modal = document.getElementById('box');
  if (modal) {
    modal.classList.remove('show');
  }
  hm('box');
  btnRemoveSelected();
}
</script>


